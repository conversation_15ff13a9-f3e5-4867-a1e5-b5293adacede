package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import coil3.ImageLoader
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 图片加载框架依赖注入模块
 * 
 * 功能：
 * - 提供ImageManager的依赖注入
 * - 提供ImageLoader的依赖注入
 * - 管理图片加载框架的生命周期
 * 
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 延迟初始化避免循环依赖
 * - 兼容现有代码逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object ImageModule {
    
    /**
     * 提供ImageManager实例
     */
    @Provides
    @Singleton
    fun provideImageManager(
        @ApplicationContext context: Context
    ): ImageManager {
        return ImageManagerProvider.getInstance(context)
    }
    
    /**
     * 提供ImageLoader实例
     */
    @Provides
    @Singleton
    fun provideImageLoader(
        @ApplicationContext context: Context
    ): ImageLoader {
        return ImageLoaderProvider.getInstance(context)
    }
    
    /**
     * ImageManager提供器
     */
    private object ImageManagerProvider {
        @Volatile
        private var instance: ImageManager? = null
        
        fun getInstance(context: Context): ImageManager {
            return instance ?: synchronized(this) {
                instance ?: createImageManager(context).also { instance = it }
            }
        }
        
        private fun createImageManager(context: Context): ImageManager {
            // 检查ImageManager是否已经通过手动方式初始化
            return if (ImageManager.isInitialized()) {
                // 如果已经初始化，直接返回现有实例
                ImageManager
            } else {
                // 如果未初始化，进行初始化
                ImageManager.apply {
                    init(context)
                }
            }
        }
    }
    
    /**
     * ImageLoader提供器
     */
    private object ImageLoaderProvider {
        @Volatile
        private var instance: ImageLoader? = null
        
        fun getInstance(context: Context): ImageLoader {
            return instance ?: synchronized(this) {
                instance ?: createImageLoader(context).also { instance = it }
            }
        }
        
        private fun createImageLoader(context: Context): ImageLoader {
            // 确保ImageManager已初始化
            ImageManagerProvider.getInstance(context)
            
            // 获取ImageLoader实例
            return ImageManager.getImageLoader(context)
        }
    }
}
