package com.yjsoft.roadtravel.basiclibrary.permission.config

import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionRequestConfig


/**
 * 权限框架全局配置
 */
object PermissionConfig {
    
    /**
     * 默认权限请求配置
     */
    var defaultRequestConfig: PermissionRequestConfig = PermissionRequestConfig(
        showRationaleDialog = true,
        rationaleTitle = "权限申请",
        rationaleMessage = "应用需要此权限才能正常工作",
        showSettingsDialog = true,
        settingsTitle = "权限设置",
        settingsMessage = "请在设置中手动开启权限",
        autoNavigateToSettings = false
    )
    
    /**
     * 是否启用权限日志
     */
    var enableLogging: Boolean = true
    
    /**
     * 权限请求超时时间（毫秒）
     */
    var requestTimeoutMillis: Long = 30_000L // 30秒
    
    /**
     * 是否在权限被永久拒绝时自动显示设置引导
     */
    var autoShowSettingsOnPermanentDenial: Boolean = true
    
    /**
     * 权限说明对话框主题配置
     */
    data class DialogTheme(
        val backgroundColor: String = "#FFFFFF",
        val textColor: String = "#333333",
        val buttonColor: String = "#007AFF",
        val buttonTextColor: String = "#FFFFFF"
    )
    
    /**
     * 对话框主题
     */
    var dialogTheme: DialogTheme = DialogTheme()
    
    /**
     * 权限组配置映射
     */
    private val permissionGroupConfigs = mutableMapOf<String, PermissionRequestConfig>()
    
    /**
     * 设置特定权限组的配置
     */
    fun setPermissionGroupConfig(groupName: String, config: PermissionRequestConfig) {
        permissionGroupConfigs[groupName] = config
    }
    
    /**
     * 获取权限组配置
     */
    fun getPermissionGroupConfig(groupName: String): PermissionRequestConfig? {
        return permissionGroupConfigs[groupName]
    }
    
    /**
     * 权限优先级配置
     * 数值越小优先级越高
     */
    enum class PermissionPriority(val value: Int) {
        CRITICAL(1),    // 关键权限（如位置、相机）
        IMPORTANT(2),   // 重要权限（如存储）
        NORMAL(3),      // 普通权限（如通知）
        OPTIONAL(4)     // 可选权限
    }
    
    /**
     * 权限优先级映射
     */
    private val permissionPriorities = mutableMapOf<String, PermissionPriority>()
    
    /**
     * 设置权限优先级
     */
    fun setPermissionPriority(permission: String, priority: PermissionPriority) {
        permissionPriorities[permission] = priority
    }
    
    /**
     * 获取权限优先级
     */
    fun getPermissionPriority(permission: String): PermissionPriority {
        return permissionPriorities[permission] ?: PermissionPriority.NORMAL
    }
    
    /**
     * 权限申请策略
     */
    enum class RequestStrategy {
        /** 一次性申请所有权限 */
        ALL_AT_ONCE,
        /** 按优先级逐个申请 */
        BY_PRIORITY,
        /** 按需申请（用户触发时才申请） */
        ON_DEMAND
    }
    
    /**
     * 默认权限申请策略
     */
    var defaultRequestStrategy: RequestStrategy = RequestStrategy.BY_PRIORITY
    
    /**
     * 权限拒绝处理策略
     */
    enum class DenialHandlingStrategy {
        /** 显示说明对话框 */
        SHOW_RATIONALE,
        /** 直接跳转设置 */
        NAVIGATE_TO_SETTINGS,
        /** 静默处理 */
        SILENT,
        /** 自定义处理 */
        CUSTOM
    }
    
    /**
     * 默认权限拒绝处理策略
     */
    var defaultDenialHandlingStrategy: DenialHandlingStrategy = DenialHandlingStrategy.SHOW_RATIONALE
    
    /**
     * 初始化默认配置
     */
    fun initializeDefaults() {
        // 设置位置权限的默认配置
        setPermissionGroupConfig(
            PermissionGroups.LOCATION_GROUP,
            PermissionRequestConfig(
                showRationaleDialog = true,
                rationaleTitle = "位置权限申请",
                rationaleMessage = "应用需要获取您的位置信息来提供导航和定位服务",
                showSettingsDialog = true,
                settingsTitle = "开启位置权限",
                settingsMessage = "请在设置中开启位置权限，以便应用为您提供更好的服务",
                autoNavigateToSettings = false
            )
        )
        
        // 设置存储权限的默认配置
        setPermissionGroupConfig(
            PermissionGroups.STORAGE_GROUP,
            PermissionRequestConfig(
                showRationaleDialog = true,
                rationaleTitle = "存储权限申请",
                rationaleMessage = "应用需要访问存储空间来保存和读取文件",
                showSettingsDialog = true,
                settingsTitle = "开启存储权限",
                settingsMessage = "请在设置中开启存储权限",
                autoNavigateToSettings = false
            )
        )
        
        // 设置相机权限的默认配置
        setPermissionGroupConfig(
            PermissionGroups.CAMERA_GROUP,
            PermissionRequestConfig(
                showRationaleDialog = true,
                rationaleTitle = "相机权限申请",
                rationaleMessage = "应用需要使用相机来拍照或录制视频",
                showSettingsDialog = true,
                settingsTitle = "开启相机权限",
                settingsMessage = "请在设置中开启相机权限",
                autoNavigateToSettings = false
            )
        )
        
        // 设置权限优先级
        setPermissionPriority(android.Manifest.permission.ACCESS_FINE_LOCATION, PermissionPriority.CRITICAL)
        setPermissionPriority(android.Manifest.permission.ACCESS_COARSE_LOCATION, PermissionPriority.CRITICAL)
        setPermissionPriority(android.Manifest.permission.CAMERA, PermissionPriority.IMPORTANT)
        setPermissionPriority(android.Manifest.permission.WRITE_EXTERNAL_STORAGE, PermissionPriority.IMPORTANT)
        setPermissionPriority(android.Manifest.permission.READ_EXTERNAL_STORAGE, PermissionPriority.IMPORTANT)
    }
    
    /**
     * 重置为默认配置
     */
    fun resetToDefaults() {
        defaultRequestConfig = PermissionRequestConfig()
        enableLogging = true
        requestTimeoutMillis = 30_000L
        autoShowSettingsOnPermanentDenial = true
        dialogTheme = DialogTheme()
        defaultRequestStrategy = RequestStrategy.BY_PRIORITY
        defaultDenialHandlingStrategy = DenialHandlingStrategy.SHOW_RATIONALE
        permissionGroupConfigs.clear()
        permissionPriorities.clear()
        initializeDefaults()
    }
}
