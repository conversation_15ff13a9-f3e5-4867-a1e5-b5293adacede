package com.yjsoft.roadtravel.basiclibrary.payment.core

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConfig
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.util.concurrent.ConcurrentHashMap

/**
 * 支付管理器
 * 统一管理所有支付方式，提供统一的支付接口
 */
object PaymentManager {
    
    private const val TAG = "PaymentManager %s"
    
    // 是否已初始化
    private var isInitialized = false
    
    // 支付策略映射表
    private val paymentStrategies = ConcurrentHashMap<PaymentType, PaymentStrategy>()
    
    // 当前支付状态
    private val _paymentState = MutableStateFlow(
        com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.idle()
    )
    val paymentState: StateFlow<com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState> = _paymentState.asStateFlow()
    
    /**
     * 初始化支付管理器
     * @param context 应用上下文
     * @param debugMode 是否为调试模式
     */
    fun init(context: Context, debugMode: Boolean = false) {
        if (isInitialized) {
            LogManager.w(TAG, "PaymentManager已经初始化，跳过重复初始化")
            return
        }
        
        try {
            // 初始化支付配置
            PaymentConfig.init(context, debugMode)
            
            // 注册支付策略工厂
            PaymentFactory.init()
            
            isInitialized = true
            LogManager.i(TAG, "支付管理器初始化完成 - 调试模式: $debugMode")
            
        } catch (e: Exception) {
            LogManager.e(TAG, "支付管理器初始化失败", e)
            throw e
        }
    }
    
    /**
     * 注册支付策略
     * @param strategy 支付策略
     */
    fun registerStrategy(strategy: PaymentStrategy) {
        val paymentType = strategy.getPaymentType()
        paymentStrategies[paymentType] = strategy
        LogManager.d(TAG, "注册支付策略: ${paymentType.name}")
    }
    
    /**
     * 获取支付策略
     * @param type 支付类型
     * @return 支付策略
     */
    fun getStrategy(type: PaymentType): PaymentStrategy? {
        return paymentStrategies[type] ?: createStrategy(type)
    }
    
    /**
     * 创建支付策略
     * @param type 支付类型
     * @return 支付策略
     */
    private fun createStrategy(type: PaymentType): PaymentStrategy? {
        return try {
            val strategy = PaymentFactory.createStrategy(type)
            if (strategy != null) {
                paymentStrategies[type] = strategy
            }
            strategy
        } catch (e: Exception) {
            LogManager.e(TAG, "创建支付策略失败: ${type.name}", e)
            null
        }
    }
    
    /**
     * 执行支付
     * @param context 上下文
     * @param type 支付类型
     * @param request 支付请求
     * @return 支付结果
     */
    suspend fun pay(
        context: Context,
        type: PaymentType,
        request: PaymentRequest
    ): PaymentResult = withContext(Dispatchers.IO) {
        checkInitialized()
        
        // 更新支付状态为准备中
        updatePaymentState(
            com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.loading(
                paymentType = type,
                orderId = request.orderId,
                message = "准备支付..."
            )
        )
        
        // 获取支付策略
        val strategy = getStrategy(type) ?: return@withContext PaymentResult.Error(
            orderId = request.orderId,
            paymentType = type,
            errorCode = "STRATEGY_NOT_FOUND",
            errorMessage = "不支持的支付方式: ${type.name}"
        )
        
        // 检查支付方式是否可用
        if (!strategy.isAvailable(context)) {
            val errorResult = PaymentResult.Error(
                orderId = request.orderId,
                paymentType = type,
                errorCode = "PAYMENT_UNAVAILABLE",
                errorMessage = "支付方式不可用: ${type.name}"
            )
            updatePaymentState(com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.error(errorResult))
            return@withContext errorResult
        }
        
        // 验证支付请求
        if (!request.isValid()) {
            val errorResult = PaymentResult.Error(
                orderId = request.orderId,
                paymentType = type,
                errorCode = "INVALID_REQUEST",
                errorMessage = "支付请求参数无效"
            )
            updatePaymentState(com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.error(errorResult))
            return@withContext errorResult
        }
        
        // 更新支付状态为支付中
        updatePaymentState(
            com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.paying(
                paymentType = type,
                orderId = request.orderId,
                amount = request.getAmountString()
            )
        )
        
        // 执行支付
        val result = try {
            withTimeout(PaymentConfig.paymentTimeoutMs) {
                strategy.pay(context, request)
            }
        } catch (e: TimeoutCancellationException) {
            PaymentResult.Error(
                orderId = request.orderId,
                paymentType = type,
                errorCode = "PAYMENT_TIMEOUT",
                errorMessage = "支付超时",
                cause = e
            )
        } catch (e: CancellationException) {
            PaymentResult.Cancel(
                orderId = request.orderId,
                paymentType = type,
                reason = "支付已取消"
            )
        } catch (e: Exception) {
            PaymentResult.Error(
                orderId = request.orderId,
                paymentType = type,
                errorCode = "PAYMENT_ERROR",
                errorMessage = e.message ?: "支付过程中发生错误",
                cause = e
            )
        }
        
        // 更新支付状态
        updatePaymentStateByResult(result)
        
        return@withContext result
    }
    
    /**
     * 获取可用的支付方式
     * @param context 上下文
     * @return 可用的支付方式列表
     */
    suspend fun getAvailablePaymentTypes(context: Context): List<PaymentType> = withContext(Dispatchers.IO) {
        checkInitialized()
        
        val availableTypes = mutableListOf<PaymentType>()
        
        for (type in PaymentType.values()) {
            if (PaymentConfig.isPaymentTypeEnabled(type)) {
                val strategy = getStrategy(type)
                if (strategy != null && strategy.isAvailable(context)) {
                    availableTypes.add(type)
                }
            }
        }
        
        return@withContext availableTypes
    }
    
    /**
     * 检查支付方式是否可用
     * @param context 上下文
     * @param type 支付类型
     * @return 是否可用
     */
    suspend fun isPaymentTypeAvailable(context: Context, type: PaymentType): Boolean = withContext(Dispatchers.IO) {
        checkInitialized()
        
        if (!PaymentConfig.isPaymentTypeEnabled(type)) {
            return@withContext false
        }
        
        val strategy = getStrategy(type)
        return@withContext strategy?.isAvailable(context) ?: false
    }
    
    /**
     * 重置支付状态
     */
    fun resetPaymentState() {
        _paymentState.value = com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.idle()
    }
    
    /**
     * 更新支付状态
     * @param state 新状态
     */
    private fun updatePaymentState(state: com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState) {
        _paymentState.value = state
    }
    
    /**
     * 根据支付结果更新状态
     * @param result 支付结果
     */
    private fun updatePaymentStateByResult(result: PaymentResult) {
        when (result) {
            is PaymentResult.Success -> {
                updatePaymentState(com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.success(result))
            }
            is PaymentResult.Cancel -> {
                updatePaymentState(com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.cancelled(result))
            }
            is PaymentResult.Error -> {
                updatePaymentState(com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.error(result))
            }
            is PaymentResult.Processing -> {
                updatePaymentState(com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState.processing(result))
            }
        }
    }
    
    /**
     * 检查是否已初始化
     */
    private fun checkInitialized() {
        if (!isInitialized) {
            throw IllegalStateException("PaymentManager未初始化，请先调用init()方法")
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        paymentStrategies.values.forEach { it.cleanup() }
        paymentStrategies.clear()
        resetPaymentState()
        isInitialized = false
        LogManager.d(TAG, "支付管理器资源已清理")
    }
}
