package com.yjsoft.roadtravel.basiclibrary.payment.state

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentStateListener
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentStatus
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 支付状态管理器
 * 负责管理全局的支付状态和状态变化监听
 */
object PaymentStateManager {
    
    private const val TAG = "PaymentStateManager %s"
    
    // 当前支付状态
    private val _currentState = MutableStateFlow(PaymentState.idle())
    val currentState: StateFlow<PaymentState> = _currentState.asStateFlow()
    
    // 状态历史记录
    private val stateHistory = mutableListOf<PaymentState>()
    private val maxHistorySize = 50
    
    // 状态监听器列表
    private val stateListeners = CopyOnWriteArrayList<PaymentStateListener>()
    
    /**
     * 更新支付状态
     * @param newState 新的支付状态
     */
    fun updateState(newState: PaymentState) {
        val oldState = _currentState.value
        
        // 检查状态是否真的发生了变化
        if (oldState != newState) {
            _currentState.value = newState
            
            // 添加到历史记录
            addToHistory(newState)
            
            // 通知所有监听器
            notifyStateListeners(oldState, newState)
            
            LogManager.d(TAG, "支付状态变化: ${oldState.status} -> ${newState.status}")
        }
    }
    
    /**
     * 获取当前支付状态
     */
    fun getCurrentState(): PaymentState = _currentState.value
    
    /**
     * 检查是否处于支付中状态
     */
    fun isPaymentInProgress(): Boolean {
        val status = _currentState.value.status
        return status in listOf(
            PaymentStatus.INITIALIZING,
            PaymentStatus.PREPARING,
            PaymentStatus.PAYING,
            PaymentStatus.PROCESSING
        )
    }
    
    /**
     * 检查是否可以开始新的支付
     */
    fun canStartNewPayment(): Boolean {
        val status = _currentState.value.status
        return status in listOf(
            PaymentStatus.IDLE,
            PaymentStatus.SUCCESS,
            PaymentStatus.CANCELLED,
            PaymentStatus.FAILED
        )
    }
    
    /**
     * 检查是否可以重试支付
     */
    fun canRetryPayment(): Boolean {
        return _currentState.value.canRetry
    }
    
    /**
     * 重置支付状态为空闲
     */
    fun resetToIdle() {
        updateState(PaymentState.idle())
        LogManager.d(TAG, "支付状态已重置为空闲")
    }
    
    /**
     * 添加状态监听器
     * @param listener 状态监听器
     */
    fun addStateListener(listener: PaymentStateListener) {
        if (!stateListeners.contains(listener)) {
            stateListeners.add(listener)
            LogManager.d(TAG, "添加状态监听器，当前监听器数量: ${stateListeners.size}")
        }
    }
    
    /**
     * 移除状态监听器
     * @param listener 状态监听器
     */
    fun removeStateListener(listener: PaymentStateListener) {
        if (stateListeners.remove(listener)) {
            LogManager.d(TAG, "移除状态监听器，当前监听器数量: ${stateListeners.size}")
        }
    }
    
    /**
     * 清除所有状态监听器
     */
    fun clearStateListeners() {
        val count = stateListeners.size
        stateListeners.clear()
        LogManager.d(TAG, "清除所有状态监听器，共清除: $count 个")
    }
    
    /**
     * 获取状态历史记录
     * @param limit 返回的历史记录数量限制
     */
    fun getStateHistory(limit: Int = 10): List<PaymentState> {
        return stateHistory.takeLast(limit)
    }
    
    /**
     * 清除状态历史记录
     */
    fun clearStateHistory() {
        stateHistory.clear()
        LogManager.d(TAG, "状态历史记录已清除")
    }
    
    /**
     * 获取状态统计信息
     */
    fun getStateStatistics(): PaymentStateStatistics {
        val history = stateHistory
        val totalStates = history.size
        val statusCounts = mutableMapOf<PaymentStatus, Int>()
        
        history.forEach { state ->
            statusCounts[state.status] = statusCounts.getOrDefault(state.status, 0) + 1
        }
        
        return PaymentStateStatistics(
            totalStateChanges = totalStates,
            statusCounts = statusCounts,
            currentStatus = _currentState.value.status,
            lastUpdateTime = System.currentTimeMillis()
        )
    }
    
    /**
     * 添加状态到历史记录
     */
    private fun addToHistory(state: PaymentState) {
        stateHistory.add(state)
        
        // 限制历史记录大小
        if (stateHistory.size > maxHistorySize) {
            stateHistory.removeAt(0)
        }
    }
    
    /**
     * 通知所有状态监听器
     */
    private fun notifyStateListeners(oldState: PaymentState, newState: PaymentState) {
        stateListeners.forEach { listener ->
            try {
                listener.onStateChanged(oldState, newState)
            } catch (e: Exception) {
                LogManager.e(TAG, "状态监听器回调异常", e)
            }
        }
    }
    
    /**
     * 创建状态快照
     */
    fun createStateSnapshot(): PaymentStateSnapshot {
        return PaymentStateSnapshot(
            currentState = _currentState.value,
            stateHistory = stateHistory.toList(),
            listenerCount = stateListeners.size,
            timestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * 恢复状态快照
     */
    fun restoreStateSnapshot(snapshot: PaymentStateSnapshot) {
        _currentState.value = snapshot.currentState
        stateHistory.clear()
        stateHistory.addAll(snapshot.stateHistory)
        LogManager.d(TAG, "状态快照已恢复")
    }
}

/**
 * 支付状态统计信息
 */
data class PaymentStateStatistics(
    val totalStateChanges: Int,
    val statusCounts: Map<PaymentStatus, Int>,
    val currentStatus: PaymentStatus,
    val lastUpdateTime: Long
)

/**
 * 支付状态快照
 */
data class PaymentStateSnapshot(
    val currentState: PaymentState,
    val stateHistory: List<PaymentState>,
    val listenerCount: Int,
    val timestamp: Long
)
