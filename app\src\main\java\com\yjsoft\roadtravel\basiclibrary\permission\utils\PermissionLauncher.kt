package com.yjsoft.roadtravel.basiclibrary.permission.utils

import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionRequest

/**
 * 权限启动器封装
 * 简化权限申请的ActivityResultLauncher使用
 */
class PermissionLauncher(
    private val activity: ComponentActivity
) : DefaultLifecycleObserver {
    
    companion object {
        private const val TAG = "PermissionLauncher"
    }
    
    // 权限启动器
    private var permissionLauncher: ActivityResultLauncher<Array<String>>? = null
    
    // 当前等待结果的请求
    private var pendingRequest: PermissionRequest? = null
    private var pendingCallback: ((Map<String, Boolean>) -> Unit)? = null
    
    init {
        // 注册生命周期观察者
        activity.lifecycle.addObserver(this)
        
        // 创建权限启动器
        permissionLauncher = activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            handlePermissionResult(permissions)
        }
    }
    
    /**
     * 启动权限请求
     */
    fun launch(
        request: PermissionRequest,
        callback: (Map<String, Boolean>) -> Unit
    ) {
        val launcher = permissionLauncher
        if (launcher == null) {
            PermissionLogger.e(TAG, "权限启动器未初始化")
            return
        }
        
        PermissionLogger.d(TAG, "启动权限请求: ${request.permissions}")
        
        pendingRequest = request
        pendingCallback = callback
        
        try {
            launcher.launch(request.permissions.toTypedArray())
        } catch (e: Exception) {
            PermissionLogger.e(TAG, "启动权限请求失败", e)
            pendingRequest = null
            pendingCallback = null
        }
    }
    
    /**
     * 处理权限请求结果
     */
    private fun handlePermissionResult(permissions: Map<String, Boolean>) {
        val request = pendingRequest
        val callback = pendingCallback
        
        if (request == null || callback == null) {
            PermissionLogger.w(TAG, "收到权限结果但没有待处理的请求")
            return
        }
        
        PermissionLogger.d(TAG, "处理权限结果: $permissions")
        
        // 清理状态
        pendingRequest = null
        pendingCallback = null
        
        // 回调结果
        callback(permissions)
    }
    
    /**
     * 检查是否有待处理的请求
     */
    fun hasPendingRequest(): Boolean {
        return pendingRequest != null
    }
    
    /**
     * 获取待处理的请求
     */
    fun getPendingRequest(): PermissionRequest? {
        return pendingRequest
    }
    
    /**
     * 取消待处理的请求
     */
    fun cancelPendingRequest() {
        if (pendingRequest != null) {
            PermissionLogger.d(TAG, "取消待处理的权限请求: ${pendingRequest?.requestId}")
            pendingRequest = null
            pendingCallback = null
        }
    }
    
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        PermissionLogger.d(TAG, "权限启动器销毁")
        
        // 清理资源
        cancelPendingRequest()
        permissionLauncher = null
    }
}

/**
 * 权限启动器工厂
 */
object PermissionLauncherFactory {
    
    private val launchers = mutableMapOf<ComponentActivity, PermissionLauncher>()
    
    /**
     * 获取或创建权限启动器
     */
    fun getOrCreate(activity: ComponentActivity): PermissionLauncher {
        return launchers.getOrPut(activity) {
            val launcher = PermissionLauncher(activity)
            
            // 监听Activity销毁，清理启动器
            activity.lifecycle.addObserver(object : DefaultLifecycleObserver {
                override fun onDestroy(owner: LifecycleOwner) {
                    launchers.remove(activity)
                }
            })
            
            launcher
        }
    }
    
    /**
     * 移除权限启动器
     */
    fun remove(activity: ComponentActivity) {
        launchers.remove(activity)
    }
    
    /**
     * 清理所有启动器
     */
    fun clear() {
        launchers.clear()
    }
}
