package com.yjsoft.roadtravel.ui.fragments.message

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.launchWithUiState
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.safeLaunch
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * 消息ViewModel
 * 
 * 功能：
 * - 管理消息列表
 * - 处理消息状态
 * - 消息交互逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@HiltViewModel
class MessageViewModel @Inject constructor() : BaseViewModel<MessageUiState>() {
    
    companion object {
        private const val TAG = "MessageViewModel %s"
    }
    
    // ========== 消息列表状态 ==========
    
    private val _messageListState = MutableStateFlow<UiState<List<Message>>>(UiState.Idle)
    val messageListState: StateFlow<UiState<List<Message>>> = _messageListState.asStateFlow()
    
    // ========== 基类实现 ==========
    
    override fun createInitialState(): MessageUiState {
        return MessageUiState()
    }
    
    // ========== 初始化 ==========
    
    init {
        LogManager.d(TAG, "MessageViewModel启动")
        loadMessages()
    }
    
    // ========== 消息管理 ==========
    
    /**
     * 加载消息列表
     */
    fun loadMessages() {
        launchWithUiState(_messageListState, "加载消息中...") {
            // 模拟网络请求
            delay(800)
            
            // 模拟数据
            generateMockMessages()
        }
    }
    
    /**
     * 打开消息详情
     */
    fun openMessage(messageId: String) {
        safeLaunch {
            // 标记消息为已读
            markMessageAsRead(messageId)
            
            updateState { 
                it.copy(
                    selectedMessageId = messageId,
                    lastAction = "打开消息: $messageId"
                )
            }
            
            showToast("打开消息详情")
            LogManager.d(TAG, "打开消息: $messageId")
            
            // 这里可以导航到消息详情页面
            navigateTo("message_detail/$messageId")
        }
    }
    
    /**
     * 标记消息为已读
     */
    private fun markMessageAsRead(messageId: String) {
        val currentMessages = (_messageListState.value as? UiState.Success)?.data ?: return
        val updatedMessages = currentMessages.map { message ->
            if (message.id == messageId) {
                message.copy(isRead = true)
            } else {
                message
            }
        }
        _messageListState.value = UiState.Success(updatedMessages)
        
        // 更新未读数量
        val unreadCount = updatedMessages.count { !it.isRead }
        updateState { 
            it.copy(unreadCount = unreadCount)
        }
    }
    
    /**
     * 标记所有消息为已读
     */
    fun markAllAsRead() {
        safeLaunch {
            val currentMessages = (_messageListState.value as? UiState.Success)?.data ?: return@safeLaunch
            val updatedMessages = currentMessages.map { it.copy(isRead = true) }
            _messageListState.value = UiState.Success(updatedMessages)
            
            updateState { 
                it.copy(
                    unreadCount = 0,
                    lastAction = "标记所有消息为已读"
                )
            }
            
            showToast("所有消息已标记为已读")
            LogManager.d(TAG, "标记所有消息为已读")
        }
    }
    
    /**
     * 删除消息
     */
    fun deleteMessage(messageId: String) {
        safeLaunch {
            val currentMessages = (_messageListState.value as? UiState.Success)?.data ?: return@safeLaunch
            val messageToDelete = currentMessages.find { it.id == messageId }
            
            if (messageToDelete != null) {
                val updatedMessages = currentMessages.filter { it.id != messageId }
                _messageListState.value = UiState.Success(updatedMessages)
                
                val unreadCount = updatedMessages.count { !it.isRead }
                updateState { 
                    it.copy(
                        totalMessages = updatedMessages.size,
                        unreadCount = unreadCount,
                        lastAction = "删除消息: ${messageToDelete.senderName}"
                    )
                }
                
                showToast("消息已删除")
                LogManager.d(TAG, "删除消息: ${messageToDelete.senderName}")
            }
        }
    }
    
    /**
     * 刷新消息列表
     */
    fun refresh() {
        loadMessages()
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 生成模拟消息数据
     */
    private fun generateMockMessages(): List<Message> {
        return listOf(
            Message(
                id = "msg_1",
                senderName = "系统通知",
                content = "欢迎使用RoadTravel！您的旅行助手已准备就绪。",
                time = "10:30",
                type = MessageType.SYSTEM,
                isRead = false
            ),
            Message(
                id = "msg_2",
                senderName = "AI助手",
                content = "您的北京3日游规划已生成完成，点击查看详情。",
                time = "09:15",
                type = MessageType.AI_PLAN,
                isRead = false
            ),
            Message(
                id = "msg_3",
                senderName = "客服小助手",
                content = "您好！有什么可以帮助您的吗？我们的客服团队随时为您服务。",
                time = "昨天",
                type = MessageType.CUSTOMER_SERVICE,
                isRead = true
            ),
            Message(
                id = "msg_4",
                senderName = "行程提醒",
                content = "您的上海之行还有3天开始，记得提前准备行李哦！",
                time = "昨天",
                type = MessageType.REMINDER,
                isRead = true
            ),
            Message(
                id = "msg_5",
                senderName = "优惠活动",
                content = "限时优惠！AI规划服务8折优惠，仅限本周末。",
                time = "2天前",
                type = MessageType.PROMOTION,
                isRead = true
            )
        ).also { messages ->
            // 更新状态
            val unreadCount = messages.count { !it.isRead }
            updateState { 
                it.copy(
                    totalMessages = messages.size,
                    unreadCount = unreadCount
                )
            }
        }
    }
    
    /**
     * 获取消息统计信息
     */
    fun getMessageStats(): String {
        val total = currentState.totalMessages
        val unread = currentState.unreadCount
        return "共 $total 条消息，$unread 条未读"
    }
}

/**
 * 消息UI状态
 */
data class MessageUiState(
    val totalMessages: Int = 0,
    val unreadCount: Int = 0,
    val selectedMessageId: String = "",
    val lastAction: String = ""
)

/**
 * 消息数据类
 */
data class Message(
    val id: String,
    val senderName: String,
    val content: String,
    val time: String,
    val type: MessageType,
    val isRead: Boolean
)

/**
 * 消息类型枚举
 */
enum class MessageType(val displayName: String) {
    TEXT("文本"),
    SYSTEM("系统通知"),
    AI_PLAN("AI规划"),
    CUSTOMER_SERVICE("客服"),
    REMINDER("提醒"),
    PROMOTION("优惠活动")
}
