package com.yjsoft.roadtravel.basiclibrary.payment.models

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.yjsoft.roadtravel.R

/** 支付类型枚举 定义支持的支付方式类型 */
enum class PaymentType(
        @param:StringRes val displayNameRes: Int,
        @param:DrawableRes val iconRes: Int,
        val sdkName: String
) {
    /** 支付宝支付 */
    ALIPAY(
            displayNameRes = R.string.payment_type_alipay,
            iconRes = R.drawable.ic_payment_alipay,
            sdkName = "Alipay SDK"
    ),

    /** 微信支付 */
    WECHAT_PAY(
            displayNameRes = R.string.payment_type_wechat,
            iconRes = R.drawable.ic_payment_wechat,
            sdkName = "WeChat Pay SDK"
    ),

    /** 云闪付 */
    UNION_PAY(
            displayNameRes = R.string.payment_type_unionpay,
            iconRes = R.drawable.ic_payment_unionpay,
            sdkName = "UnionPay SDK"
    );

    companion object {
        /** 获取所有支付类型 */
        fun getAllTypes(): List<PaymentType> = values().toList()

        /** 根据名称获取支付类型 */
        fun fromName(name: String): PaymentType? {
            return values().find { it.name == name }
        }
    }
}
