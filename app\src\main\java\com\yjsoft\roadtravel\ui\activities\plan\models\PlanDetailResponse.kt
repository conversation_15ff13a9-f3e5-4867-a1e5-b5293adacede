package com.yjsoft.roadtravel.ui.activities.plan.models

import com.google.gson.annotations.SerializedName

/**
 * 行程详情接口响应数据模型
 */
data class PlanDetailResponse(
    @SerializedName("code")
    val code: Int,

    @SerializedName("data")
    val data: PlanDetailData? = null,

    @SerializedName("msg")
    val msg: String
) {
    /**
     * 判断请求是否成功
     */
    fun isSuccess(): Boolean = code == 0

    /**
     * 获取成功的数据，如果失败则返回null
     */
    fun getDataOrNull(): PlanDetailData? = if (isSuccess()) data else null

    /**
     * 获取成功的数据，如果失败则抛出异常
     */
    fun getDataOrThrow(): PlanDetailData {
        return if (isSuccess()) {
            data ?: throw Exception("数据为空")
        } else {
            throw Exception(msg)
        }
    }
}

/**
 * 行程详情数据
 */
data class PlanDetailData(
    @SerializedName("prompt_options")
    val promptOptions: PromptOptions? = null,

    @SerializedName("scene_num")
    val sceneNum: Int = 0,

    @SerializedName("integral_cost_state")
    val integralCostState: Int = 0,

    @SerializedName("integral_cost")
    val integralCost: Int = 0,

    @SerializedName("from")
    val from: String = "",

    @SerializedName("to")
    val to: String = "",

    @SerializedName("days")
    val days: Int = 0,

    @SerializedName("transport")
    val transport: String = "",

    @SerializedName("plan_id")
    val planId: Int = 0,

    @SerializedName("ai_reqid")
    val aiReqid: String = "",

    @SerializedName("is_own")
    val isOwn: Boolean = false,

    @SerializedName("likes")
    val likes: Int = 0,

    @SerializedName("is_like")
    val isLike: Boolean = false,

    @SerializedName("user_id")
    val userId: Int = 0,

    @SerializedName("subject")
    val subject: String = "",

    @SerializedName("subtitle")
    val subtitle: String = "",

    @SerializedName("notice")
    val notice: String = "",

    @SerializedName("fit_for")
    val fitFor: String = "",

    @SerializedName("budget")
    val budget: String = "",

    @SerializedName("budget_detail")
    val budgetDetail: String = "",

    @SerializedName("pdf_state")
    val pdfState: Int = 0,

    @SerializedName("pdf_cover")
    val pdfCover: String = "",

    @SerializedName("pdf_url")
    val pdfUrl: String = "",

    @SerializedName("plan_cost")
    val planCost: Double? = null,

    @SerializedName("sections")
    val sections: List<Section> = emptyList(),

    @SerializedName("cost_ok")
    val costOk: Boolean = false,

    @SerializedName("wish_id")
    val wishId: Int = 0,

    @SerializedName("can_edit")
    val canEdit: Boolean = false,

    @SerializedName("distance")
    val distance: Int = 0,

    @SerializedName("cost")
    val cost: Double = 0.0,

    @SerializedName("cost1")
    val cost1: Int = 0,

    @SerializedName("nickname")
    val nickname: String = "",

    @SerializedName("avatar")
    val avatar: String = "",

    @SerializedName("activities")
    val activities: List<Any> = emptyList(),

    @SerializedName("covers")
    val covers: List<String> = emptyList()
)

/**
 * 提示选项数据
 */
data class PromptOptions(
    @SerializedName("from")
    val from: String = "",

    @SerializedName("to")
    val to: String = "",

    @SerializedName("interest")
    val interest: String = "",

    @SerializedName("strength")
    val strength: String = "",

    @SerializedName("accommodation")
    val accommodation: String = "",

    @SerializedName("days")
    val days: String = "",

    @SerializedName("extra_scenes")
    val extraScenes: String = "",

    @SerializedName("travel_date")
    val travelDate: String = "",

    @SerializedName("people")
    val people: String = "",

    @SerializedName("transport")
    val transport: String = "",

    @SerializedName("how_play")
    val howPlay: String = "",

    @SerializedName("start_date")
    val startDate: Long = 0
)

/**
 * 行程段落数据
 */
data class Section(
    @SerializedName("section_name")
    val sectionName: String = "",

    @SerializedName("subject")
    val subject: String = "",

    @SerializedName("zones")
    val zones: List<Zone> = emptyList(),

    @SerializedName("timeline")
    val timeline: List<Timeline> = emptyList()
)

/**
 * 地区数据
 */
data class Zone(
    @SerializedName("id")
    val id: Int = 0,

    @SerializedName("pic")
    val pic: String = "",

    @SerializedName("name")
    val name: String = "",

    @SerializedName("distance")
    val distance: Int = 0,

    @SerializedName("type")
    val type: Int = 0,

    @SerializedName("lng")
    val lng: Double = 0.0,

    @SerializedName("lat")
    val lat: Double = 0.0,

    @SerializedName("price")
    val price: String = ""
)

/**
 * 时间线数据
 */
data class Timeline(
    @SerializedName("pics")
    val pics: List<String> = emptyList(),

    @SerializedName("title")
    val title: String = "",

    @SerializedName("type")
    val type: String = "",

    @SerializedName("time")
    val time: String = "",

    @SerializedName("desc")
    val desc: String = "",

    @SerializedName("item_id")
    val itemId: Int = 0,

    @SerializedName("cate")
    val cate: List<String>? = null,

    @SerializedName("tags")
    val tags: List<String> = emptyList(),

    @SerializedName("avg_price")
    val avgPrice: Int = 0,

    @SerializedName("scene")
    val scene: Scene? = null,

    @SerializedName("poi")
    val poi: Poi? = null
)

/**
 * 景点数据
 */
data class Scene(
    @SerializedName("is_free")
    val isFree: Boolean = false,

    @SerializedName("cost_time")
    val costTime: CostTime? = null,

    @SerializedName("prices")
    val prices: Prices? = null
)

/**
 * 游玩时间数据
 */
data class CostTime(
    @SerializedName("standard")
    val standard: Int = 0,

    @SerializedName("in_depth")
    val inDepth: Int = 0
)

/**
 * 价格信息数据
 */
data class Prices(
    @SerializedName("is_free")
    val isFree: Boolean = false,

    @SerializedName("adult")
    val adult: PriceInfo? = null,

    @SerializedName("child")
    val child: DiscountInfo? = null,

    @SerializedName("senior")
    val senior: DiscountInfo? = null
)

/**
 * 价格详情数据
 */
data class PriceInfo(
    @SerializedName("monthly_price_avg")
    val monthlyPriceAvg: Int = 0,

    @SerializedName("monthly_prices")
    val monthlyPrices: List<Any>? = null
)

/**
 * 折扣信息数据
 */
data class DiscountInfo(
    @SerializedName("discount")
    val discount: String = ""
)

/**
 * POI数据
 */
data class Poi(
    @SerializedName("id")
    val id: Int = 0,

    @SerializedName("pic")
    val pic: String = "",

    @SerializedName("name")
    val name: String = "",

    @SerializedName("distance")
    val distance: Int = 0,

    @SerializedName("type")
    val type: Int = 0,

    @SerializedName("lng")
    val lng: Double = 0.0,

    @SerializedName("lat")
    val lat: Double = 0.0,

    @SerializedName("price")
    val price: String = ""
)
