package com.yjsoft.roadtravel.basiclibrary.network.utils
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import kotlinx.coroutines.delay
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import kotlin.math.min
import kotlin.math.pow
import kotlin.random.Random

/**
 * 重试策略配置
 */
data class RetryPolicy(
    val maxRetries: Int = NetworkConfig.getMaxRetryCount(),
    val initialDelay: Long = NetworkConfig.getRetryDelay(),
    val maxDelay: Long = NetworkConfig.getRetryMaxDelay(),
    val multiplier: Double = NetworkConfig.getRetryMultiplier(),
    val jitter: Boolean = true,
    val retryableExceptions: Set<Class<out Exception>> = DEFAULT_RETRYABLE_EXCEPTIONS,
    val retryableHttpCodes: Set<Int> = DEFAULT_RETRYABLE_HTTP_CODES
) {
    companion object {
        val DEFAULT_RETRYABLE_EXCEPTIONS = setOf(
            IOException::class.java,
            ConnectException::class.java,
            SocketTimeoutException::class.java,
            UnknownHostException::class.java
        )
        
        val DEFAULT_RETRYABLE_HTTP_CODES = setOf(
            408, // Request Timeout
            429, // Too Many Requests
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504  // Gateway Timeout
        )
        
        /**
         * 创建默认重试策略
         */
        fun default(): RetryPolicy = RetryPolicy()
        
        /**
         * 创建激进重试策略（更多重试次数）
         */
        fun aggressive(): RetryPolicy = RetryPolicy(
            maxRetries = 5,
            initialDelay = 500L,
            maxDelay = 30000L
        )
        
        /**
         * 创建保守重试策略（较少重试次数）
         */
        fun conservative(): RetryPolicy = RetryPolicy(
            maxRetries = 2,
            initialDelay = 2000L,
            maxDelay = 5000L
        )
        
        /**
         * 创建无重试策略
         */
        fun none(): RetryPolicy = RetryPolicy(maxRetries = 0)
    }
    
    /**
     * 判断异常是否可重试
     */
    fun isRetryableException(exception: Exception): Boolean {
        return retryableExceptions.any { it.isInstance(exception) }
    }
    
    /**
     * 判断HTTP状态码是否可重试
     */
    fun isRetryableHttpCode(httpCode: Int): Boolean {
        return retryableHttpCodes.contains(httpCode)
    }
    
    /**
     * 计算重试延迟时间
     */
    fun calculateDelay(attempt: Int): Long {
        if (attempt <= 0) return 0L
        
        // 指数退避算法
        val exponentialDelay = (initialDelay * multiplier.pow(attempt - 1)).toLong()
        val delayWithCap = min(exponentialDelay, maxDelay)
        
        // 添加抖动以避免惊群效应
        return if (jitter) {
            val jitterRange = (delayWithCap * 0.1).toLong()
            delayWithCap + Random.nextLong(-jitterRange, jitterRange)
        } else {
            delayWithCap
        }
    }
}

/**
 * 重试拦截器
 */
class RetryInterceptor(
    private val retryPolicy: RetryPolicy = RetryPolicy.default()
) : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var lastException: IOException? = null
        var response: Response? = null
        
        for (attempt in 0..retryPolicy.maxRetries) {
            try {
                // 如果不是第一次尝试，需要等待
                if (attempt > 0) {
                    val delay = retryPolicy.calculateDelay(attempt)
                    Thread.sleep(delay)
                }
                
                response?.close() // 关闭之前的响应
                response = chain.proceed(request)
                
                // 检查HTTP状态码是否需要重试
                if (response.isSuccessful || !retryPolicy.isRetryableHttpCode(response.code)) {
                    return response
                }
                
                // 如果是最后一次尝试，直接返回响应
                if (attempt == retryPolicy.maxRetries) {
                    return response
                }
                
                response.close()
                
            } catch (e: IOException) {
                lastException = e
                
                // 检查异常是否可重试
                if (!retryPolicy.isRetryableException(e)) {
                    throw e
                }
                
                // 如果是最后一次尝试，抛出异常
                if (attempt == retryPolicy.maxRetries) {
                    throw e
                }
            }
        }
        
        // 这里不应该到达，但为了安全起见
        throw lastException ?: IOException("重试失败")
    }
}

/**
 * 协程重试工具类
 */
object CoroutineRetryUtils {
    
    /**
     * 带重试的协程执行
     */
    suspend fun <T> retryWithPolicy(
        retryPolicy: RetryPolicy = RetryPolicy.default(),
        block: suspend () -> T
    ): T {
        var lastException: Exception? = null
        
        for (attempt in 0..retryPolicy.maxRetries) {
            try {
                // 如果不是第一次尝试，需要等待
                if (attempt > 0) {
                    val delay = retryPolicy.calculateDelay(attempt)
                    delay(delay)
                }
                
                return block()
                
            } catch (e: Exception) {
                lastException = e
                
                // 检查异常是否可重试
                if (!retryPolicy.isRetryableException(e)) {
                    throw e
                }
                
                // 如果是最后一次尝试，抛出异常
                if (attempt == retryPolicy.maxRetries) {
                    throw e
                }
            }
        }
        
        // 这里不应该到达，但为了安全起见
        throw lastException ?: Exception("重试失败")
    }
    
    /**
     * 简单重试（使用默认策略）
     */
    suspend fun <T> retry(block: suspend () -> T): T {
        return retryWithPolicy(RetryPolicy.default(), block)
    }
    
    /**
     * 激进重试
     */
    suspend fun <T> retryAggressive(block: suspend () -> T): T {
        return retryWithPolicy(RetryPolicy.aggressive(), block)
    }
    
    /**
     * 保守重试
     */
    suspend fun <T> retryConservative(block: suspend () -> T): T {
        return retryWithPolicy(RetryPolicy.conservative(), block)
    }
}

/**
 * 重试监听器
 */
interface RetryListener {
    /**
     * 重试开始
     */
    fun onRetryStarted(attempt: Int, maxRetries: Int, exception: Exception) {}
    
    /**
     * 重试成功
     */
    fun onRetrySucceeded(attempt: Int, totalAttempts: Int) {}
    
    /**
     * 重试失败
     */
    fun onRetryFailed(totalAttempts: Int, finalException: Exception) {}
}

/**
 * 带监听器的重试拦截器
 */
class RetryInterceptorWithListener(
    private val retryPolicy: RetryPolicy = RetryPolicy.default(),
    private val listener: RetryListener? = null
) : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var lastException: IOException? = null
        var response: Response? = null
        
        for (attempt in 0..retryPolicy.maxRetries) {
            try {
                // 如果不是第一次尝试，需要等待并通知监听器
                if (attempt > 0) {
                    lastException?.let { 
                        listener?.onRetryStarted(attempt, retryPolicy.maxRetries, it)
                    }
                    
                    val delay = retryPolicy.calculateDelay(attempt)
                    Thread.sleep(delay)
                }
                
                response?.close()
                response = chain.proceed(request)
                
                if (response.isSuccessful || !retryPolicy.isRetryableHttpCode(response.code)) {
                    if (attempt > 0) {
                        listener?.onRetrySucceeded(attempt, attempt + 1)
                    }
                    return response
                }
                
                if (attempt == retryPolicy.maxRetries) {
                    return response
                }
                
                response.close()
                
            } catch (e: IOException) {
                lastException = e
                
                if (!retryPolicy.isRetryableException(e)) {
                    listener?.onRetryFailed(attempt + 1, e)
                    throw e
                }
                
                if (attempt == retryPolicy.maxRetries) {
                    listener?.onRetryFailed(attempt + 1, e)
                    throw e
                }
            }
        }
        
        throw lastException ?: IOException("重试失败")
    }
}
