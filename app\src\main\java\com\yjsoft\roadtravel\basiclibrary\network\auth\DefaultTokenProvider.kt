package com.yjsoft.roadtravel.basiclibrary.network.auth

import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenInfo
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenProvider
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * 默认Token提供者实现
 * 提供基础的Token管理功能，可以根据具体业务需求进行扩展
 */
open class DefaultTokenProvider : TokenProvider {
    
    companion object {
        private const val TAG = "DefaultTokenProvider"
    }
    

    
    /**
     * 认证失败回调
     * 当Token刷新失败或认证彻底失败时调用
     */
    override fun onAuthFailure() {
        LogManager.tag(LogConfig.Tags.NETWORK).w("$TAG: 认证失败，需要重新登录")
        
        // 默认实现：只记录日志
        // 实际项目中可能需要：
        // 1. 清除本地存储的用户信息和Token
        // 2. 跳转到登录页面
        // 3. 发送广播通知其他组件
        // 4. 显示认证失败的提示信息
        
        /* 示例实现：
        // 清除本地Token
        clearLocalTokens()
        
        // 发送认证失败广播
        sendAuthFailureBroadcast()
        
        // 跳转到登录页面
        navigateToLogin()
        */
    }
    
    /**
     * 检查Token是否有效
     * 可以根据Token的过期时间等信息判断
     */
    fun isTokenValid(token: String?): Boolean {
        if (token.isNullOrEmpty()) {
            return false
        }
        
        // 简单的Token格式检查
        // 实际项目中可能需要检查Token的格式、过期时间等
        return token.length > 10 // 简单的长度检查
    }
    
    /**
     * 获取Token过期时间
     * 可以从Token中解析或从本地存储获取
     */
    fun getTokenExpirationTime(token: String?): Long {
        // 默认实现：返回0表示未知
        // 实际项目中可能需要解析JWT Token或从本地存储获取
        return 0L
    }
    
    /**
     * 检查Token是否即将过期
     * @param token 要检查的Token
     * @param thresholdMinutes 提前多少分钟算作即将过期
     */
    fun isTokenExpiringSoon(token: String?, thresholdMinutes: Int = 5): Boolean {
        val expirationTime = getTokenExpirationTime(token)
        if (expirationTime <= 0) {
            return false
        }
        
        val currentTime = System.currentTimeMillis()
        val thresholdTime = thresholdMinutes * 60 * 1000L
        
        return (expirationTime - currentTime) <= thresholdTime
    }
}

/**
 * 具体业务的Token提供者实现示例
 * 展示如何扩展DefaultTokenProvider实现具体的业务逻辑
 */
class BusinessTokenProvider(
    private val apiService: Any? = null // 实际项目中应该注入具体的API服务
) : DefaultTokenProvider() {
    

    
    override fun onAuthFailure() {
        LogManager.tag(LogConfig.Tags.NETWORK).w("BusinessTokenProvider: 认证失败，执行清理操作")
        
        // 执行具体的认证失败处理逻辑
        clearUserData()
        notifyAuthFailure()
        navigateToLogin()
    }
    
    private fun clearUserData() {
        // 清除用户数据的具体实现
        LogManager.tag(LogConfig.Tags.NETWORK).d("BusinessTokenProvider: 清除用户数据")
    }
    
    private fun notifyAuthFailure() {
        // 通知认证失败的具体实现
        LogManager.tag(LogConfig.Tags.NETWORK).d("BusinessTokenProvider: 发送认证失败通知")
    }
    
    private fun navigateToLogin() {
        // 跳转到登录页面的具体实现
        LogManager.tag(LogConfig.Tags.NETWORK).d("BusinessTokenProvider: 跳转到登录页面")
    }
}
