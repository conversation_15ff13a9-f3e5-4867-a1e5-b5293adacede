package com.yjsoft.roadtravel.basiclibrary.permission.config

import android.Manifest
import android.os.Build
import androidx.annotation.RequiresApi

/**
 * 权限说明文案管理
 * 提供各种权限的用户友好说明文案
 */
object PermissionMessages {
    
    /**
     * 权限说明文案数据类
     */
    data class PermissionMessage(
        val title: String,
        val rationale: String,
        val settingsMessage: String,
        val deniedMessage: String? = null
    )
    
    /**
     * 默认权限说明文案
     */

    private val defaultMessages = mapOf(
        // 位置权限
        Manifest.permission.ACCESS_FINE_LOCATION to PermissionMessage(
            title = "精确位置权限",
            rationale = "应用需要获取您的精确位置信息来提供准确的导航和定位服务，帮助您找到最佳路线。",
            settingsMessage = "请在设置中开启位置权限，以便应用为您提供精确的导航服务。",
            deniedMessage = "没有位置权限，应用无法提供导航和定位功能。"
        ),
        
        Manifest.permission.ACCESS_COARSE_LOCATION to PermissionMessage(
            title = "大致位置权限",
            rationale = "应用需要获取您的大致位置信息来提供基本的定位服务和区域相关功能。",
            settingsMessage = "请在设置中开启位置权限，以便应用为您提供定位服务。",
            deniedMessage = "没有位置权限，应用无法提供定位功能。"
        ),

        Manifest.permission.ACCESS_BACKGROUND_LOCATION to PermissionMessage(
            title = "后台位置权限",
            rationale = "应用需要在后台获取位置信息来提供持续的导航服务和位置提醒功能。",
            settingsMessage = "请在设置中允许应用始终访问位置信息。",
            deniedMessage = "没有后台位置权限，应用无法在后台提供位置服务。"
        ),
        
        // 相机权限
        Manifest.permission.CAMERA to PermissionMessage(
            title = "相机权限",
            rationale = "应用需要使用相机来拍摄照片和录制视频，记录您的旅行美好时刻。",
            settingsMessage = "请在设置中开启相机权限，以便使用拍照功能。",
            deniedMessage = "没有相机权限，无法使用拍照和录像功能。"
        ),
        
        // 麦克风权限
        Manifest.permission.RECORD_AUDIO to PermissionMessage(
            title = "麦克风权限",
            rationale = "应用需要使用麦克风来录制音频，为您的旅行记录添加声音。",
            settingsMessage = "请在设置中开启麦克风权限，以便使用录音功能。",
            deniedMessage = "没有麦克风权限，无法使用录音功能。"
        ),
        
        // 存储权限
        Manifest.permission.READ_EXTERNAL_STORAGE to PermissionMessage(
            title = "存储读取权限",
            rationale = "应用需要读取存储空间来访问您的照片和文件，用于分享和编辑。",
            settingsMessage = "请在设置中开启存储权限，以便访问您的文件。",
            deniedMessage = "没有存储权限，无法访问您的照片和文件。"
        ),
        
        Manifest.permission.WRITE_EXTERNAL_STORAGE to PermissionMessage(
            title = "存储写入权限",
            rationale = "应用需要写入存储空间来保存您的照片、视频和其他文件。",
            settingsMessage = "请在设置中开启存储权限，以便保存文件。",
            deniedMessage = "没有存储权限，无法保存照片和文件。"
        ),
        
        // Android 13+ 媒体权限
        Manifest.permission.READ_MEDIA_IMAGES to PermissionMessage(
            title = "图片访问权限",
            rationale = "应用需要访问您的图片来进行分享和编辑。",
            settingsMessage = "请在设置中开启图片访问权限。",
            deniedMessage = "没有图片访问权限，无法使用相关功能。"
        ),
        
        Manifest.permission.READ_MEDIA_VIDEO to PermissionMessage(
            title = "视频访问权限",
            rationale = "应用需要访问您的视频来进行播放和分享。",
            settingsMessage = "请在设置中开启视频访问权限。",
            deniedMessage = "没有视频访问权限，无法使用相关功能。"
        ),
        
        Manifest.permission.READ_MEDIA_AUDIO to PermissionMessage(
            title = "音频访问权限",
            rationale = "应用需要访问您的音频文件来进行播放和分享。",
            settingsMessage = "请在设置中开启音频访问权限。",
            deniedMessage = "没有音频访问权限，无法使用相关功能。"
        ),
        
        // 通知权限 (Android 13+)
        Manifest.permission.POST_NOTIFICATIONS to PermissionMessage(
            title = "通知权限",
            rationale = "应用需要发送通知来及时提醒您重要信息和更新。",
            settingsMessage = "请在设置中开启通知权限，以便接收重要提醒。",
            deniedMessage = "没有通知权限，您将无法收到应用的重要提醒。"
        ),
        
        // 联系人权限
        Manifest.permission.READ_CONTACTS to PermissionMessage(
            title = "联系人读取权限",
            rationale = "应用需要访问您的联系人来方便您分享内容给朋友。",
            settingsMessage = "请在设置中开启联系人权限。",
            deniedMessage = "没有联系人权限，无法使用联系人相关功能。"
        ),
        
        Manifest.permission.WRITE_CONTACTS to PermissionMessage(
            title = "联系人写入权限",
            rationale = "应用需要修改联系人信息来保存相关数据。",
            settingsMessage = "请在设置中开启联系人权限。",
            deniedMessage = "没有联系人权限，无法保存联系人信息。"
        ),
        
        // 电话权限
        Manifest.permission.READ_PHONE_STATE to PermissionMessage(
            title = "电话状态权限",
            rationale = "应用需要读取电话状态来提供更好的用户体验。",
            settingsMessage = "请在设置中开启电话权限。",
            deniedMessage = "没有电话权限，部分功能可能无法正常使用。"
        ),
        
        Manifest.permission.CALL_PHONE to PermissionMessage(
            title = "拨打电话权限",
            rationale = "应用需要拨打电话权限来帮助您快速联系相关服务。",
            settingsMessage = "请在设置中开启拨打电话权限。",
            deniedMessage = "没有拨打电话权限，无法使用一键拨号功能。"
        )
    )
    
    /**
     * 自定义权限说明文案
     */
    private val customMessages = mutableMapOf<String, PermissionMessage>()
    
    /**
     * 获取权限说明文案
     */
    fun getMessage(permission: String): PermissionMessage {
        return customMessages[permission] 
            ?: defaultMessages[permission] 
            ?: getDefaultMessage(permission)
    }
    
    /**
     * 设置自定义权限说明文案
     */
    fun setCustomMessage(permission: String, message: PermissionMessage) {
        customMessages[permission] = message
    }
    
    /**
     * 批量设置自定义权限说明文案
     */
    fun setCustomMessages(messages: Map<String, PermissionMessage>) {
        customMessages.putAll(messages)
    }
    
    /**
     * 清除自定义权限说明文案
     */
    fun clearCustomMessage(permission: String) {
        customMessages.remove(permission)
    }
    
    /**
     * 清除所有自定义权限说明文案
     */
    fun clearAllCustomMessages() {
        customMessages.clear()
    }
    
    /**
     * 获取默认权限说明文案
     */
    private fun getDefaultMessage(permission: String): PermissionMessage {
        return PermissionMessage(
            title = "权限申请",
            rationale = "应用需要此权限才能正常工作，请允许权限申请。",
            settingsMessage = "请在设置中手动开启此权限。",
            deniedMessage = "权限被拒绝，相关功能可能无法使用。"
        )
    }
    
    /**
     * 获取权限组的说明文案
     */
    fun getGroupMessage(groupName: String): PermissionMessage {
        return when (groupName) {
            PermissionGroups.LOCATION_GROUP -> PermissionMessage(
                title = "位置权限",
                rationale = "应用需要获取您的位置信息来提供导航、定位和位置相关服务。这将帮助您获得更准确的路线规划和周边信息。",
                settingsMessage = "请在设置中开启位置权限，包括精确位置和后台位置访问权限。",
                deniedMessage = "没有位置权限，应用无法提供导航和定位功能。"
            )
            
            PermissionGroups.STORAGE_GROUP -> PermissionMessage(
                title = "存储权限",
                rationale = "应用需要访问您的存储空间来保存和读取照片、视频等文件，让您能够记录和分享美好的旅行时光。",
                settingsMessage = "请在设置中开启存储权限，以便保存和访问您的文件。",
                deniedMessage = "没有存储权限，无法保存照片和访问文件。"
            )
            
            PermissionGroups.CAMERA_GROUP -> PermissionMessage(
                title = "相机权限",
                rationale = "应用需要使用相机来拍摄照片和录制视频，记录您旅行中的精彩瞬间。",
                settingsMessage = "请在设置中开启相机权限。",
                deniedMessage = "没有相机权限，无法使用拍照和录像功能。"
            )
            
            else -> getDefaultMessage("group_$groupName")
        }
    }
    
    /**
     * 格式化权限说明文案
     */
    fun formatMessage(template: String, vararg args: Any): String {
        return try {
            String.format(java.util.Locale.getDefault(), template, *args)
        } catch (e: Exception) {
            template
        }
    }
}
