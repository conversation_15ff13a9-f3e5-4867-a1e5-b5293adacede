package com.yjsoft.roadtravel.basiclibrary.image.utils

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.request.ImageRequest
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import coil3.transform.RoundedCornersTransformation
import coil3.transform.Transformation
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * 图片变换工具类
 * 提供常用的图片变换效果，如圆角、圆形、模糊等
 */
object ImageTransformations {
    
    /**
     * 变换类型枚举
     */
    enum class TransformationType {
        NONE,           // 无变换
        CIRCLE,         // 圆形裁剪
        ROUNDED_CORNER, // 圆角
        BLUR,           // 模糊
        GRAYSCALE       // 灰度
    }
    
    /**
     * 创建圆形裁剪变换
     */
    fun createCircleTransformation(): Transformation {
        return CircleCropTransformation()
    }
    
    /**
     * 创建圆角变换
     */
    fun createRoundedCornersTransformation(
        topLeft: Dp = 0.dp,
        topRight: Dp = 0.dp,
        bottomLeft: Dp = 0.dp,
        bottomRight: Dp = 0.dp
    ): Transformation {
        return RoundedCornersTransformation(
            topLeft = topLeft.value,
            topRight = topRight.value,
            bottomLeft = bottomLeft.value,
            bottomRight = bottomRight.value
        )
    }
    
    /**
     * 创建统一圆角变换
     */
    fun createRoundedCornersTransformation(radius: Dp): Transformation {
        return RoundedCornersTransformation(radius.value)
    }
    
    /**
     * 根据Shape创建对应的变换
     */
    fun createTransformationFromShape(shape: Shape): Transformation? {
        return when (shape::class.simpleName) {
            "CircleShape" -> createCircleTransformation()
            "RoundedCornerShape" -> {
                // 注意：这里简化处理，实际项目中可能需要更复杂的逻辑来提取圆角值
                createRoundedCornersTransformation(8.dp)
            }
            else -> {
                LogManager.tag(LogConfig.Tags.IMAGE).d("不支持的Shape类型: ${shape::class.simpleName}")
                null
            }
        }
    }
    
    /**
     * 为ImageRequest添加变换
     */
    fun ImageRequest.Builder.applyTransformation(transformation: Transformation): ImageRequest.Builder {
        return this.transformations(transformation)
    }
    
    /**
     * 为ImageRequest添加多个变换
     */
    fun ImageRequest.Builder.applyTransformations(vararg transformations: Transformation): ImageRequest.Builder {
        return this.transformations(*transformations)
    }
    
    /**
     * 为ImageRequest添加圆形变换
     */
    fun ImageRequest.Builder.circleTransform(): ImageRequest.Builder {
        return this.transformations(createCircleTransformation())
    }
    
    /**
     * 为ImageRequest添加圆角变换
     */
    fun ImageRequest.Builder.roundedCornersTransform(radius: Dp): ImageRequest.Builder {
        return this.transformations(createRoundedCornersTransformation(radius))
    }
    
    /**
     * 为ImageRequest添加自定义圆角变换
     */
    fun ImageRequest.Builder.roundedCornersTransform(
        topLeft: Dp = 0.dp,
        topRight: Dp = 0.dp,
        bottomLeft: Dp = 0.dp,
        bottomRight: Dp = 0.dp
    ): ImageRequest.Builder {
        return this.transformations(
            createRoundedCornersTransformation(topLeft, topRight, bottomLeft, bottomRight)
        )
    }
    
    /**
     * 创建头像专用的变换组合
     */
    fun createAvatarTransformations(): List<Transformation> {
        return listOf(createCircleTransformation())
    }
    
    /**
     * 创建卡片图片的变换组合
     */
    fun createCardImageTransformations(cornerRadius: Dp = 12.dp): List<Transformation> {
        return listOf(createRoundedCornersTransformation(cornerRadius))
    }
    
    /**
     * 创建缩略图的变换组合
     */
    fun createThumbnailTransformations(cornerRadius: Dp = 8.dp): List<Transformation> {
        return listOf(createRoundedCornersTransformation(cornerRadius))
    }
    
    /**
     * 根据变换类型创建变换
     */
    fun createTransformation(
        type: TransformationType,
        radius: Dp = 8.dp
    ): Transformation? {
        return when (type) {
            TransformationType.NONE -> null
            TransformationType.CIRCLE -> createCircleTransformation()
            TransformationType.ROUNDED_CORNER -> createRoundedCornersTransformation(radius)
            TransformationType.BLUR -> {
                LogManager.tag(LogConfig.Tags.IMAGE).w("模糊变换需要自定义实现")
                null
            }
            TransformationType.GRAYSCALE -> {
                LogManager.tag(LogConfig.Tags.IMAGE).w("灰度变换需要自定义实现")
                null
            }
        }
    }
    
    /**
     * 变换配置数据类
     */
    data class TransformationConfig(
        val type: TransformationType = TransformationType.NONE,
        val radius: Dp = 8.dp,
        val topLeft: Dp = 0.dp,
        val topRight: Dp = 0.dp,
        val bottomLeft: Dp = 0.dp,
        val bottomRight: Dp = 0.dp,
        val customTransformations: List<Transformation> = emptyList()
    ) {
        /**
         * 创建变换列表
         */
        fun createTransformations(): List<Transformation> {
            val transformations = mutableListOf<Transformation>()
            
            // 添加基础变换
            when (type) {
                TransformationType.CIRCLE -> {
                    transformations.add(createCircleTransformation())
                }
                TransformationType.ROUNDED_CORNER -> {
                    if (topLeft == topRight && topRight == bottomLeft && bottomLeft == bottomRight && topLeft == radius) {
                        // 统一圆角
                        transformations.add(createRoundedCornersTransformation(radius))
                    } else {
                        // 自定义圆角
                        transformations.add(
                            createRoundedCornersTransformation(topLeft, topRight, bottomLeft, bottomRight)
                        )
                    }
                }
                else -> {
                    // 其他类型暂不处理
                }
            }
            
            // 添加自定义变换
            transformations.addAll(customTransformations)
            
            return transformations
        }
    }
    
    /**
     * 常用变换配置预设
     */
    object Presets {
        /**
         * 头像配置
         */
        val AVATAR = TransformationConfig(
            type = TransformationType.CIRCLE
        )
        
        /**
         * 卡片图片配置
         */
        val CARD = TransformationConfig(
            type = TransformationType.ROUNDED_CORNER,
            radius = 12.dp
        )
        
        /**
         * 缩略图配置
         */
        val THUMBNAIL = TransformationConfig(
            type = TransformationType.ROUNDED_CORNER,
            radius = 8.dp
        )
        
        /**
         * 轮播图配置
         */
        val BANNER = TransformationConfig(
            type = TransformationType.ROUNDED_CORNER,
            radius = 16.dp
        )
        
        /**
         * 无变换配置
         */
        val NONE = TransformationConfig(
            type = TransformationType.NONE
        )
        
        /**
         * 顶部圆角配置（适用于卡片顶部图片）
         */
        val TOP_ROUNDED = TransformationConfig(
            type = TransformationType.ROUNDED_CORNER,
            topLeft = 12.dp,
            topRight = 12.dp,
            bottomLeft = 0.dp,
            bottomRight = 0.dp
        )
        
        /**
         * 底部圆角配置
         */
        val BOTTOM_ROUNDED = TransformationConfig(
            type = TransformationType.ROUNDED_CORNER,
            topLeft = 0.dp,
            topRight = 0.dp,
            bottomLeft = 12.dp,
            bottomRight = 12.dp
        )
    }
    
    /**
     * 应用变换配置到ImageRequest
     */
    fun ImageRequest.Builder.applyTransformationConfig(config: TransformationConfig): ImageRequest.Builder {
        val transformations = config.createTransformations()
        return if (transformations.isNotEmpty()) {
            this.transformations(transformations)
        } else {
            this
        }
    }
    
    /**
     * 获取变换信息
     */
    fun getTransformationInfo(): String {
        return buildString {
            appendLine("=== 图片变换工具信息 ===")
            appendLine("支持的变换类型:")
            TransformationType.values().forEach { type ->
                appendLine("  - ${type.name}: ${getTransformationDescription(type)}")
            }
            appendLine("预设配置:")
            appendLine("  - AVATAR: 圆形头像")
            appendLine("  - CARD: 12dp圆角卡片")
            appendLine("  - THUMBNAIL: 8dp圆角缩略图")
            appendLine("  - BANNER: 16dp圆角轮播图")
            appendLine("  - TOP_ROUNDED: 顶部圆角")
            appendLine("  - BOTTOM_ROUNDED: 底部圆角")
        }
    }
    
    /**
     * 获取变换类型描述
     */
    private fun getTransformationDescription(type: TransformationType): String {
        return when (type) {
            TransformationType.NONE -> "无变换"
            TransformationType.CIRCLE -> "圆形裁剪"
            TransformationType.ROUNDED_CORNER -> "圆角处理"
            TransformationType.BLUR -> "模糊效果（待实现）"
            TransformationType.GRAYSCALE -> "灰度效果（待实现）"
        }
    }
}
