package com.yjsoft.roadtravel.ui.activities.main

import com.google.gson.annotations.SerializedName

/**
 * 初始化接口响应数据模型
 */
data class InitApiResponse(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("data")
    val data: InitData? = null,
    
    @SerializedName("msg")
    val msg: String
) {
    /**
     * 判断请求是否成功
     */
    fun isSuccess(): Boolean = code == 0
    
    /**
     * 获取成功的数据，如果失败则返回null
     */
    fun getDataOrNull(): InitData? = if (isSuccess()) data else null
    
    /**
     * 获取成功的数据，如果失败则抛出异常
     */
    fun getDataOrThrow(): InitData {
        return if (isSuccess()) {
            data ?: throw Exception("数据为空")
        } else {
            throw Exception(msg)
        }
    }
}

/**
 * 初始化数据
 */
data class InitData(
    @SerializedName("js_sdk_config")
    val jsSdkConfig: Any? = null, // 当前为null，使用Any类型
    
    @SerializedName("zone_id")
    val zoneId: Int,
    
    @SerializedName("zone_name")
    val zoneName: String,
    
    @SerializedName("lat")
    val lat: Double,
    
    @SerializedName("lng")
    val lng: Double,
    
    @SerializedName("service_wx")
    val serviceWx: String,
    
    @SerializedName("service_email")
    val serviceEmail: String,
    
    @SerializedName("share_mini_task")
    val shareMiniTask: ShareMiniTask,
    
    @SerializedName("plan_cost_amount")
    val planCostAmount: Int,
    
    @SerializedName("bind_invite_give")
    val bindInviteGive: Int,
    
    @SerializedName("plan_pdf_cost_amount")
    val planPdfCostAmount: Int,
    
    @SerializedName("package_buy_service")
    val packageBuyService: String,
    
    @SerializedName("ad_config")
    val adConfig: AdConfig
)

/**
 * 分享小程序任务
 */
data class ShareMiniTask(
    @SerializedName("task_id")
    val taskId: Int,
    
    @SerializedName("short_desc")
    val shortDesc: String,
    
    @SerializedName("value")
    val value: Int
)

/**
 * 广告配置
 */
data class AdConfig(
    @SerializedName("integral_pop")
    val integralPop: IntegralPop
)

/**
 * 积分弹窗广告
 */
data class IntegralPop(
    @SerializedName("type")
    val type: String,
    
    @SerializedName("ad_id")
    val adId: String,
    
    @SerializedName("amount")
    val amount: Int
) 