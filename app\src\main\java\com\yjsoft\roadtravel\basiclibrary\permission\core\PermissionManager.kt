package com.yjsoft.roadtravel.basiclibrary.permission.core

import android.content.Context
import android.content.pm.PackageManager
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionLogger
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * 权限管理器核心类
 * 提供统一的权限申请、检查和状态管理功能
 *
 * 注意：为了避免内存泄漏，使用ApplicationContext并确保正确的生命周期管理
 */
class PermissionManager private constructor(
    context: Context
) : DefaultLifecycleObserver {

    companion object {
        private const val TAG = "PermissionManager"

        @Volatile
        private var INSTANCE: PermissionManager? = null

        /**
         * 获取权限管理器实例
         * 使用ApplicationContext避免内存泄漏
         */
        fun getInstance(context: Context): PermissionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PermissionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    // 使用ApplicationContext避免内存泄漏
    private val applicationContext = context.applicationContext
    
    // 权限状态流
    private val _permissionStates = MutableStateFlow<Map<String, PermissionState>>(emptyMap())
    val permissionStates: StateFlow<Map<String, PermissionState>> = _permissionStates.asStateFlow()
    
    // 当前活动的权限请求
    private val activeRequests = mutableMapOf<String, PermissionRequest>()
    
    // 权限启动器
    private var permissionLauncher: ActivityResultLauncher<Array<String>>? = null
    
    // 当前等待结果的请求
    private var pendingRequest: PermissionRequest? = null
    private var pendingCallback: ((PermissionResult) -> Unit)? = null
    
    /**
     * 初始化权限管理器
     *
     * 重要：必须在Activity的onCreate方法中调用，且在setContent之前
     * 因为ActivityResultLauncher必须在Activity的STARTED状态之前注册
     *
     * @param activity 需要申请权限的Activity
     */
    fun initialize(activity: ComponentActivity) {
        PermissionLogger.d(TAG, "初始化权限管理器")
        
        // 注册生命周期观察者
        activity.lifecycle.addObserver(this)
        
        // 创建权限启动器
        permissionLauncher = activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            handlePermissionResult(permissions)
        }
        
        // 初始化权限状态
        refreshAllPermissionStates()
    }
    
    /**
     * 检查单个权限状态
     */
    fun checkPermission(permission: String): PermissionState {
        val isGranted = ContextCompat.checkSelfPermission(applicationContext, permission) == PackageManager.PERMISSION_GRANTED
        val status = if (isGranted) PermissionStatus.GRANTED else PermissionStatus.DENIED
        
        return PermissionState(
            permission = permission,
            status = status,
            shouldShowRationale = false,
            isFirstRequest = true
        )
    }
    
    /**
     * 检查多个权限状态
     */
    fun checkPermissions(permissions: List<String>): MultiplePermissionState {
        val permissionStates = permissions.associateWith { checkPermission(it) }
        return MultiplePermissionState(permissionStates)
    }
    
    /**
     * 请求权限（挂起函数）
     */
    suspend fun requestPermissions(request: PermissionRequest): PermissionResult {
        PermissionLogger.d(TAG, "请求权限: ${request.permissions}, 请求ID: ${request.requestId}")
        
        // 检查权限启动器是否已初始化
        val launcher = permissionLauncher
            ?: return PermissionResult.Error(
                permissions = request.permissions,
                requestId = request.requestId,
                error = IllegalStateException("权限管理器未初始化，请先调用 initialize() 方法")
            )
        
        // 检查是否已经有权限
        val currentStates = checkPermissions(request.permissions)
        if (currentStates.allGranted) {
            PermissionLogger.d(TAG, "所有权限已授予: ${request.permissions}")
            return PermissionResult.Granted(
                permissions = request.permissions,
                requestId = request.requestId
            )
        }
        
        // 记录请求
        activeRequests[request.requestId] = request
        
        return suspendCancellableCoroutine { continuation ->
            pendingRequest = request
            pendingCallback = { result ->
                PermissionLogger.d(TAG, "权限请求结果: $result")
                continuation.resume(result)
            }
            
            // 启动权限请求
            try {
                launcher.launch(request.permissions.toTypedArray())
            } catch (e: Exception) {
                PermissionLogger.e(TAG, "启动权限请求失败", e)
                val errorResult = PermissionResult.Error(
                    permissions = request.permissions,
                    requestId = request.requestId,
                    error = e
                )
                continuation.resume(errorResult)
            }
            
            // 设置取消回调
            continuation.invokeOnCancellation {
                PermissionLogger.d(TAG, "权限请求被取消: ${request.requestId}")
                activeRequests.remove(request.requestId)
                pendingRequest = null
                pendingCallback = null
            }
        }
    }
    
    /**
     * 处理权限请求结果
     */
    private fun handlePermissionResult(permissions: Map<String, Boolean>) {
        val request = pendingRequest
        val callback = pendingCallback
        
        if (request == null || callback == null) {
            PermissionLogger.w(TAG, "收到权限结果但没有待处理的请求")
            return
        }
        
        PermissionLogger.d(TAG, "处理权限结果: $permissions")
        
        // 分析结果
        val grantedPermissions = permissions.filter { it.value }.keys.toList()
        val deniedPermissions = permissions.filter { !it.value }.keys.toList()
        
        // 更新权限状态
        updatePermissionStates(permissions)
        
        // 创建结果
        val result = if (deniedPermissions.isEmpty()) {
            PermissionResult.Granted(
                permissions = grantedPermissions,
                requestId = request.requestId
            )
        } else {
            PermissionResult.Denied(
                deniedPermissions = deniedPermissions,
                grantedPermissions = grantedPermissions,
                requestId = request.requestId
            )
        }
        
        // 清理状态
        activeRequests.remove(request.requestId)
        pendingRequest = null
        pendingCallback = null
        
        // 回调结果
        callback(result)
    }
    
    /**
     * 更新权限状态
     */
    private fun updatePermissionStates(permissions: Map<String, Boolean>) {
        val currentStates = _permissionStates.value.toMutableMap()
        
        permissions.forEach { (permission, granted) ->
            val status = if (granted) PermissionStatus.GRANTED else PermissionStatus.DENIED
            currentStates[permission] = PermissionState(
                permission = permission,
                status = status,
                shouldShowRationale = false,
                isFirstRequest = false
            )
        }
        
        _permissionStates.value = currentStates
    }
    
    /**
     * 刷新所有权限状态
     */
    private fun refreshAllPermissionStates() {
        // 这里可以根据需要刷新特定权限的状态
        // 暂时保持空实现，后续可以扩展
    }
    
    /**
     * 获取权限状态
     */
    fun getPermissionState(permission: String): PermissionState? {
        return _permissionStates.value[permission] ?: checkPermission(permission)
    }
    
    /**
     * 是否有活动的权限请求
     */
    fun hasActiveRequest(): Boolean {
        return activeRequests.isNotEmpty()
    }
    
    /**
     * 获取活动的权限请求
     */
    fun getActiveRequests(): List<PermissionRequest> {
        return activeRequests.values.toList()
    }
    
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        PermissionLogger.d(TAG, "权限管理器销毁")
        
        // 清理资源
        activeRequests.clear()
        pendingRequest = null
        pendingCallback = null
        permissionLauncher = null
    }
}
