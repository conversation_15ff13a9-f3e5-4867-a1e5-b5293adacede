package com.yjsoft.roadtravel.basiclibrary.auth.wechat

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 微信登录API接口
 */
interface WeChatApiService {
    
    /**
     * 通过code获取access_token
     */
    @GET("sns/oauth2/access_token")
    suspend fun getAccessToken(
        @Query("appid") appId: String,
        @Query("secret") secret: String,
        @Query("code") code: String,
        @Query("grant_type") grantType: String = "authorization_code"
    ): Response<WeChatAccessTokenResponse>
    
    /**
     * 刷新access_token
     */
    @GET("sns/oauth2/refresh_token")
    suspend fun refreshAccessToken(
        @Query("appid") appId: String,
        @Query("grant_type") grantType: String = "refresh_token",
        @Query("refresh_token") refreshToken: String
    ): Response<WeChatAccessTokenResponse>
    
    /**
     * 检查access_token有效性
     */
    @GET("sns/auth")
    suspend fun checkAccessToken(
        @Query("access_token") accessToken: String,
        @Query("openid") openId: String
    ): Response<WeChatCheckTokenResponse>
    
    /**
     * 获取用户信息
     */
    @GET("sns/userinfo")
    suspend fun getUserInfo(
        @Query("access_token") accessToken: String,
        @Query("openid") openId: String,
        @Query("lang") lang: String = "zh_CN"
    ): Response<WeChatUserInfoResponse>

}

/**
 * 微信access_token响应
 */
data class WeChatAccessTokenResponse(
    val access_token: String?,
    val expires_in: Int?,
    val refresh_token: String?,
    val openid: String?,
    val scope: String?,
    val unionid: String?,
    val errcode: Int?,
    val errmsg: String?
) {
    fun isSuccess(): Boolean = errcode == null || errcode == 0
    
    fun getErrorMessage(): String = errmsg ?: "未知错误"
}

/**
 * 微信token检查响应
 */
data class WeChatCheckTokenResponse(
    val errcode: Int,
    val errmsg: String?
) {
    fun isValid(): Boolean = errcode == 0
}

/**
 * 微信用户信息响应
 */
data class WeChatUserInfoResponse(
    val openid: String?,
    val nickname: String?,
    val sex: Int?, // 1为男性，2为女性，0为未知
    val province: String?,
    val city: String?,
    val country: String?,
    val headimgurl: String?,
    val privilege: List<String>?,
    val unionid: String?,
    val errcode: Int?,
    val errmsg: String?
) {
    fun isSuccess(): Boolean = errcode == null || errcode == 0
    
    fun getErrorMessage(): String = errmsg ?: "未知错误"
    
    fun getSexString(): String = when (sex) {
        1 -> "男"
        2 -> "女"
        else -> "未知"
    }
}

/**
 * 微信登录用户信息（业务层使用的简化版本）
 */
data class WeChatUserInfo(
    val openId: String,
    val unionId: String?,
    val nickname: String,
    val avatar: String?,
    val sex: String,
    val province: String?,
    val city: String?,
    val country: String?
) {
    companion object {
        fun fromResponse(response: WeChatUserInfoResponse): WeChatUserInfo? {
            return if (response.isSuccess() && !response.openid.isNullOrEmpty()) {
                WeChatUserInfo(
                    openId = response.openid,
                    unionId = response.unionid,
                    nickname = response.nickname ?: "微信用户",
                    avatar = response.headimgurl,
                    sex = response.getSexString(),
                    province = response.province,
                    city = response.city,
                    country = response.country
                )
            } else {
                null
            }
        }
    }
}
