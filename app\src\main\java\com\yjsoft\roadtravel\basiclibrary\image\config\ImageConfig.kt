package com.yjsoft.roadtravel.basiclibrary.image.config

import android.content.Context
import coil3.ImageLoader
import coil3.disk.DiskCache
import coil3.memory.MemoryCache
import coil3.request.crossfade
import coil3.util.DebugLogger
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import okhttp3.OkHttpClient

/**
 * 图片加载配置类
 * 提供图片加载的各种配置选项
 */
object ImageConfig {
    
    /**
     * 图片加载环境配置
     */
    enum class Environment {
        DEBUG,      // 调试环境
        RELEASE     // 发布环境
    }
    
    /**
     * 默认配置常量
     */
    object Defaults {
        const val MEMORY_CACHE_SIZE_PERCENT = 0.25  // 内存缓存占应用内存的25%
        const val DISK_CACHE_SIZE_MB = 250L         // 磁盘缓存250MB
        const val CROSSFADE_DURATION_MS = 300       // 淡入淡出动画时长
        const val PLACEHOLDER_FADE_IN = true        // 占位符淡入效果
        const val ERROR_FADE_IN = true              // 错误图片淡入效果
        const val ALLOW_HARDWARE_BITMAPS = true     // 允许硬件位图
        const val ALLOW_RGB_565 = false             // 不允许RGB565格式（保证图片质量）
    }
    
    /**
     * 图片质量配置
     */
    enum class ImageQuality {
        LOW,        // 低质量：RGB565，压缩率高
        MEDIUM,     // 中等质量：ARGB8888，适度压缩
        HIGH        // 高质量：ARGB8888，最小压缩
    }
    
    /**
     * 缓存策略配置
     */
    enum class CacheStrategy {
        MEMORY_ONLY,        // 仅内存缓存
        DISK_ONLY,          // 仅磁盘缓存
        MEMORY_AND_DISK,    // 内存+磁盘缓存
        NO_CACHE            // 不缓存
    }
    
    /**
     * 图片加载配置数据类
     */
    data class ImageLoadConfig(
        val environment: Environment = Environment.RELEASE,
        val imageQuality: ImageQuality = ImageQuality.MEDIUM,
        val cacheStrategy: CacheStrategy = CacheStrategy.MEMORY_AND_DISK,
        val memoryCacheSizePercent: Double = Defaults.MEMORY_CACHE_SIZE_PERCENT,
        val diskCacheSizeMB: Long = Defaults.DISK_CACHE_SIZE_MB,
        val crossfadeDurationMs: Int = Defaults.CROSSFADE_DURATION_MS,
        val placeholderFadeIn: Boolean = Defaults.PLACEHOLDER_FADE_IN,
        val errorFadeIn: Boolean = Defaults.ERROR_FADE_IN,
        val allowHardwareBitmaps: Boolean = Defaults.ALLOW_HARDWARE_BITMAPS,
        val allowRgb565: Boolean = Defaults.ALLOW_RGB_565,
        val enableLogging: Boolean = false,
        val customOkHttpClient: OkHttpClient? = null
    )
    
    // 当前配置
    private var currentConfig: ImageLoadConfig = ImageLoadConfig()
    
    /**
     * 设置图片加载配置
     */
    fun setConfig(config: ImageLoadConfig) {
        currentConfig = config
        LogManager.tag(LogConfig.Tags.IMAGE).i("图片加载配置已更新: $config")
    }
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): ImageLoadConfig = currentConfig
    
    /**
     * 获取内存缓存大小（字节）
     */
    fun getMemoryCacheSize(context: Context): Long {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        return (maxMemory * currentConfig.memoryCacheSizePercent).toLong()
    }
    
    /**
     * 获取磁盘缓存大小（字节）
     */
    fun getDiskCacheSize(): Long {
        return currentConfig.diskCacheSizeMB * 1024 * 1024
    }
    
    /**
     * 获取缓存目录名称
     */
    fun getCacheDirectoryName(): String = "image_cache"
    
    /**
     * 是否启用调试模式
     */
    fun isDebugMode(): Boolean = currentConfig.environment == Environment.DEBUG
    
    /**
     * 是否启用日志
     */
    fun isLoggingEnabled(): Boolean = currentConfig.enableLogging || isDebugMode()
    
    /**
     * 创建默认的调试配置
     */
    fun createDebugConfig(okHttpClient: OkHttpClient? = null): ImageLoadConfig {
        return ImageLoadConfig(
            environment = Environment.DEBUG,
            imageQuality = ImageQuality.HIGH,
            cacheStrategy = CacheStrategy.MEMORY_AND_DISK,
            enableLogging = true,
            customOkHttpClient = okHttpClient
        )
    }
    
    /**
     * 创建默认的发布配置
     */
    fun createReleaseConfig(okHttpClient: OkHttpClient? = null): ImageLoadConfig {
        return ImageLoadConfig(
            environment = Environment.RELEASE,
            imageQuality = ImageQuality.MEDIUM,
            cacheStrategy = CacheStrategy.MEMORY_AND_DISK,
            enableLogging = false,
            customOkHttpClient = okHttpClient
        )
    }
    
    /**
     * 创建性能优先配置
     */
    fun createPerformanceConfig(okHttpClient: OkHttpClient? = null): ImageLoadConfig {
        return ImageLoadConfig(
            environment = Environment.RELEASE,
            imageQuality = ImageQuality.LOW,
            cacheStrategy = CacheStrategy.MEMORY_ONLY,
            memoryCacheSizePercent = 0.15,
            allowRgb565 = true,
            crossfadeDurationMs = 150,
            customOkHttpClient = okHttpClient
        )
    }
    
    /**
     * 创建质量优先配置
     */
    fun createQualityConfig(okHttpClient: OkHttpClient? = null): ImageLoadConfig {
        return ImageLoadConfig(
            environment = Environment.RELEASE,
            imageQuality = ImageQuality.HIGH,
            cacheStrategy = CacheStrategy.MEMORY_AND_DISK,
            memoryCacheSizePercent = 0.35,
            diskCacheSizeMB = 500L,
            allowRgb565 = false,
            customOkHttpClient = okHttpClient
        )
    }
}
