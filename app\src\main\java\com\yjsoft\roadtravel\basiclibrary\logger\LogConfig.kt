package com.yjsoft.roadtravel.basiclibrary.logger

import java.io.File

/**
 * 日志配置类
 * 统一管理日志相关的配置参数
 */
object LogConfig {
    
    /**
     * 日志级别枚举
     */
    enum class LogLevel(val priority: Int) {
        VERBOSE(2),
        DEBUG(3),
        INFO(4),
        WARN(5),
        ERROR(6),
        ASSERT(7)
    }
    
    /**
     * 日志标签配置
     */
    object Tags {
        const val DEFAULT = "RoadTravel"
        const val NETWORK = "Network"
        const val DATABASE = "Database"
        const val UI = "UI"
        const val BUSINESS = "Business"
        const val CRASH = "Crash"
        const val IMAGE = "Image"
    }
    
    /**
     * 文件日志配置
     */
    object FileLog {
        // 日志文件目录名
        const val LOG_DIR_NAME = "logs"
        
        // 日志文件名前缀
        const val LOG_FILE_PREFIX = "roadtravel"
        
        // 日志文件扩展名
        const val LOG_FILE_EXTENSION = ".log"
        
        // 单个日志文件最大大小（MB）
        const val MAX_FILE_SIZE_MB = 10L
        
        // 最大保留日志文件数量
        const val MAX_FILE_COUNT = 7
        
        // 日志文件编码
        const val FILE_ENCODING = "UTF-8"
        
        // 是否启用文件日志
        var isEnabled = true
        
        /**
         * 获取日志文件目录
         */
        fun getLogDir(appDir: File): File {
            return File(appDir, LOG_DIR_NAME).apply {
                if (!exists()) {
                    mkdirs()
                }
            }
        }
        
        /**
         * 生成日志文件名
         */
        fun generateLogFileName(timestamp: Long = System.currentTimeMillis()): String {
            val dateStr = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
                .format(java.util.Date(timestamp))
            return "${LOG_FILE_PREFIX}_${dateStr}${LOG_FILE_EXTENSION}"
        }
    }
    
    /**
     * 远程日志配置
     */
    object RemoteLog {
        // 是否启用远程日志上传
        var isEnabled = false
        
        // 远程日志服务器地址
        var serverUrl = ""
        
        // 批量上传大小
        const val BATCH_SIZE = 50
        
        // 上传间隔（毫秒）
        const val UPLOAD_INTERVAL_MS = 30000L
        
        // 最大重试次数
        const val MAX_RETRY_COUNT = 3
    }
    
    /**
     * 崩溃日志配置
     */
    object CrashLog {
        // 是否启用崩溃日志收集
        var isEnabled = true
        
        // 崩溃日志文件名前缀
        const val CRASH_FILE_PREFIX = "crash"
        
        // 最大保留崩溃日志文件数量
        const val MAX_CRASH_FILE_COUNT = 10
    }
    
    /**
     * 调试配置
     */
    object Debug {
        // 是否显示线程信息
        var showThreadInfo = true
        
        // 是否显示方法信息
        var showMethodInfo = true
        
        // 是否显示文件位置信息
        var showFileInfo = true
        
        // 方法调用栈深度
        var methodStackDepth = 5
        
        // 日志显示模式
        var logDisplayMode = LogDisplayMode.CALLER_INFO_ONLY
        
        // 是否在标签中显示详细信息（当使用STANDARD模式时）
        var showDetailedTag = false
    }
    
    /**
     * 日志显示模式
     */
    enum class LogDisplayMode {
        STANDARD,           // 标准模式：显示标签 + 消息
        CALLER_INFO_ONLY,   // 仅调用者信息模式：只在消息中显示调用者
        MINIMAL             // 最简模式：不显示额外信息
    }
    
    /**
     * 性能配置
     */
    object Performance {
        // 是否启用异步日志写入
        var enableAsyncLogging = true
        
        // 日志缓冲区大小
        const val BUFFER_SIZE = 8192
        
        // 刷新间隔（毫秒）
        const val FLUSH_INTERVAL_MS = 1000L
    }
    
    /**
     * 根据构建类型获取默认日志级别
     */
    fun getDefaultLogLevel(isDebug: Boolean): LogLevel {
        return if (isDebug) LogLevel.DEBUG else LogLevel.INFO
    }
    
    /**
     * 检查日志级别是否应该输出
     */
    fun shouldLog(currentLevel: LogLevel, targetLevel: LogLevel): Boolean {
        return targetLevel.priority >= currentLevel.priority
    }
}
