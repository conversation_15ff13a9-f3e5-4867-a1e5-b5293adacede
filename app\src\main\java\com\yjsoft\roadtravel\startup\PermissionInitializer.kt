package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionConfig

/**
 * 权限框架初始化器
 * 使用androidx.startup进行权限框架的初始化
 * 
 * 优先级：中（依赖LogManager）
 * 依赖：LogManager
 */
class PermissionInitializer : Initializer<PermissionConfig> {

    companion object {
        private const val TAG = "PermissionInitializer"
    }

    /**
     * 创建并初始化权限框架
     * @param context 应用上下文
     * @return PermissionConfig实例
     */
    override fun create(context: Context): PermissionConfig {
        try {
            // 初始化权限框架默认配置
            PermissionConfig.initializeDefaults()

            LogManager.d(TAG, "权限框架初始化成功")
            LogManager.d(TAG, "权限框架配置 - 默认策略: ${PermissionConfig.defaultRequestStrategy.name}")
            
            LogManager.i(TAG, "权限框架通过androidx.startup初始化成功")
            
            return PermissionConfig
            
        } catch (e: Exception) {
            LogManager.e(TAG, "权限框架初始化失败", e)
            throw e
        }
    }

    /**
     * 返回此初始化器的依赖项
     * 权限框架依赖LogManager进行日志记录
     * @return 依赖LogManagerInitializer
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return listOf(LogManagerInitializer::class.java)
    }
}
