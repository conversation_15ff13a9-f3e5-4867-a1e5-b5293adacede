package com.yjsoft.roadtravel.basiclibrary.network.models

/**
 * 网络请求结果封装类
 * 用于统一处理网络请求的成功、失败和加载状态
 */
sealed class NetworkResult<out T> {
    
    /**
     * 加载中状态
     */
    object Loading : NetworkResult<Nothing>()
    
    /**
     * 成功状态
     * @param data 成功返回的数据
     */
    data class Success<T>(val data: T) : NetworkResult<T>()
    
    /**
     * 失败状态
     * @param error 错误信息
     */
    data class Error(val error: ApiError) : NetworkResult<Nothing>()
    
    /**
     * 判断是否为成功状态
     */
    fun isSuccess(): Boolean = this is Success
    
    /**
     * 判断是否为失败状态
     */
    fun isError(): Boolean = this is Error
    
    /**
     * 判断是否为加载状态
     */
    fun isLoading(): Boolean = this is Loading
    
    /**
     * 获取成功数据，失败时返回null
     */
    fun getDataOrNull(): T? {
        return when (this) {
            is Success -> data
            else -> null
        }
    }
    
    /**
     * 获取错误信息，非错误状态返回null
     */
    fun getErrorOrNull(): ApiError? {
        return when (this) {
            is Error -> error
            else -> null
        }
    }
    
    /**
     * 对成功结果进行转换
     */
    inline fun <R> map(transform: (T) -> R): NetworkResult<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Error -> this
            is Loading -> this
        }
    }
    
    /**
     * 对成功结果进行异步转换
     */
    suspend inline fun <R> mapSuspend(crossinline transform: suspend (T) -> R): NetworkResult<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Error -> this
            is Loading -> this
        }
    }
    
    /**
     * 执行成功回调
     */
    inline fun onSuccess(action: (T) -> Unit): NetworkResult<T> {
        if (this is Success) {
            action(data)
        }
        return this
    }
    
    /**
     * 执行失败回调
     */
    inline fun onError(action: (ApiError) -> Unit): NetworkResult<T> {
        if (this is Error) {
            action(error)
        }
        return this
    }
    
    /**
     * 执行加载回调
     */
    inline fun onLoading(action: () -> Unit): NetworkResult<T> {
        if (this is Loading) {
            action()
        }
        return this
    }
    
    companion object {
        /**
         * 创建成功结果
         */
        fun <T> success(data: T): NetworkResult<T> = Success(data)
        
        /**
         * 创建失败结果
         */
        fun <T> error(error: ApiError): NetworkResult<T> = Error(error)
        
        /**
         * 创建失败结果（通过异常）
         */
        fun <T> error(exception: ApiException): NetworkResult<T> = Error(exception.toApiError())
        
        /**
         * 创建加载结果
         */
        fun <T> loading(): NetworkResult<T> = Loading
        
        /**
         * 从ApiResponse创建NetworkResult
         */
        fun <T> fromApiResponse(response: ApiResponse<T>): NetworkResult<T> {
            return if (response.isSuccess()) {
                response.data?.let { success(it) } ?: error(ApiError.parseError("数据为空"))
            } else {
                error(ApiError.businessError(response.code, response.message))
            }
        }
    }
}
