package com.yjsoft.roadtravel.basiclibrary.mvvm.extensions

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import retrofit2.HttpException
import retrofit2.Response
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * Repository扩展函数
 * 
 * 功能：
 * - 简化Repository中的常用操作
 * - 提供安全的网络请求封装
 * - 统一的响应处理
 * - 缓存策略工具
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

// ========== 网络请求扩展 ==========

/**
 * 安全的API调用扩展（适用于所有Repository）
 */
suspend fun <T> safeApiCall(
    tag: String = "Repository",
    apiCall: suspend () -> T
): Resource<T> {
    return withContext(Dispatchers.IO) {
        try {
            LogManager.d("[%s] 开始API调用", tag)
            val result = apiCall()
            LogManager.d("[%s] API调用成功", tag)
            Resource.Success(result)
        } catch (exception: Exception) {
            LogManager.e(exception, "[%s] API调用失败", tag)
            handleApiException(exception)
        }
    }
}

/**
 * 处理Retrofit Response
 */
suspend fun <T> Response<T>.toResource(): Resource<T> {
    return try {
        if (isSuccessful) {
            val body = body()
            if (body != null) {
                Resource.Success(body)
            } else {
                Resource.Error(message = "响应体为空")
            }
        } else {
            Resource.Error(
                message = "请求失败: ${code()} ${message()}",
                code = code()
            )
        }
    } catch (e: Exception) {
        Resource.Error(e, e.message ?: "响应处理失败")
    }
}

/**
 * 将挂起函数转换为Resource Flow
 */
fun <T> suspendToResourceFlow(
    tag: String = "Repository",
    block: suspend () -> T
): Flow<Resource<T>> = flow {
    emit(Resource.Loading())
    try {
        LogManager.d("[%s] 开始执行操作", tag)
        val result = block()
        LogManager.d("[%s] 操作执行成功", tag)
        emit(Resource.Success(result))
    } catch (exception: Exception) {
        LogManager.e(exception, "[%s] 操作执行失败", tag)
        emit(handleApiException(exception))
    }
}.flowOn(Dispatchers.IO)

// ========== 缓存扩展 ==========

/**
 * 简化的网络绑定资源
 */
fun <T> networkBoundResource(
    tag: String = "Repository",
    query: suspend () -> T?,
    fetch: suspend () -> T,
    saveFetchResult: suspend (T) -> Unit = {},
    shouldFetch: (T?) -> Boolean = { true }
): Flow<Resource<T>> = flow {
    emit(Resource.Loading())
    
    // 查询本地数据
    val localData = query()
    LogManager.d("[%s] 查询本地数据: %s", tag, localData != null)
    
    // 如果有本地数据，先发射
    if (localData != null) {
        emit(Resource.Loading(localData))
    }
    
    // 判断是否需要从网络获取
    if (shouldFetch(localData)) {
        try {
            LogManager.d("[%s] 从网络获取数据", tag)
            val networkData = fetch()
            saveFetchResult(networkData)
            
            // 重新查询本地数据
            val newLocalData = query()
            if (newLocalData != null) {
                emit(Resource.Success(newLocalData))
            } else {
                emit(Resource.Success(networkData))
            }
        } catch (exception: Exception) {
            LogManager.e(exception, "[%s] 网络获取失败", tag)
            
            // 如果有本地数据，使用本地数据
            if (localData != null) {
                emit(Resource.Success(localData))
            } else {
                emit(handleApiException(exception))
            }
        }
    } else {
        // 不需要网络获取，使用本地数据
        if (localData != null) {
            emit(Resource.Success(localData))
        } else {
            emit(Resource.Error(message = "无数据"))
        }
    }
}.catch { exception ->
    LogManager.e(exception, "[%s] 网络绑定资源异常", tag)
    val apiException = exception as? Exception ?: Exception(exception)
    emit(handleApiException(apiException))
}.flowOn(Dispatchers.IO)

// ========== 数据转换扩展 ==========

/**
 * 映射Resource数据
 */
fun <T, R> Flow<Resource<T>>.mapResource(transform: (T) -> R): Flow<Resource<R>> {
    return this.map { resource ->
        when (resource) {
            is Resource.Success -> Resource.Success(transform(resource.data))
            is Resource.Error -> Resource.Error(resource.exception, resource.message, resource.code)
            is Resource.Loading -> Resource.Loading(resource.data?.let(transform))
        }
    }
}

/**
 * 过滤Resource数据
 */
fun <T> Flow<Resource<T>>.filterResource(predicate: (T) -> Boolean): Flow<Resource<T>> {
    return this.map { resource ->
        when (resource) {
            is Resource.Success -> {
                if (predicate(resource.data)) {
                    resource
                } else {
                    Resource.Error(message = "数据不符合条件")
                }
            }
            else -> resource
        }
    }
}

/**
 * 合并多个Resource Flow
 */
fun <T1, T2, R> combineResources(
    flow1: Flow<Resource<T1>>,
    flow2: Flow<Resource<T2>>,
    transform: (T1, T2) -> R
): Flow<Resource<R>> = flow {
    var data1: T1?
    var data2: T2? = null
    var hasError = false
    var errorResource: Resource.Error? = null
    
    // 这里简化实现，实际可以使用combine操作符
    flow1.collect { resource1 ->
        when (resource1) {
            is Resource.Success -> {
                data1 = resource1.data
                if (data2 != null && !hasError) {
                    emit(Resource.Success(transform(data1!!, data2)))
                }
            }
            is Resource.Error -> {
                hasError = true
                errorResource = resource1
                emit(resource1)
            }
            is Resource.Loading -> {
                emit(Resource.Loading())
            }
        }
    }
}

// ========== 缓存策略扩展 ==========

/**
 * 仅缓存策略
 */
fun <T> cacheOnlyResource(
    tag: String = "Repository",
    query: suspend () -> T?
): Flow<Resource<T>> = flow {
    try {
        val data = query()
        if (data != null) {
            emit(Resource.Success(data))
        } else {
            emit(Resource.Error(message = "无缓存数据"))
        }
    } catch (e: Exception) {
        LogManager.e(e, "[%s] 缓存查询失败", tag)
        emit(Resource.Error(e, e.message ?: "缓存查询失败"))
    }
}.flowOn(Dispatchers.IO)

/**
 * 仅网络策略
 */
fun <T> networkOnlyResource(
    tag: String = "Repository",
    fetch: suspend () -> T,
    saveToCache: suspend (T) -> Unit = {}
): Flow<Resource<T>> = flow {
    emit(Resource.Loading())
    try {
        LogManager.d("[%s] 仅从网络获取数据", tag)
        val data = fetch()
        saveToCache(data)
        emit(Resource.Success(data))
    } catch (e: Exception) {
        LogManager.e(e, "[%s] 网络获取失败", tag)
        emit(handleApiException(e))
    }
}.flowOn(Dispatchers.IO)

// ========== 分页扩展 ==========

/**
 * 分页数据Resource
 */
data class PagedResource<T>(
    val data: List<T>,
    val hasMore: Boolean,
    val nextPage: Int?
)

/**
 * 分页网络请求
 */
fun <T> pagedNetworkResource(
    tag: String = "Repository",
    page: Int,
    pageSize: Int,
    fetch: suspend (Int, Int) -> List<T>,
    hasMore: (List<T>) -> Boolean = { it.size >= pageSize }
): Flow<Resource<PagedResource<T>>> = flow {
    emit(Resource.Loading())
    try {
        LogManager.d("[%s] 获取分页数据: page=%d, size=%d", tag, page, pageSize)
        val data = fetch(page, pageSize)
        val hasMoreData = hasMore(data)
        val nextPage = if (hasMoreData) page + 1 else null
        
        val pagedData = PagedResource(
            data = data,
            hasMore = hasMoreData,
            nextPage = nextPage
        )
        
        emit(Resource.Success(pagedData))
    } catch (e: Exception) {
        LogManager.e(e, "[%s] 分页数据获取失败", tag)
        emit(handleApiException<PagedResource<T>>(e))
    }
}.flowOn(Dispatchers.IO)

// ========== 工具函数 ==========

/**
 * 处理API异常（内部函数）
 */
private fun <T> handleApiException(exception: Exception): Resource<T> {
    return when (exception) {
        is HttpException -> {
            val code = exception.code()
            val message = when (code) {
                400 -> "请求参数错误"
                401 -> "未授权访问"
                403 -> "禁止访问"
                404 -> "请求的资源不存在"
                408 -> "请求超时"
                500 -> "服务器内部错误"
                502 -> "网关错误"
                503 -> "服务不可用"
                else -> "网络请求失败 (${code})"
            }
            Resource.Error(exception, message, code)
        }
        is UnknownHostException -> {
            Resource.Error(exception, "网络连接失败，请检查网络设置")
        }
        is ConnectException -> {
            Resource.Error(exception, "无法连接到服务器")
        }
        is SocketTimeoutException -> {
            Resource.Error(exception, "网络请求超时")
        }
        is IOException -> {
            Resource.Error(exception, "网络连接异常")
        }
        else -> {
            Resource.Error(exception, exception.message ?: "未知错误")
        }
    }
}

/**
 * 记录Repository操作
 */
fun logRepositoryOperation(tag: String, operation: String, details: String = "") {
    LogManager.d("[%s] %s %s", tag, operation, details)
}

/**
 * 创建空的Resource成功状态
 */
fun <T> emptySuccess(): Resource<T> where T : Collection<*> {
    @Suppress("UNCHECKED_CAST")
    return Resource.Success(emptyList<Any>() as T)
}

/**
 * 检查Resource是否为空数据
 */
fun <T> Resource<T>.isEmptyData(): Boolean where T : Collection<*> {
    return this is Resource.Success && data.isEmpty()
}
