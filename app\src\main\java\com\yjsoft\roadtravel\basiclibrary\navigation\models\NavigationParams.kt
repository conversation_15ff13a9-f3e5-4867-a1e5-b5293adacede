package com.yjsoft.roadtravel.basiclibrary.navigation.models

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

/**
 * 导航参数传递数据类
 * 支持多种类型的参数传递
 */
@Serializable
data class NavigationParams(
    val stringParams: Map<String, String> = emptyMap(),
    val intParams: Map<String, Int> = emptyMap(),
    val booleanParams: Map<String, Boolean> = emptyMap(),
    val longParams: Map<String, Long> = emptyMap(),
    val doubleParams: Map<String, Double> = emptyMap(),
    val jsonParams: Map<String, JsonElement> = emptyMap()
) {
    
    companion object {
        fun builder() = NavigationParamsBuilder()
        
        fun empty() = NavigationParams()
    }
    
    /**
     * 获取字符串参数
     */
    fun getString(key: String, defaultValue: String = ""): String {
        return stringParams[key] ?: defaultValue
    }
    
    /**
     * 获取整数参数
     */
    fun getInt(key: String, defaultValue: Int = 0): Int {
        return intParams[key] ?: defaultValue
    }
    
    /**
     * 获取布尔参数
     */
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return booleanParams[key] ?: defaultValue
    }
    
    /**
     * 获取长整型参数
     */
    fun getLong(key: String, defaultValue: Long = 0L): Long {
        return longParams[key] ?: defaultValue
    }
    
    /**
     * 获取双精度参数
     */
    fun getDouble(key: String, defaultValue: Double = 0.0): Double {
        return doubleParams[key] ?: defaultValue
    }
    
    /**
     * 获取JSON参数
     */
    fun getJsonElement(key: String): JsonElement? {
        return jsonParams[key]
    }
    
    /**
     * 检查是否包含某个参数
     */
    fun hasParam(key: String): Boolean {
        return stringParams.containsKey(key) ||
                intParams.containsKey(key) ||
                booleanParams.containsKey(key) ||
                longParams.containsKey(key) ||
                doubleParams.containsKey(key) ||
                jsonParams.containsKey(key)
    }
}

/**
 * 导航参数构建器
 */
class NavigationParamsBuilder {
    private val stringParams = mutableMapOf<String, String>()
    private val intParams = mutableMapOf<String, Int>()
    private val booleanParams = mutableMapOf<String, Boolean>()
    private val longParams = mutableMapOf<String, Long>()
    private val doubleParams = mutableMapOf<String, Double>()
    private val jsonParams = mutableMapOf<String, JsonElement>()
    
    fun putString(key: String, value: String) = apply {
        stringParams[key] = value
    }
    
    fun putInt(key: String, value: Int) = apply {
        intParams[key] = value
    }
    
    fun putBoolean(key: String, value: Boolean) = apply {
        booleanParams[key] = value
    }
    
    fun putLong(key: String, value: Long) = apply {
        longParams[key] = value
    }
    
    fun putDouble(key: String, value: Double) = apply {
        doubleParams[key] = value
    }
    
    fun putJsonElement(key: String, value: JsonElement) = apply {
        jsonParams[key] = value
    }
    
    fun build(): NavigationParams {
        return NavigationParams(
            stringParams = stringParams.toMap(),
            intParams = intParams.toMap(),
            booleanParams = booleanParams.toMap(),
            longParams = longParams.toMap(),
            doubleParams = doubleParams.toMap(),
            jsonParams = jsonParams.toMap()
        )
    }
} 