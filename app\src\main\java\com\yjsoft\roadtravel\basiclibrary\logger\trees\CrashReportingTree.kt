package com.yjsoft.roadtravel.basiclibrary.logger.trees

import android.content.Context
import android.os.Build
import android.util.Log
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import timber.log.Timber
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 崩溃日志收集Tree
 * 专门收集ERROR级别以上的日志和崩溃信息
 */
class CrashReportingTree(private val context: Context) : Timber.Tree() {
    
    private val crashDir: File = File(context.filesDir, "crashes").apply {
        if (!exists()) {
            mkdirs()
        }
    }
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    private val fileNameFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    init {
        // 设置全局异常处理器
        setupGlobalExceptionHandler()
        
        // 清理旧的崩溃日志
        cleanupOldCrashFiles()
    }
    
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 只记录ERROR级别以上的日志
        if (priority < Log.ERROR) {
            return
        }
        
        try {
            val crashInfo = CrashInfo(
                timestamp = System.currentTimeMillis(),
                priority = priority,
                tag = tag ?: "Unknown",
                message = message,
                throwable = t,
                deviceInfo = getDeviceInfo(),
                appInfo = getAppInfo()
            )
            
            writeCrashLog(crashInfo)
            
        } catch (e: Exception) {
            // 避免在崩溃日志系统中再次崩溃
            System.err.println("写入崩溃日志失败: ${e.message}")
        }
    }
    
    /**
     * 崩溃信息数据类
     */
    private data class CrashInfo(
        val timestamp: Long,
        val priority: Int,
        val tag: String,
        val message: String,
        val throwable: Throwable?,
        val deviceInfo: DeviceInfo,
        val appInfo: AppInfo
    )
    
    /**
     * 设备信息数据类
     */
    private data class DeviceInfo(
        val manufacturer: String,
        val model: String,
        val androidVersion: String,
        val apiLevel: Int,
        val brand: String,
        val device: String,
        val display: String,
        val fingerprint: String,
        val hardware: String,
        val host: String,
        val id: String,
        val product: String,
        val tags: String,
        val type: String,
        val user: String
    )
    
    /**
     * 应用信息数据类
     */
    private data class AppInfo(
        val packageName: String,
        val versionName: String,
        val versionCode: Long,
        val buildType: String,
        val isDebug: Boolean
    )
    
    /**
     * 设置全局异常处理器
     */
    private fun setupGlobalExceptionHandler() {
        val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
        
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            try {
                // 记录崩溃日志
                val crashInfo = CrashInfo(
                    timestamp = System.currentTimeMillis(),
                    priority = Log.ERROR,
                    tag = LogConfig.Tags.CRASH,
                    message = "应用崩溃 - 线程: ${thread.name}",
                    throwable = exception,
                    deviceInfo = getDeviceInfo(),
                    appInfo = getAppInfo()
                )
                
                writeCrashLog(crashInfo, isFatal = true)
                
            } catch (e: Exception) {
                System.err.println("记录崩溃日志失败: ${e.message}")
            } finally {
                // 调用原始的异常处理器
                defaultHandler?.uncaughtException(thread, exception)
            }
        }
    }
    
    /**
     * 写入崩溃日志
     */
    private fun writeCrashLog(crashInfo: CrashInfo, isFatal: Boolean = false) {
        try {
            val fileName = if (isFatal) {
                "${LogConfig.CrashLog.CRASH_FILE_PREFIX}_fatal_${fileNameFormat.format(Date(crashInfo.timestamp))}.log"
            } else {
                "${LogConfig.CrashLog.CRASH_FILE_PREFIX}_error_${fileNameFormat.format(Date(crashInfo.timestamp))}.log"
            }
            
            val crashFile = File(crashDir, fileName)
            val content = formatCrashLog(crashInfo, isFatal)
            
            FileWriter(crashFile, false).use { writer ->
                writer.write(content)
                writer.flush()
            }
            
        } catch (e: IOException) {
            System.err.println("写入崩溃日志文件失败: ${e.message}")
        }
    }
    
    /**
     * 格式化崩溃日志
     */
    private fun formatCrashLog(crashInfo: CrashInfo, isFatal: Boolean): String {
        val builder = StringBuilder()
        
        // 基本信息
        builder.append("==================== ${if (isFatal) "FATAL CRASH" else "ERROR"} ====================\n")
        builder.append("时间: ${dateFormat.format(Date(crashInfo.timestamp))}\n")
        builder.append("级别: ${getPriorityString(crashInfo.priority)}\n")
        builder.append("标签: ${crashInfo.tag}\n")
        builder.append("消息: ${crashInfo.message}\n")
        builder.append("\n")
        
        // 应用信息
        builder.append("==================== 应用信息 ====================\n")
        builder.append("包名: ${crashInfo.appInfo.packageName}\n")
        builder.append("版本名: ${crashInfo.appInfo.versionName}\n")
        builder.append("版本号: ${crashInfo.appInfo.versionCode}\n")
        builder.append("构建类型: ${crashInfo.appInfo.buildType}\n")
        builder.append("调试模式: ${crashInfo.appInfo.isDebug}\n")
        builder.append("\n")
        
        // 设备信息
        builder.append("==================== 设备信息 ====================\n")
        builder.append("制造商: ${crashInfo.deviceInfo.manufacturer}\n")
        builder.append("型号: ${crashInfo.deviceInfo.model}\n")
        builder.append("品牌: ${crashInfo.deviceInfo.brand}\n")
        builder.append("设备: ${crashInfo.deviceInfo.device}\n")
        builder.append("Android版本: ${crashInfo.deviceInfo.androidVersion}\n")
        builder.append("API级别: ${crashInfo.deviceInfo.apiLevel}\n")
        builder.append("显示: ${crashInfo.deviceInfo.display}\n")
        builder.append("指纹: ${crashInfo.deviceInfo.fingerprint}\n")
        builder.append("\n")
        
        // 异常信息
        crashInfo.throwable?.let { throwable ->
            builder.append("==================== 异常信息 ====================\n")
            builder.append("异常类型: ${throwable.javaClass.name}\n")
            builder.append("异常消息: ${throwable.message ?: "无"}\n")
            builder.append("\n")
            builder.append("堆栈跟踪:\n")
            builder.append(Log.getStackTraceString(throwable))
        }
        
        return builder.toString()
    }
    
    /**
     * 获取设备信息
     */
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            device = Build.DEVICE,
            display = Build.DISPLAY,
            fingerprint = Build.FINGERPRINT,
            hardware = Build.HARDWARE,
            host = Build.HOST,
            id = Build.ID,
            product = Build.PRODUCT,
            tags = Build.TAGS,
            type = Build.TYPE,
            user = Build.USER
        )
    }
    
    /**
     * 获取应用信息
     */
    private fun getAppInfo(): AppInfo {
        val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)

        // 获取构建类型和调试状态，提供备用方案
        val buildType = try {
            // 尝试通过反射获取BuildConfig
            val buildConfigClass = Class.forName("${context.packageName}.BuildConfig")
            val buildTypeField = buildConfigClass.getField("BUILD_TYPE")
            buildTypeField.get(null) as String
        } catch (e: Exception) {
            // 如果BuildConfig不可用，通过其他方式判断
            if (isDebugBuild()) "debug" else "release"
        }

        val isDebug = try {
            // 尝试通过反射获取BuildConfig
            val buildConfigClass = Class.forName("${context.packageName}.BuildConfig")
            val debugField = buildConfigClass.getField("DEBUG")
            debugField.getBoolean(null)
        } catch (e: Exception) {
            // 备用方案：通过应用信息判断
            isDebugBuild()
        }

        return AppInfo(
            packageName = context.packageName,
            versionName = packageInfo.versionName ?: "Unknown",
            versionCode = packageInfo.longVersionCode,
            buildType = buildType,
            isDebug = isDebug
        )
    }

    /**
     * 判断是否为调试构建（备用方案）
     */
    private fun isDebugBuild(): Boolean {
        return try {
            val applicationInfo = context.applicationInfo
            (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取优先级字符串
     */
    private fun getPriorityString(priority: Int): String {
        return when (priority) {
            Log.ERROR -> "ERROR"
            Log.ASSERT -> "ASSERT"
            else -> "UNKNOWN"
        }
    }
    
    /**
     * 清理旧的崩溃日志文件
     */
    private fun cleanupOldCrashFiles() {
        try {
            val crashFiles = crashDir.listFiles { _, name ->
                name.startsWith(LogConfig.CrashLog.CRASH_FILE_PREFIX)
            }
            
            if (crashFiles != null && crashFiles.size > LogConfig.CrashLog.MAX_CRASH_FILE_COUNT) {
                crashFiles.sortBy { it.lastModified() }
                val filesToDelete = crashFiles.size - LogConfig.CrashLog.MAX_CRASH_FILE_COUNT
                
                for (i in 0 until filesToDelete) {
                    if (crashFiles[i].delete()) {
                        System.out.println("删除旧崩溃日志: ${crashFiles[i].name}")
                    }
                }
            }
        } catch (e: Exception) {
            System.err.println("清理旧崩溃日志失败: ${e.message}")
        }
    }
    
    /**
     * 获取所有崩溃日志文件
     */
    fun getCrashFiles(): List<File> {
        return crashDir.listFiles { _, name ->
            name.startsWith(LogConfig.CrashLog.CRASH_FILE_PREFIX)
        }?.toList() ?: emptyList()
    }
}
