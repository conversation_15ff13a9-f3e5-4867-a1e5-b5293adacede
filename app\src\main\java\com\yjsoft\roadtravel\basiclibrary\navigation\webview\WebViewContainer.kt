package com.yjsoft.roadtravel.basiclibrary.navigation.webview

import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebSettings
import android.webkit.JavascriptInterface
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationConstants

/**
 * WebView容器组件
 * 支持H5页面显示、JavaScript Bridge和原生功能调用
 */
@Composable
fun WebViewContainer(
    url: String,
    modifier: Modifier = Modifier,
    onPageStarted: (String) -> Unit = {},
    onPageFinished: (String) -> Unit = {},
    onPageError: (String, String) -> Unit = { _, _ -> },
    onProgressChanged: (Int) -> Unit = {},
    onTitleChanged: (String) -> Unit = {},
    enableJavaScriptBridge: Boolean = true,
    javaScriptBridge: JavaScriptBridge? = null,
    onWebViewReady: (WebView) -> Unit = {}
) {
    val context = LocalContext.current
    
    // 创建并配置WebView
    val webView = remember {
        WebView(context).apply {
            configureWebView(this, enableJavaScriptBridge, javaScriptBridge)
            // 通知WebView已准备就绪
            onWebViewReady(this)
        }
    }
    
    AndroidView(
        factory = { webView },
        modifier = modifier.fillMaxSize(),
        update = { view ->
            view.webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(view: WebView?, request: android.webkit.WebResourceRequest?): Boolean {
                    // 确保所有URL导航都被WebView处理，以便正确建立历史记录
                    val url = request?.url?.toString()
                    LogManager.d("WebViewContainer", "URL导航: $url")
                    
                    // 让WebView处理导航，确保历史记录正确
                    return false // 返回false表示让WebView自己处理URL加载
                }
                
                override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                    url?.let { onPageStarted(it) }
                    LogManager.d("WebViewContainer", "页面开始加载: $url, canGoBack: ${view?.canGoBack()}")
                }
                
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    url?.let { onPageFinished(it) }
                    LogManager.d("WebViewContainer", "页面加载完成: $url, canGoBack: ${view?.canGoBack()}")
                }
                
                override fun onReceivedError(
                    view: WebView?,
                    request: android.webkit.WebResourceRequest?,
                    error: android.webkit.WebResourceError?
                ) {
                    super.onReceivedError(view, request, error)
                    
                    val errorMsg = error?.description?.toString() ?: "未知错误"
                    val errorCode = error?.errorCode ?: -1
                    val errorUrl = request?.url?.toString() ?: url
                    val isMainFrame = request?.isForMainFrame ?: false
                    
                    // 只处理主页面的加载错误，忽略资源文件错误
                    if (isMainFrame) {
                        onPageError(errorUrl, errorMsg)
                        LogManager.e("WebViewContainer", "主页面加载错误: $errorMsg (错误码: $errorCode), URL: $errorUrl")
                    } else {
                        // 资源文件错误只记录日志，不影响页面状态
                        LogManager.w("WebViewContainer", "资源加载错误: $errorMsg (错误码: $errorCode), 资源URL: $errorUrl")
                    }
                }
            }
            
            view.webChromeClient = object : android.webkit.WebChromeClient() {
                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                    onProgressChanged(newProgress)
                }
                
                override fun onReceivedTitle(view: WebView?, title: String?) {
                    super.onReceivedTitle(view, title)
                    // 动态更新页面标题
                    if (!title.isNullOrEmpty() && title != "about:blank") {
                        onTitleChanged(title)
                        LogManager.d("WebViewContainer", "页面标题更新: $title")
                    }
                }
            }
            
            // 加载URL
            if (view.url != url) {
                view.loadUrl(url)
            }
        }
    )
}

/**
 * 配置WebView设置
 */
private fun configureWebView(
    webView: WebView,
    enableJavaScriptBridge: Boolean,
    javaScriptBridge: JavaScriptBridge?
) {
    webView.settings.apply {
        // 启用JavaScript
        javaScriptEnabled = true
        
        // 设置User-Agent
        userAgentString = "$userAgentString ${NavigationConstants.WebView.USER_AGENT_SUFFIX}"
        
        // 启用DOM存储
        domStorageEnabled = true
        
        // 启用数据库
        databaseEnabled = true
        
        // 设置缓存模式
        cacheMode = WebSettings.LOAD_DEFAULT
        
        // 支持缩放
        setSupportZoom(true)
        builtInZoomControls = true
        displayZoomControls = false
        
        // 自适应屏幕
        useWideViewPort = true
        loadWithOverviewMode = true
        
        // 支持内容重新布局
        layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
        
        // 允许文件访问
        allowFileAccess = true
        
        // 允许通用文件访问
        allowUniversalAccessFromFileURLs = true
        allowFileAccessFromFileURLs = true
        
        // 支持多窗口
        setSupportMultipleWindows(false)
        
        // 设置文本编码
        defaultTextEncodingName = "UTF-8"
        
        // 设置字体大小
        defaultFontSize = 16
        
        // 混合内容模式（允许HTTPS页面加载HTTP资源）
        mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
    }
    
    // 添加JavaScript Bridge
    if (enableJavaScriptBridge && javaScriptBridge != null) {
        webView.addJavascriptInterface(
            javaScriptBridge,
            NavigationConstants.WebView.JAVASCRIPT_INTERFACE_NAME
        )
        LogManager.d("WebViewContainer", "JavaScript Bridge已添加")
    }
}

/**
 * 简化的WebView组件
 * 用于快速集成基础WebView功能
 */
@Composable
fun SimpleWebView(
    url: String,
    modifier: Modifier = Modifier,
    onPageFinished: (String) -> Unit = {},
    onTitleChanged: (String) -> Unit = {}
) {
    WebViewContainer(
        url = url,
        modifier = modifier,
        onPageFinished = onPageFinished,
        onTitleChanged = onTitleChanged,
        enableJavaScriptBridge = false
    )
}

/**
 * 带加载进度的WebView组件
 */
@Composable
fun WebViewWithProgress(
    url: String,
    modifier: Modifier = Modifier,
    onProgressChanged: (Int) -> Unit = {},
    onPageFinished: (String) -> Unit = {},
    onTitleChanged: (String) -> Unit = {}
) {
    WebViewContainer(
        url = url,
        modifier = modifier,
        onProgressChanged = onProgressChanged,
        onPageFinished = onPageFinished,
        onTitleChanged = onTitleChanged,
        enableJavaScriptBridge = true
    )
} 