package com.yjsoft.roadtravel.basiclibrary.payment.core

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType

/**
 * 支付策略接口
 * 定义所有支付方式必须实现的核心方法
 */
interface PaymentStrategy {
    
    /**
     * 获取支付类型
     */
    fun getPaymentType(): PaymentType
    
    /**
     * 检查支付方式是否可用
     * @param context 上下文
     * @return 是否可用
     */
    suspend fun isAvailable(context: Context): Boolean
    
    /**
     * 初始化支付SDK
     * @param context 上下文
     * @return 是否初始化成功
     */
    suspend fun initialize(context: Context): Boolean
    
    /**
     * 执行支付
     * @param context 上下文
     * @param request 支付请求
     * @return 支付结果
     */
    suspend fun pay(context: Context, request: PaymentRequest): PaymentResult
    
    /**
     * 检查支付配置是否完整
     * @return 配置是否完整
     */
    fun isConfigured(): Boolean
    
    /**
     * 获取SDK版本信息
     * @return SDK版本
     */
    fun getSdkVersion(): String
    
    /**
     * 清理资源
     */
    fun cleanup()
}

/**
 * 支付状态监听器
 * 用于监听支付状态变化
 */
interface PaymentStateListener {

    /**
     * 支付状态变化
     * @param oldState 旧状态
     * @param newState 新状态
     */
    fun onStateChanged(oldState: com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState, newState: com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState)
}
