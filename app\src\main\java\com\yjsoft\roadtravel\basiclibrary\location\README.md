# 高德地图定位集成

## 概述

本模块提供了完整的高德地图定位功能集成，包括权限管理、定位服务、状态管理和UI组件。

## 主要特性

### 🎯 核心功能
- **高德地图定位集成** - 封装高德定位API，提供响应式数据流
- **权限自动管理** - 与权限框架无缝集成，自动处理位置权限申请
- **多种定位模式** - 支持高精度、省电、仅设备等多种定位模式
- **智能错误处理** - 完善的错误处理和用户友好的提示信息

### 🎨 Compose集成
- **声明式定位组件** - 提供易用的Compose组件
- **响应式状态管理** - 基于StateFlow的定位状态监听
- **丰富的UI组件** - 定位状态指示器、信息卡片等
- **生命周期感知** - 自动处理定位服务的启动和停止

## 快速开始

### 1. 基础使用

```kotlin
@Composable
fun LocationDemo() {
    LocationRequester(
        config = LocationConfig.singleHighAccuracy(),
        includeBackgroundLocation = false
    ) { locationState ->
        val scope = rememberCoroutineScope()
        
        // 显示定位信息
        LocationInfoCard(
            locationData = locationState.locationData,
            locationState = locationState.locationState,
            onRetryClick = { locationState.retryLocation() }
        )
        
        // 定位按钮
        Button(
            onClick = {
                scope.launch {
                    locationState.startLocation()
                }
            }
        ) {
            Text("开始定位")
        }
    }
}
```

### 2. 单次定位

```kotlin
@Composable
fun SingleLocationDemo() {
    val scope = rememberCoroutineScope()
    var locationResult by remember { mutableStateOf<LocationData?>(null) }
    
    SingleLocationRequester { locationState ->
        Button(
            onClick = {
                scope.launch {
                    val result = locationState.getSingleLocation()
                    result.onSuccess { locationData ->
                        locationResult = locationData
                    }
                }
            }
        ) {
            Text("获取当前位置")
        }
    }
}
```

### 3. 连续定位

```kotlin
@Composable
fun ContinuousLocationDemo() {
    ContinuousLocationRequester(
        intervalMillis = 5000L,
        onLocationUpdate = { locationData ->
            // 处理位置更新
            locationData?.let {
                println("新位置: ${it.shortAddress}")
            }
        }
    ) { locationState ->
        // UI内容
    }
}
```

### 4. ViewModel集成

```kotlin
class MyLocationViewModel(application: Application) : LocationViewModel(application) {
    
    fun startLocationTracking() {
        requestLocationAndStart(
            config = LocationConfig.continuous(5000L),
            includeBackgroundLocation = false
        )
    }
    
    fun getCurrentLocation() {
        getSingleLocation { result ->
            result.onSuccess { locationData ->
                // 处理定位结果
            }
        }
    }
}
```

## 定位配置

### 预定义配置

```kotlin
// 高精度单次定位
val config = LocationConfig.singleHighAccuracy()

// 连续定位
val config = LocationConfig.continuous(intervalMillis = 5000L)

// 省电模式
val config = LocationConfig.batterySaving()

// 仅GPS模式
val config = LocationConfig.deviceOnly()
```

### 自定义配置

```kotlin
val config = LocationConfig(
    locationMode = LocationMode.HIGH_ACCURACY,
    interval = 3000L,
    isOnceLocation = false,
    needAddress = true,
    isMockEnable = false,
    gpsFirstTimeout = 20000L
)
```

## UI组件

### 定位状态指示器

```kotlin
@Composable
fun StatusDemo() {
    LocationStatusIndicator(
        locationState = locationState,
        showLabel = true
    )
}
```

### 定位信息卡片

```kotlin
@Composable
fun InfoCardDemo() {
    LocationInfoCard(
        locationData = locationData,
        locationState = locationState,
        onRetryClick = { /* 重试逻辑 */ }
    )
}
```

### 简洁信息显示

```kotlin
@Composable
fun CompactDemo() {
    LocationInfoCompact(
        locationData = locationData,
        locationState = locationState
    )
}
```

## 错误处理

框架提供了完善的错误处理机制：

```kotlin
LocationRequester { locationState ->
    when (locationState.locationState.status) {
        LocationStatus.PERMISSION_DENIED -> {
            // 处理权限被拒绝
        }
        LocationStatus.GPS_DISABLED -> {
            // 处理GPS未开启
        }
        LocationStatus.NETWORK_UNAVAILABLE -> {
            // 处理网络不可用
        }
        LocationStatus.FAILED -> {
            // 处理定位失败
            val error = locationState.locationState.error
            Text(error?.userFriendlyMessage ?: "定位失败")
        }
    }
}
```

## 权限集成

定位功能与权限框架无缝集成：

```kotlin
// 自动申请权限并开始定位
locationViewModel.requestLocationAndStart(
    config = LocationConfig.singleHighAccuracy(),
    includeBackgroundLocation = false
)

// 检查权限状态
if (locationViewModel.isPermissionGroupGranted(PermissionGroups.LOCATION_GROUP)) {
    // 有权限，可以开始定位
    locationViewModel.startLocation()
} else {
    // 没有权限，需要申请
    locationViewModel.requestLocationPermissions()
}
```

## 最佳实践

1. **合理选择定位模式**
   - 单次定位使用高精度模式
   - 连续定位根据需求选择省电或高精度模式
   - 导航应用使用高精度模式

2. **及时停止定位**
   - 在不需要定位时及时停止，节省电量
   - 利用生命周期自动管理

3. **处理定位错误**
   - 提供用户友好的错误提示
   - 支持重试机制
   - 引导用户解决权限和设置问题

4. **优化用户体验**
   - 显示定位状态和进度
   - 提供精度信息
   - 缓存最后一次定位结果

## 注意事项

1. 确保在AndroidManifest.xml中声明了位置权限
2. 高德地图API Key必须正确配置
3. 定位服务需要网络连接（除纯GPS模式外）
4. 在室内环境GPS精度可能较差，建议使用网络定位
5. 连续定位会消耗较多电量，请合理设置定位间隔
