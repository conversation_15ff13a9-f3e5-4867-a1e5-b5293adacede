package com.yjsoft.roadtravel.basiclibrary.location.viewmodel

import android.Manifest
import android.app.Application
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationConfig
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationMode
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationError
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationState
import com.yjsoft.roadtravel.basiclibrary.location.service.LocationManager
import com.yjsoft.roadtravel.basiclibrary.permission.viewmodel.PermissionViewModel
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionResult
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 定位ViewModel
 * 集成权限管理和定位服务
 */
class LocationViewModel(application: Application) : PermissionViewModel(application) {

    companion object {
        private const val TAG = "LocationViewModel"
    }

    // 定位管理器
    private val locationManager = LocationManager.getInstance(application)

    // 定位状态持有者
    private val locationStateHolder = LocationStateHolder(application, locationManager)

    // 是否已初始化
    private var _isInitialized = false
    val isInitialized: Boolean get() = _isInitialized

    // 定位状态流
    val locationState: StateFlow<LocationState> = locationStateHolder.locationState

    // 定位数据流
    val locationData: StateFlow<LocationData?> = locationStateHolder.latestLocationData

    // 是否正在定位流
    val isLocating: StateFlow<Boolean> = locationStateHolder.isLocating

    // 定位错误流
    val locationError: StateFlow<LocationError?> = locationStateHolder.locationError

    // 是否有有效定位数据流
    val hasValidLocation: StateFlow<Boolean> = locationStateHolder.hasValidLocation

    // 组合定位数据流
    val combinedLocationData: StateFlow<LocationDataCombined> =
        locationStateHolder.combineLocationData()

    /**
     * 初始化定位服务
     */
    fun initializeLocationService(config: LocationConfig = LocationConfig.default()) {
        if (_isInitialized) {
            LogManager.d("$TAG 定位服务已初始化，跳过")
            return
        }

        LogManager.d("$TAG 初始化定位服务")

        viewModelScope.launch {
            val result = locationStateHolder.initialize(config)
            if (result.isFailure) {
                LogManager.e("$TAG 定位服务初始化失败", result.exceptionOrNull())
            }
        }
        _isInitialized = true
    }

    /**
     * 请求位置权限并开始定位
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun requestLocationAndStart(
        config: LocationConfig? = null,
        includeBackgroundLocation: Boolean = false
    ) {
        LogManager.d("$TAG 请求位置权限并开始定位")

        // 先请求位置权限
        requestLocationPermissions(includeBackgroundLocation) { result ->
            when (result) {
                is PermissionResult.Granted -> {
                    LogManager.d("$TAG 位置权限已授予，开始定位")
                    startLocation(config)
                }

                is PermissionResult.Denied -> {
                    LogManager.w("$TAG 位置权限被拒绝: ${result.deniedPermissions}")
                    locationStateHolder.handlePermissionDenied(result.deniedPermissions)
                }

                else -> {
                    LogManager.w("$TAG 位置权限申请失败: $result")
                    locationStateHolder.handlePermissionDenied(emptyList())
                }
            }
        }
    }

    /**
     * 开始定位（需要先确保有权限）
     */
    fun startLocation(config: LocationConfig? = null) {
        LogManager.d("$TAG 开始定位")

        // 检查基本位置权限（不包括后台位置权限）
        if (!hasBasicLocationPermissions()) {
            LogManager.w("$TAG 缺少位置权限")
            val basicPermissions = listOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
            locationStateHolder.handlePermissionDenied(basicPermissions)
            return
        }

        locationStateHolder.startLocation(config)
    }

    /**
     * 停止定位
     */
    fun stopLocation() {
        LogManager.d("$TAG 停止定位")
        locationStateHolder.stopLocation()
    }

    /**
     * 单次定位
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun getSingleLocation(
        config: LocationConfig = LocationConfig.singleHighAccuracy(),
        includeBackgroundLocation: Boolean = false,
        callback: ((Result<LocationData>) -> Unit)? = null
    ) {
        LogManager.d("$TAG 执行单次定位")

        // 先请求位置权限
        requestLocationPermissions(includeBackgroundLocation) { result ->
            when (result) {
                is PermissionResult.Granted -> {
                    viewModelScope.launch {
                        val locationResult = locationStateHolder.getSingleLocation(config)
                        callback?.invoke(locationResult)
                    }
                }

                is PermissionResult.Denied -> {
                    LogManager.w("$TAG 位置权限被拒绝，无法执行单次定位")
                    locationStateHolder.handlePermissionDenied(result.deniedPermissions)
                    callback?.invoke(Result.failure(Exception("位置权限被拒绝")))
                }

                else -> {
                    LogManager.w("$TAG 位置权限申请失败，无法执行单次定位")
                    callback?.invoke(Result.failure(Exception("位置权限申请失败")))
                }
            }
        }
    }

    /**
     * 重试定位
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun retryLocation() {
        LogManager.d("$TAG 重试定位")

        val currentState = locationState.value
        if (currentState.needsPermission) {
            // 如果需要权限，重新申请权限
            requestLocationAndStart()
        } else {
            // 否则直接重试定位
            locationStateHolder.retryLocation()
        }
    }

    /**
     * 获取最后一次定位结果
     */
    fun getLastKnownLocation(): LocationData? {
        return locationStateHolder.getLastKnownLocation()
    }

    /**
     * 设置定位配置
     */
    fun setLocationConfig(config: LocationConfig) {
        locationStateHolder.setLocationConfig(config)
    }

    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): LocationConfig {
        return locationStateHolder.getCurrentConfig()
    }

    /**
     * 检查定位服务是否可用
     */
    fun isLocationServiceAvailable(): Boolean {
        return locationStateHolder.isLocationServiceAvailable()
    }

    /**
     * 清除定位状态
     */
    fun clearLocationState() {
        locationStateHolder.clearLocationState()
    }

    /**
     * 处理GPS设置
     */
    fun handleGPSSettings() {
        // 这里可以引导用户到GPS设置页面
        LogManager.d("$TAG 引导用户开启GPS")
        locationStateHolder.handleGPSDisabled()
    }

    /**
     * 处理网络设置
     */
    fun handleNetworkSettings() {
        // 这里可以引导用户到网络设置页面
        LogManager.d("$TAG 引导用户检查网络")
        locationStateHolder.handleNetworkUnavailable()
    }

    /**
     * 切换定位模式
     */
    fun switchLocationMode(mode: LocationMode) {
        LogManager.d("$TAG 切换定位模式: ${mode.description}")

        val currentConfig = getCurrentConfig()
        val newConfig = currentConfig.copy(locationMode = mode)
        setLocationConfig(newConfig)

        // 如果正在定位，重新开始定位以应用新配置
        if (isLocating.value) {
            stopLocation()
            startLocation(newConfig)
        }
    }

    /**
     * 设置定位间隔
     */
    fun setLocationInterval(intervalMillis: Long) {
        LogManager.d("$TAG 设置定位间隔: ${intervalMillis}ms")

        val currentConfig = getCurrentConfig()
        val newConfig = currentConfig.copy(interval = intervalMillis)
        setLocationConfig(newConfig)
    }

    /**
     * 检查基本位置权限是否已授予
     */
    private fun hasBasicLocationPermissions(): Boolean {
        val basicPermissions = listOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
        val granted = arePermissionsGranted(basicPermissions)
        LogManager.d("$TAG 基本位置权限检查结果: $granted")

        // 逐个检查权限状态
        basicPermissions.forEach { permission ->
            val isGranted = isPermissionGranted(permission)
            LogManager.d("$TAG 权限 $permission: $isGranted")
        }

        return granted
    }

    override fun onCleared() {
        super.onCleared()
        LogManager.d("$TAG LocationViewModel已清理")
        stopLocation()
    }
}
