package com.yjsoft.roadtravel.ui.activities.login

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatLoginStateEvent
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatLoginStateManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import com.yjsoft.roadtravel.ui.theme.LoginButtonYellow
import com.yjsoft.roadtravel.ui.theme.LoginTextGray
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest

@AndroidEntryPoint
class LoginActivity : BaseActivity() {

    // 移除Hilt依赖注入，使用普通ViewModel创建

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setupContent {
            LoginScreen(
                    onLoginSuccess = { navigateToMain() },
                    onShowToast = { message -> showToast(message) }
            )
        }
    }

    /** 导航到主页面 */
    private fun navigateToMain() {
        // 使用NavigationManager跳转到主页面并清除任务栈
        // navigateToMainAndClearTask(NavigationConstants.ActivityClasses.MAIN)
        finish()
    }
}

@Composable
fun LoginScreen(onLoginSuccess: () -> Unit = {}, onShowToast: (String) -> Unit = {}) {
    val context = LocalContext.current
    val viewModel: LoginViewModel = viewModel()

    // 初始化ViewModel
    LaunchedEffect(Unit) { viewModel.initialize(context) }

    // 观察状态
    val loginState by viewModel.loginState.collectAsState()
    val isWeChatInstalled by viewModel.isWeChatInstalled.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    // 处理登录结果
    LaunchedEffect(loginState) {
        val currentState = loginState
        when (currentState) {
            is UiState.Success -> {
                when (val result = currentState.data) {
                    is LoginResult.WeChatSuccess -> {
                        onShowToast("微信登录成功，欢迎 ${result.userInfo.nickname}")
                        onLoginSuccess()
                    }
                    is LoginResult.OtherSuccess -> {
                        onShowToast("登录成功")
                        onLoginSuccess()
                    }
                }
            }
            is UiState.Error -> {
                onShowToast(currentState.message)
            }
            else -> {}
        }
    }

    // 处理错误消息
    LaunchedEffect(errorMessage) {
        errorMessage?.let { message ->
            onShowToast(message)
            viewModel.clearError()
        }
    }

    // 监听微信登录状态事件
    LaunchedEffect(Unit) {
        try {
            WeChatLoginStateManager.getInstance().loginStateEvents.collectLatest { event ->
                when (event) {
                    is WeChatLoginStateEvent.LoginFailed -> {
                        viewModel.resetLoginState()
                        onShowToast(event.errorMessage)
                    }
                    is WeChatLoginStateEvent.ServiceNotInitialized -> {
                        viewModel.resetLoginState()
                        onShowToast("微信登录服务未初始化，请重试")
                    }
                    is WeChatLoginStateEvent.LoginCancelled -> {
                        viewModel.resetLoginState()
                        onShowToast("用户取消登录")
                    }
                    is WeChatLoginStateEvent.LoginSuccess -> {
                        // 这个事件通常由正常流程处理，这里可以作为备用
                        onShowToast("微信登录成功，欢迎 ${event.userInfo.nickname}")
                        onLoginSuccess()
                    }
                }
            }
        } catch (e: Exception) {
            // 如果状态管理器获取失败，记录错误但不影响正常流程
            LogManager.e("LoginScreen %s", "监听微信登录状态事件失败", e)
        }
    }
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片
        Image(
                painter = painterResource(id = R.drawable.login_bg),
                contentDescription = "登录背景",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
        )

        // 距离顶部118dp的login_text图片，尺寸322*167
        Image(
                painter = painterResource(id = R.drawable.login_text),
                contentDescription = "登录标题",
                modifier =
                        Modifier.size(322.dp, 167.dp).align(Alignment.TopCenter).offset(y = 118.dp)
        )

        // 距离底部82dp的login_bottom_logo图片，尺寸115*30
        Image(
                painter = painterResource(id = R.drawable.login_bottom_logo),
                contentDescription = "底部Logo",
                modifier =
                        Modifier.size(115.dp, 30.dp)
                                .align(Alignment.BottomCenter)
                                .offset(y = (-82).dp)
        )

        // 距离底部34dp的公司名称文字
        Text(
                text = "上海义简信息科技有限公司",
                color = LoginTextGray,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.align(Alignment.BottomCenter).offset(y = (-34).dp)
        )

        // 距离底部222dp的微信登录按钮
        Button(
                onClick = {
                    if (isWeChatInstalled) {
                        viewModel.loginWithWeChat(context)
                    } else {
                        onShowToast("请先安装微信客户端")
                    }
                },
                enabled = loginState !is UiState.Loading,
                modifier =
                        Modifier.align(Alignment.BottomCenter)
                                .offset(y = (-222).dp)
                                .padding(horizontal = 38.dp)
                                .fillMaxWidth(),
                shape = RoundedCornerShape(24.dp),
                colors =
                        ButtonDefaults.buttonColors(
                                containerColor = LoginButtonYellow,
                                contentColor = Color.Black
                        ),
                contentPadding = PaddingValues(vertical = 14.dp)
        ) {
            Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
            ) {
                // 显示加载状态或微信图标
                if (loginState is UiState.Loading) {
                    CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = Color.Black,
                            strokeWidth = 2.dp
                    )
                } else {
                    // 微信图标，尺寸25*20
                    Image(
                            painter = painterResource(id = R.drawable.login_wx),
                            contentDescription = "微信图标",
                            modifier = Modifier.size(25.dp, 20.dp)
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 微信登录文字
                Text(
                        text = if (loginState is UiState.Loading) "登录中..." else "微信登入",
                        fontSize = 14.sp,
                        color = Color.Black
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LoginScreenPreview() {
    LoginScreen()
}
