package com.yjsoft.roadtravel.basiclibrary.di.modules

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 测试模块
 * 用于验证Hilt是否正常工作
 */
@Module
@InstallIn(SingletonComponent::class)
object TestModule {
    
    @Provides
    @Singleton
    fun provideTestString(): String {
        return "Hilt Test Success!"
    }
}
