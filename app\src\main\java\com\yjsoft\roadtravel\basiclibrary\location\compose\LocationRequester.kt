package com.yjsoft.roadtravel.basiclibrary.location.compose

import android.os.Build
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.suspendCancellableCoroutine
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationConfig
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationError
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationState
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationStatus
import com.yjsoft.roadtravel.basiclibrary.location.viewmodel.LocationDataCombined
import com.yjsoft.roadtravel.basiclibrary.location.viewmodel.LocationViewModel
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * 定位请求状态
 */
@Stable
class LocationRequesterState(
    private val locationViewModel: LocationViewModel
) {
    // 提供访问ViewModel的方法，让Composable函数来收集状态
    fun getLocationViewModel(): LocationViewModel = locationViewModel
    
    /**
     * 开始定位
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    suspend fun startLocation(config: LocationConfig? = null, includeBackgroundLocation: Boolean = false) {
        locationViewModel.requestLocationAndStart(config, includeBackgroundLocation)
    }
    
    /**
     * 停止定位
     */
    fun stopLocation() {
        locationViewModel.stopLocation()
    }
    
    /**
     * 单次定位
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    suspend fun getSingleLocation(
        config: LocationConfig = LocationConfig.singleHighAccuracy(),
        includeBackgroundLocation: Boolean = false
    ): Result<LocationData> {
        return suspendCancellableCoroutine { continuation ->
            locationViewModel.getSingleLocation(config, includeBackgroundLocation) { result ->
                if (continuation.isActive) {
                    continuation.resumeWith(Result.success(result))
                }
            }
        }
    }
    
    /**
     * 重试定位
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    fun retryLocation() {
        locationViewModel.retryLocation()
    }
    
    /**
     * 获取最后一次定位结果
     */
    fun getLastKnownLocation(): LocationData? {
        return locationViewModel.getLastKnownLocation()
    }
    
    /**
     * 清除定位状态
     */
    fun clearLocationState() {
        locationViewModel.clearLocationState()
    }
}

/**
 * 创建定位请求状态
 */
@Composable
fun rememberLocationRequesterState(): LocationRequesterState {
    val context = LocalContext.current
    val activity = context as ComponentActivity
    val locationViewModel: LocationViewModel = viewModel()

    // 只初始化定位服务，权限管理器已经在MainActivity中初始化
    LaunchedEffect(locationViewModel) {
        locationViewModel.initializeLocationService()
    }

    return remember { LocationRequesterState(locationViewModel) }
}

/**
 * 定位状态数据类
 */
@Stable
data class LocationStateData(
    val locationState: LocationState,
    val locationData: LocationData?,
    val isLocating: Boolean,
    val locationError: LocationError?,
    val hasValidLocation: Boolean,
    val combinedData: LocationDataCombined
)

/**
 * 收集定位状态的Composable函数
 */
@Composable
fun collectLocationState(state: LocationRequesterState): LocationStateData {
    val locationViewModel = state.getLocationViewModel()
    val locationState by locationViewModel.locationState.collectAsState()
    val locationData by locationViewModel.locationData.collectAsState()
    val isLocating by locationViewModel.isLocating.collectAsState()
    val locationError by locationViewModel.locationError.collectAsState()
    val hasValidLocation by locationViewModel.hasValidLocation.collectAsState()
    val combinedData by locationViewModel.combinedLocationData.collectAsState()

    return LocationStateData(
        locationState = locationState,
        locationData = locationData,
        isLocating = isLocating,
        locationError = locationError,
        hasValidLocation = hasValidLocation,
        combinedData = combinedData
    )
}

/**
 * 定位请求器Composable
 * 主要的定位申请组件
 */
@Composable
fun LocationRequester(
    config: LocationConfig = LocationConfig.default(),
    includeBackgroundLocation: Boolean = false,
    onLocationResult: (LocationState) -> Unit = {},
    content: @Composable (LocationRequesterState) -> Unit
) {
    val state = rememberLocationRequesterState()
    val stateData = collectLocationState(state)

    // 监听定位状态变化
    LaunchedEffect(stateData.locationState) {
        onLocationResult(stateData.locationState)
    }

    content(state)
}

/**
 * 单次定位请求器
 */
@Composable
fun SingleLocationRequester(
    config: LocationConfig = LocationConfig.singleHighAccuracy(),
    includeBackgroundLocation: Boolean = false,
    onLocationResult: (Result<LocationData>) -> Unit = {},
    content: @Composable (LocationRequesterState) -> Unit
) {
    val state = rememberLocationRequesterState()
    val scope = rememberCoroutineScope()
    
    content(state)
}

/**
 * 连续定位请求器
 */
@Composable
fun ContinuousLocationRequester(
    intervalMillis: Long = 5000L,
    includeBackgroundLocation: Boolean = false,
    onLocationUpdate: (LocationData?) -> Unit = {},
    content: @Composable (LocationRequesterState) -> Unit
) {
    val state = rememberLocationRequesterState()
    val stateData = collectLocationState(state)

    // 监听定位数据变化
    LaunchedEffect(stateData.locationData) {
        onLocationUpdate(stateData.locationData)
    }

    content(state)
}

/**
 * 高精度定位请求器
 */
@Composable
fun HighAccuracyLocationRequester(
    onLocationResult: (LocationState) -> Unit = {},
    content: @Composable (LocationRequesterState) -> Unit
) {
    LocationRequester(
        config = LocationConfig.singleHighAccuracy(),
        includeBackgroundLocation = false,
        onLocationResult = onLocationResult,
        content = content
    )
}

/**
 * 省电模式定位请求器
 */
@Composable
fun BatterySavingLocationRequester(
    onLocationResult: (LocationState) -> Unit = {},
    content: @Composable (LocationRequesterState) -> Unit
) {
    LocationRequester(
        config = LocationConfig.batterySaving(),
        includeBackgroundLocation = false,
        onLocationResult = onLocationResult,
        content = content
    )
}

/**
 * 定位权限和服务检查器
 */
@Composable
fun LocationServiceChecker(
    onPermissionDenied: () -> Unit = {},
    onGPSDisabled: () -> Unit = {},
    onNetworkUnavailable: () -> Unit = {},
    onServiceReady: () -> Unit = {},
    content: @Composable () -> Unit
) {
    val state = rememberLocationRequesterState()
    val stateData = collectLocationState(state)

    // 监听定位状态并处理各种情况
    LaunchedEffect(stateData.locationState) {
        when {
            stateData.locationState.needsPermission -> {
                LogManager.d("LocationServiceChecker", "需要位置权限")
                onPermissionDenied()
            }
            stateData.locationState.needsGPS -> {
                LogManager.d("LocationServiceChecker", "需要开启GPS")
                onGPSDisabled()
            }
            stateData.locationState.needsNetwork -> {
                LogManager.d("LocationServiceChecker", "需要网络连接")
                onNetworkUnavailable()
            }
            stateData.locationState.status == LocationStatus.SUCCESS -> {
                LogManager.d("LocationServiceChecker", "定位服务就绪")
                onServiceReady()
            }
        }
    }

    content()
}

/**
 * 定位数据监听器
 */
@Composable
fun LocationDataListener(
    onLocationChanged: (LocationData?) -> Unit,
    onLocationError: (LocationError?) -> Unit = {},
    onLocationStateChanged: (LocationState) -> Unit = {}
) {
    val state = rememberLocationRequesterState()
    val stateData = collectLocationState(state)

    // 监听定位数据变化
    LaunchedEffect(stateData.locationData) {
        onLocationChanged(stateData.locationData)
    }

    // 监听定位错误
    LaunchedEffect(stateData.locationError) {
        onLocationError(stateData.locationError)
    }

    // 监听定位状态变化
    LaunchedEffect(stateData.locationState) {
        onLocationStateChanged(stateData.locationState)
    }
}

/**
 * 定位工具函数
 */
object LocationUtils {
    
    /**
     * 检查定位数据是否有效
     */
    fun isLocationValid(locationData: LocationData?): Boolean {
        return locationData?.isValid == true
    }
    
    /**
     * 格式化定位精度
     */
    fun formatAccuracy(accuracy: Float): String {
        return when {
            accuracy <= 10 -> "高精度 (${accuracy.toInt()}m)"
            accuracy <= 50 -> "中等精度 (${accuracy.toInt()}m)"
            accuracy <= 100 -> "低精度 (${accuracy.toInt()}m)"
            else -> "精度较差 (${accuracy.toInt()}m)"
        }
    }
    
    /**
     * 格式化坐标
     */
    fun formatCoordinate(latitude: Double, longitude: Double): String {
        return "%.6f, %.6f".format(latitude, longitude)
    }
    
    /**
     * 计算两点间距离
     */
    fun calculateDistance(from: LocationData, to: LocationData): Float {
        return from.distanceTo(to)
    }
}
