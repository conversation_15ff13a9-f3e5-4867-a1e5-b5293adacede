package com.yjsoft.roadtravel.basiclibrary.image.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImagePainter
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager
import com.yjsoft.roadtravel.basiclibrary.image.loader.ImageLoaderFactory

/**
 * 头像图片组件
 * 专门用于显示用户头像，支持圆形、方形等多种样式
 */
@Composable
fun AvatarImage(
    url: String?,
    contentDescription: String?,
    size: Dp,
    modifier: Modifier = Modifier,
    shape: Shape = CircleShape,
    placeholder: Painter? = null,
    error: Painter? = null,
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent,
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    crossfadeEnabled: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    val context = LocalContext.current
    val avatarImageLoader = ImageManager.getImageLoader(
        context,
        ImageLoaderFactory.LoaderType.AVATAR
    )
    
    Box(
        modifier = modifier
            .size(size)
            .clip(shape)
            .background(backgroundColor)
            .then(
                if (borderWidth > 0.dp) {
                    Modifier.border(borderWidth, borderColor, shape)
                } else {
                    Modifier
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        if (!url.isNullOrBlank()) {
            NetworkImage(
                url = url,
                contentDescription = contentDescription,
                modifier = Modifier.fillMaxSize(),
                placeholder = placeholder,
                error = error,
                contentScale = ContentScale.Crop,
                crossfadeEnabled = crossfadeEnabled,
                onLoading = onLoading,
                onSuccess = onSuccess,
                onError = onError
            )
        } else {
            // 显示默认占位符
            placeholder?.let { painter ->
                androidx.compose.foundation.Image(
                    painter = painter,
                    contentDescription = contentDescription,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
            }
        }
    }
}

/**
 * 带文字占位符的头像组件
 */
@Composable
fun AvatarImageWithText(
    url: String?,
    name: String,
    contentDescription: String?,
    size: Dp,
    modifier: Modifier = Modifier,
    shape: Shape = CircleShape,
    placeholder: Painter? = null,
    error: Painter? = null,
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent,
    backgroundColor: Color = MaterialTheme.colorScheme.primary,
    textColor: Color = MaterialTheme.colorScheme.onPrimary,
    fontSize: TextUnit = (size.value / 3).sp,
    crossfadeEnabled: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(shape)
            .background(backgroundColor)
            .then(
                if (borderWidth > 0.dp) {
                    Modifier.border(borderWidth, borderColor, shape)
                } else {
                    Modifier
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        if (!url.isNullOrBlank()) {
            NetworkImage(
                url = url,
                contentDescription = contentDescription,
                modifier = Modifier.fillMaxSize(),
                placeholder = placeholder,
                error = error,
                contentScale = ContentScale.Crop,
                crossfadeEnabled = crossfadeEnabled,
                onLoading = onLoading,
                onSuccess = onSuccess,
                onError = { state ->
                    onError?.invoke(state)
                    // 加载失败时显示文字占位符
                }
            )
        } else {
            // 显示文字占位符
            Text(
                text = getAvatarText(name),
                color = textColor,
                fontSize = fontSize,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * 圆形头像组件
 */
@Composable
fun CircleAvatarImage(
    url: String?,
    contentDescription: String?,
    size: Dp,
    modifier: Modifier = Modifier,
    placeholder: Painter? = null,
    error: Painter? = null,
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent,
    backgroundColor: Color = MaterialTheme.colorScheme.surfaceVariant,
    crossfadeEnabled: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    AvatarImage(
        url = url,
        contentDescription = contentDescription,
        size = size,
        modifier = modifier,
        shape = CircleShape,
        placeholder = placeholder,
        error = error,
        borderWidth = borderWidth,
        borderColor = borderColor,
        backgroundColor = backgroundColor,
        crossfadeEnabled = crossfadeEnabled,
        onLoading = onLoading,
        onSuccess = onSuccess,
        onError = onError
    )
}

/**
 * 带文字的圆形头像组件
 */
@Composable
fun CircleAvatarImageWithText(
    url: String?,
    name: String,
    contentDescription: String?,
    size: Dp,
    modifier: Modifier = Modifier,
    placeholder: Painter? = null,
    error: Painter? = null,
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent,
    backgroundColor: Color = MaterialTheme.colorScheme.primary,
    textColor: Color = MaterialTheme.colorScheme.onPrimary,
    fontSize: TextUnit = (size.value / 3).sp,
    crossfadeEnabled: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    AvatarImageWithText(
        url = url,
        name = name,
        contentDescription = contentDescription,
        size = size,
        modifier = modifier,
        shape = CircleShape,
        placeholder = placeholder,
        error = error,
        borderWidth = borderWidth,
        borderColor = borderColor,
        backgroundColor = backgroundColor,
        textColor = textColor,
        fontSize = fontSize,
        crossfadeEnabled = crossfadeEnabled,
        onLoading = onLoading,
        onSuccess = onSuccess,
        onError = onError
    )
}

/**
 * 头像尺寸预设
 */
object AvatarSize {
    val ExtraSmall = 24.dp
    val Small = 32.dp
    val Medium = 48.dp
    val Large = 64.dp
    val ExtraLarge = 96.dp
}

/**
 * 获取头像文字占位符
 * 从姓名中提取首字母作为头像文字
 */
private fun getAvatarText(name: String): String {
    return when {
        name.isBlank() -> "?"
        name.length == 1 -> name.uppercase()
        name.contains(" ") -> {
            // 如果包含空格，取每个单词的首字母
            name.split(" ")
                .take(2)
                .mapNotNull { it.firstOrNull()?.toString()?.uppercase() }
                .joinToString("")
        }
        name.length >= 2 -> {
            // 如果是中文名或其他，取前两个字符
            name.take(2).uppercase()
        }
        else -> name.first().toString().uppercase()
    }
}

/**
 * 头像颜色生成器
 * 根据用户名生成一致的背景色
 */
object AvatarColorGenerator {
    
    private val colors = listOf(
        Color(0xFF1976D2), // Blue
        Color(0xFF388E3C), // Green
        Color(0xFFF57C00), // Orange
        Color(0xFFD32F2F), // Red
        Color(0xFF7B1FA2), // Purple
        Color(0xFF00796B), // Teal
        Color(0xFF455A64), // Blue Grey
        Color(0xFF5D4037), // Brown
        Color(0xFFE64A19), // Deep Orange
        Color(0xFF303F9F)  // Indigo
    )
    
    /**
     * 根据名称生成背景色
     */
    fun generateBackgroundColor(name: String): Color {
        val hash = name.hashCode()
        val index = kotlin.math.abs(hash) % colors.size
        return colors[index]
    }
    
    /**
     * 根据背景色生成对应的文字颜色
     */
    fun generateTextColor(backgroundColor: Color): Color {
        // 简单的对比度计算，实际项目中可以使用更复杂的算法
        return Color.White
    }
}

/**
 * 智能头像组件
 * 自动根据名称生成背景色和文字颜色
 */
@Composable
fun SmartAvatarImage(
    url: String?,
    name: String,
    contentDescription: String?,
    size: Dp,
    modifier: Modifier = Modifier,
    shape: Shape = CircleShape,
    placeholder: Painter? = null,
    error: Painter? = null,
    borderWidth: Dp = 0.dp,
    borderColor: Color = Color.Transparent,
    crossfadeEnabled: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    val backgroundColor = AvatarColorGenerator.generateBackgroundColor(name)
    val textColor = AvatarColorGenerator.generateTextColor(backgroundColor)
    
    AvatarImageWithText(
        url = url,
        name = name,
        contentDescription = contentDescription,
        size = size,
        modifier = modifier,
        shape = shape,
        placeholder = placeholder,
        error = error,
        borderWidth = borderWidth,
        borderColor = borderColor,
        backgroundColor = backgroundColor,
        textColor = textColor,
        crossfadeEnabled = crossfadeEnabled,
        onLoading = onLoading,
        onSuccess = onSuccess,
        onError = onError
    )
}
