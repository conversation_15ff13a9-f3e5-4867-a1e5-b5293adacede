package com.yjsoft.roadtravel.basiclibrary.payment.strategies.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelpay.PayResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConfig
import com.yjsoft.roadtravel.basiclibrary.payment.strategies.WeChatPayStrategy

/**
 * 微信支付回调Activity
 * 必须放在包名.wxapi包下，且类名必须为WXPayEntryActivity
 * 用于接收微信支付的回调结果
 */
class WXPayEntryActivity : Activity(), IWXAPIEventHandler {
    
    companion object {
        private const val TAG = "WXPayEntryActivity %s"
    }
    
    private var wxApi: IWXAPI? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // 初始化微信API
            val appId = PaymentConfig.wechatAppId
            if (appId.isNullOrBlank()) {
                LogManager.e(TAG, "微信AppId未配置")
                finish()
                return
            }
            
            wxApi = WXAPIFactory.createWXAPI(this, appId, false)
            
            // 处理微信回调
            val handled = wxApi?.handleIntent(intent, this) ?: false
            if (!handled) {
                LogManager.w(TAG, "微信回调处理失败")
                finish()
            }
            
        } catch (e: Exception) {
            LogManager.e(TAG, "WXPayEntryActivity初始化异常", e)
            finish()
        }
    }
    
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        
        try {
            val handled = wxApi?.handleIntent(intent, this) ?: false
            if (!handled) {
                LogManager.w(TAG, "微信新Intent处理失败")
                finish()
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "处理微信新Intent异常", e)
            finish()
        }
    }
    
    /**
     * 微信发送请求到第三方应用时，会回调到该方法
     */
    override fun onReq(req: BaseReq?) {
        LogManager.d(TAG, "微信请求回调: ${req?.type}")
        // 通常不需要处理
        finish()
    }
    
    /**
     * 第三方应用发送到微信的请求处理后的响应结果，会回调到该方法
     */
    override fun onResp(resp: BaseResp?) {
        LogManager.d(TAG, "微信响应回调: type=${resp?.type}, errCode=${resp?.errCode}, errStr=${resp?.errStr}")
        
        try {
            when (resp?.type) {
                ConstantsAPI.COMMAND_PAY_BY_WX -> {
                    // 支付结果回调
                    handlePaymentResponse(resp as? PayResp)
                }
                else -> {
                    LogManager.w(TAG, "未知的微信回调类型: ${resp?.type}")
                }
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "处理微信响应异常", e)
        } finally {
            finish()
        }
    }
    
    /**
     * 处理支付响应
     */
    private fun handlePaymentResponse(payResp: PayResp?) {
        if (payResp == null) {
            LogManager.e(TAG, "支付响应为空")
            return
        }
        
        val errCode = payResp.errCode
        val errStr = payResp.errStr
        val orderId = extractOrderIdFromExtData(payResp.extData)
        
        LogManager.d(TAG, "支付结果 - 错误码: $errCode, 错误信息: $errStr, 订单ID: $orderId")
        
        // 将结果传递给支付策略处理
        WeChatPayStrategy.handlePaymentResult(orderId, errCode, errStr)
    }
    
    /**
     * 从扩展数据中提取订单ID
     */
    private fun extractOrderIdFromExtData(extData: String?): String {
        return try {
            if (extData.isNullOrBlank()) {
                // 如果没有扩展数据，生成一个临时订单ID
                "WECHAT_${System.currentTimeMillis()}"
            } else {
                // 尝试从扩展数据中解析订单ID
                // 这里假设扩展数据格式为 "orderId=xxx"
                val parts = extData.split("=")
                if (parts.size >= 2 && parts[0] == "orderId") {
                    parts[1]
                } else {
                    extData
                }
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "提取订单ID失败", e)
            "WECHAT_${System.currentTimeMillis()}"
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        wxApi = null
        LogManager.d(TAG, "WXPayEntryActivity已销毁")
    }
}
