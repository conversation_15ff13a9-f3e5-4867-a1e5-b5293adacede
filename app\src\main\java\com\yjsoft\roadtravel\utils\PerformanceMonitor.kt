package com.yjsoft.roadtravel.utils

import android.os.SystemClock
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import java.util.concurrent.ConcurrentHashMap

/**
 * 性能监控工具
 * 用于监控应用启动时间和组件初始化性能
 */
object PerformanceMonitor {
    
    private const val TAG = "PerformanceMonitor %s"
    
    // 时间戳记录（包含异步标记）
    private val timestamps = ConcurrentHashMap<String, Pair<Long, Boolean>>()

    // 性能指标记录
    private val metrics = ConcurrentHashMap<String, PerformanceMetric>()
    
    /**
     * 性能指标数据类
     */
    data class PerformanceMetric(
        val name: String,
        val startTime: Long,
        val endTime: Long,
        val duration: Long,
        val description: String,
        val isAsync: Boolean = false
    ) {
        override fun toString(): String {
            val asyncFlag = if (isAsync) "[异步]" else "[同步]"
            return "$name: ${duration}ms $asyncFlag ($description)"
        }
    }
    
    /**
     * 记录开始时间
     * @param key 计时标识
     * @param description 计时描述
     * @param isAsync 是否为异步操作
     */
    fun startTiming(key: String, description: String = "", isAsync: Boolean = false) {
        val currentTime = SystemClock.elapsedRealtime()
        timestamps[key] = Pair(currentTime, isAsync)
        val asyncFlag = if (isAsync) "[异步]" else "[同步]"
        LogManager.d(TAG, "开始计时: $key $asyncFlag - $description")
    }
    
    /**
     * 记录结束时间并计算耗时
     */
    fun endTiming(key: String, description: String = ""): Long {
        val endTime = SystemClock.elapsedRealtime()
        val (startTime, isAsync) = timestamps[key] ?: run {
            LogManager.w(TAG, "未找到开始时间: $key")
            return 0L
        }

        val duration = endTime - startTime
        val metric = PerformanceMetric(
            name = key,
            startTime = startTime,
            endTime = endTime,
            duration = duration,
            description = description,
            isAsync = isAsync
        )

        metrics[key] = metric
        LogManager.i(TAG, "计时完成: $metric")

        return duration
    }
    
    /**
     * 开始异步操作计时
     */
    fun startAsyncTiming(key: String, description: String = "") {
        startTiming(key, description, isAsync = true)
    }

    /**
     * 结束异步操作计时
     */
    fun endAsyncTiming(key: String, description: String = ""): Long {
        return endTiming(key, description)
    }

    /**
     * 获取性能指标
     */
    fun getMetric(key: String): PerformanceMetric? {
        return metrics[key]
    }
    
    /**
     * 获取所有性能指标
     */
    fun getAllMetrics(): Map<String, PerformanceMetric> {
        return metrics.toMap()
    }
    
    /**
     * 生成性能报告
     */
    fun generateReport(): String {
        val report = StringBuilder()
        report.appendLine("=== 性能监控报告 ===")

        val syncMetrics = metrics.values.filter { !it.isAsync }
        val asyncMetrics = metrics.values.filter { it.isAsync }

        report.appendLine("总计监控项目: ${metrics.size} (同步: ${syncMetrics.size}, 异步: ${asyncMetrics.size})")
        report.appendLine()

        // 同步操作报告
        if (syncMetrics.isNotEmpty()) {
            report.appendLine("=== 同步操作 ===")
            val sortedSyncMetrics = syncMetrics.sortedByDescending { it.duration }
            sortedSyncMetrics.forEach { metric ->
                report.appendLine("  ${metric}")
            }
            report.appendLine("同步操作总耗时: ${syncMetrics.sumOf { it.duration }}ms")
            report.appendLine()
        }

        // 异步操作报告
        if (asyncMetrics.isNotEmpty()) {
            report.appendLine("=== 异步操作 ===")
            val sortedAsyncMetrics = asyncMetrics.sortedByDescending { it.duration }
            sortedAsyncMetrics.forEach { metric ->
                report.appendLine("  ${metric}")
            }
            report.appendLine("异步操作总耗时: ${asyncMetrics.sumOf { it.duration }}ms")
            report.appendLine()
        }

        // 计算总启动时间
        val appStartMetric = metrics["app_startup"]
        if (appStartMetric != null) {
            report.appendLine("应用总启动时间: ${appStartMetric.duration}ms")
        }

        // 计算androidx.startup优化效果
        val startupMetrics = metrics.values.filter { it.name.contains("startup") }
        if (startupMetrics.isNotEmpty()) {
            val totalStartupTime = startupMetrics.sumOf { it.duration }
            report.appendLine("androidx.startup总耗时: ${totalStartupTime}ms")
        }

        return report.toString()
    }
    
    /**
     * 记录启动阶段
     */
    fun recordStartupPhase(phase: String) {
        val currentTime = SystemClock.elapsedRealtime()
        LogManager.i(TAG, "启动阶段: $phase (${currentTime}ms)")
    }
    
    /**
     * 清除所有记录
     */
    fun clear() {
        timestamps.clear()
        metrics.clear()
        LogManager.d(TAG, "性能监控数据已清除")
    }
    
    /**
     * 输出性能报告到日志
     */
    fun logReport() {
        val report = generateReport()
        LogManager.i(TAG, "\n$report")
    }
}
