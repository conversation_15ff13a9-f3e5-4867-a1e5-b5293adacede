package com.yjsoft.roadtravel.basiclibrary.payment.strategies

import android.content.Context
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConfig
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConstants
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentStrategy
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.repository.PaymentRepository
import com.yjsoft.roadtravel.basiclibrary.payment.utils.PaymentUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume

/**
 * 微信支付策略实现
 * 集成微信支付SDK，提供微信支付功能
 */
class WeChatPayStrategy : PaymentStrategy {

    companion object {
        private const val TAG = "WeChatPayStrategy %s"
        private const val WECHAT_SDK_VERSION = "6.8.26"

        // 微信支付结果回调
        private var paymentCallback: ((PaymentResult) -> Unit)? = null

        /**
         * 处理微信支付回调结果
         * 由WXPayEntryActivity调用
         */
        fun handlePaymentResult(
            orderId: String,
            errCode: Int,
            errStr: String?
        ) {
            val result = when (errCode) {
                PaymentConstants.ResultCode.WeChat.SUCCESS.toInt() -> {
                    PaymentResult.Success(
                        orderId = orderId,
                        transactionId = PaymentUtils.generateNonceStr(),
                        amount = "0.00", // 实际金额需要从服务器获取
                        paymentType = PaymentType.WECHAT_PAY,
                        extraData = mapOf(
                            "errCode" to errCode.toString(),
                            "errStr" to (errStr ?: "")
                        )
                    )
                }

                PaymentConstants.ResultCode.WeChat.CANCEL.toInt() -> {
                    PaymentResult.Cancel(
                        orderId = orderId,
                        paymentType = PaymentType.WECHAT_PAY,
                        reason = errStr ?: "用户取消支付"
                    )
                }

                else -> {
                    PaymentResult.Error(
                        orderId = orderId,
                        paymentType = PaymentType.WECHAT_PAY,
                        errorCode = errCode.toString(),
                        errorMessage = errStr ?: "微信支付失败"
                    )
                }
            }

            paymentCallback?.invoke(result)
            paymentCallback = null
        }
    }

    private var wxApi: IWXAPI? = null
    private val paymentRepository = PaymentRepository.getInstance()

    override fun getPaymentType(): PaymentType = PaymentType.WECHAT_PAY

    override suspend fun isAvailable(context: Context): Boolean {
        return try {
            val api = getWXAPI(context)
            val isInstalled = api.isWXAppInstalled
            val isSupported = api.wxAppSupportAPI
            val isConfigured = isConfigured()

            LogManager.d(
                TAG,
                "微信支付可用性检查 - 已安装: $isInstalled, 支持API: $isSupported, 已配置: $isConfigured"
            )

            // 在调试模式下，即使微信未安装也返回true，用于演示
            if (PaymentConfig.isDebugMode && !isInstalled) {
                LogManager.w(TAG, "调试模式：微信未安装，但仍返回可用状态用于演示")
                return isConfigured
            }

            isInstalled && isSupported == 1 && isConfigured
        } catch (e: Exception) {
            LogManager.e(TAG, "检查微信支付可用性失败", e)
            // 在调试模式下返回配置状态
            if (PaymentConfig.isDebugMode) {
                LogManager.w(TAG, "调试模式：忽略微信检查异常")
                return isConfigured()
            }
            false
        }
    }

    override suspend fun initialize(context: Context): Boolean {
        return try {
            val appId = PaymentConfig.wechatAppId
            if (appId.isNullOrBlank()) {
                LogManager.e(TAG, "微信AppId未配置")
                return false
            }

            wxApi = WXAPIFactory.createWXAPI(context, appId, true)
            val registerResult = wxApi?.registerApp(appId) ?: false

            LogManager.d(TAG, "微信支付SDK初始化结果: $registerResult")
            registerResult
        } catch (e: Exception) {
            LogManager.e(TAG, "微信支付SDK初始化失败", e)
            false
        }
    }

    override suspend fun pay(context: Context, request: PaymentRequest): PaymentResult =
        withContext(Dispatchers.IO) {
            LogManager.d(TAG, "开始微信支付，订单ID: ${request.orderId}")

            try {
                // 在调试模式下，如果微信未安装，返回模拟支付结果
                if (PaymentConfig.isDebugMode) {
                    val api = getWXAPI(context)
                    if (!api.isWXAppInstalled) {
                        LogManager.w(TAG, "调试模式：微信未安装，返回模拟支付成功结果")
                        delay(2000) // 模拟支付延迟
                        return@withContext PaymentResult.Success(
                            orderId = request.orderId,
                            paymentType = PaymentType.WECHAT_PAY,
                            transactionId = "wx_mock_${System.currentTimeMillis()}",
                            amount = request.amount.toString(),
                            timestamp = System.currentTimeMillis()
                        )
                    }
                }

                // 确保SDK已初始化
                if (!initialize(context)) {
                    return@withContext PaymentResult.Error(
                        orderId = request.orderId,
                        paymentType = PaymentType.WECHAT_PAY,
                        errorCode = "INIT_FAILED",
                        errorMessage = "微信支付SDK初始化失败"
                    )
                }

                // 从服务器获取支付参数
                val paymentParams = getPaymentParamsFromServer(request)
                    ?: return@withContext PaymentResult.Error(
                        orderId = request.orderId,
                        paymentType = PaymentType.WECHAT_PAY,
                        errorCode = "PARAMS_ERROR",
                        errorMessage = "获取微信支付参数失败"
                    )

                // 调用微信支付
                callWeChatPay(context, request.orderId, paymentParams)

            } catch (e: Exception) {
                LogManager.e(TAG, "微信支付异常", e)
                PaymentResult.Error(
                    orderId = request.orderId,
                    paymentType = PaymentType.WECHAT_PAY,
                    errorCode = "WECHAT_EXCEPTION",
                    errorMessage = e.message ?: "微信支付异常",
                    cause = e
                )
            }
        }

    override fun isConfigured(): Boolean {
        return PaymentConfig.isWeChatPayConfigured()
    }

    override fun getSdkVersion(): String = WECHAT_SDK_VERSION

    override fun cleanup() {
        wxApi?.unregisterApp()
        wxApi = null
        paymentCallback = null
        LogManager.d(TAG, "微信支付策略清理完成")
    }

    /**
     * 获取微信API实例
     */
    private fun getWXAPI(context: Context): IWXAPI {
        if (wxApi == null) {
            val appId = PaymentConfig.wechatAppId ?: throw IllegalStateException("微信AppId未配置")
            wxApi = WXAPIFactory.createWXAPI(context, appId, true)
        }
        return wxApi!!
    }

    /**
     * 从服务器获取微信支付参数
     */
    private suspend fun getPaymentParamsFromServer(request: PaymentRequest): WeChatPayParams? {
        return try {
            val result = paymentRepository.createPaymentOrder(PaymentType.WECHAT_PAY, request)
            if (result.isSuccess) {
                val response = result.getOrNull()
                response?.let { parseWeChatPayParams(it.paymentParams) }
            } else {
                LogManager.e(TAG, "创建微信支付订单失败: ${result.exceptionOrNull()?.message}")
                null
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取微信支付参数异常", e)
            null
        }
    }

    /**
     * 解析微信支付参数
     */
    private fun parseWeChatPayParams(paramsJson: String): WeChatPayParams? {
        return try {
            PaymentUtils.fromJsonString<WeChatPayParams>(paramsJson)
        } catch (e: Exception) {
            LogManager.e(TAG, "解析微信支付参数失败", e)
            null
        }
    }

    /**
     * 调用微信支付
     */
    private suspend fun callWeChatPay(
        context: Context,
        orderId: String,
        params: WeChatPayParams
    ): PaymentResult = suspendCancellableCoroutine { continuation ->

        try {
            val api = getWXAPI(context)

            // 构建支付请求
            val payReq = PayReq().apply {
                appId = params.appId
                partnerId = params.partnerId
                prepayId = params.prepayId
                packageValue = params.packageValue
                nonceStr = params.nonceStr
                timeStamp = params.timeStamp
                sign = params.sign
            }

            // 设置支付回调
            paymentCallback = { result ->
                if (continuation.isActive) {
                    continuation.resume(result)
                }
            }

            // 发起支付
            val sendResult = api.sendReq(payReq)
            if (!sendResult) {
                val errorMessage = if (!api.isWXAppInstalled) {
                    "微信应用未安装或版本过低"
                } else {
                    "调用微信支付失败，可能是签名验证失败"
                }

                val errorResult = PaymentResult.Error(
                    orderId = orderId,
                    paymentType = PaymentType.WECHAT_PAY,
                    errorCode = "SEND_REQ_FAILED",
                    errorMessage = errorMessage
                )

                if (continuation.isActive) {
                    continuation.resume(errorResult)
                }
            }

            LogManager.d(TAG, "微信支付请求已发送，等待回调结果")

        } catch (e: Exception) {
            LogManager.e(TAG, "调用微信支付异常", e)
            val errorResult = PaymentResult.Error(
                orderId = orderId,
                paymentType = PaymentType.WECHAT_PAY,
                errorCode = "CALL_EXCEPTION",
                errorMessage = e.message ?: "调用微信支付异常",
                cause = e
            )

            if (continuation.isActive) {
                continuation.resume(errorResult)
            }
        }

        // 设置取消回调
        continuation.invokeOnCancellation {
            paymentCallback = null
            LogManager.d(TAG, "微信支付被取消")
        }
    }
}

/**
 * 微信支付参数数据类
 */
data class WeChatPayParams(
    val appId: String,
    val partnerId: String,
    val prepayId: String,
    val packageValue: String,
    val nonceStr: String,
    val timeStamp: String,
    val sign: String
)
