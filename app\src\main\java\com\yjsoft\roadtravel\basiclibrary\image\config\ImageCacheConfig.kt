package com.yjsoft.roadtravel.basiclibrary.image.config

import android.content.Context
import coil3.disk.DiskCache
import coil3.memory.MemoryCache
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import okio.Path.Companion.toPath
import java.io.File

/**
 * 图片缓存配置类
 * 负责管理内存缓存和磁盘缓存的配置
 */
object ImageCacheConfig {
    
    /**
     * 缓存清理策略
     */
    enum class CacheCleanupStrategy {
        NEVER,          // 从不清理
        ON_LOW_MEMORY,  // 内存不足时清理
        PERIODIC,       // 定期清理
        MANUAL          // 手动清理
    }
    
    /**
     * 缓存统计信息
     */
    data class CacheStats(
        val memoryCacheSize: Long,
        val memoryCacheMaxSize: Long,
        val memoryCacheHitCount: Long,
        val memoryCacheMissCount: Long,
        val diskCacheSize: Long,
        val diskCacheMaxSize: Long,
        val diskCacheHitCount: Long,
        val diskCacheMissCount: Long
    ) {
        val memoryCacheHitRate: Double
            get() = if (memoryCacheHitCount + memoryCacheMissCount > 0) {
                memoryCacheHitCount.toDouble() / (memoryCacheHitCount + memoryCacheMissCount)
            } else 0.0
            
        val diskCacheHitRate: Double
            get() = if (diskCacheHitCount + diskCacheMissCount > 0) {
                diskCacheHitCount.toDouble() / (diskCacheHitCount + diskCacheMissCount)
            } else 0.0
    }
    
    /**
     * 创建内存缓存配置
     */
    fun createMemoryCache(context: Context, config: ImageConfig.ImageLoadConfig): MemoryCache {
        val maxSize = ImageConfig.getMemoryCacheSize(context)
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建内存缓存，最大大小: ${maxSize / 1024 / 1024}MB")
        
        return MemoryCache.Builder()
            .maxSizeBytes(maxSize)
            .strongReferencesEnabled(true)
            .weakReferencesEnabled(true)
            .build()
    }
    
    /**
     * 创建磁盘缓存配置
     */
    fun createDiskCache(context: Context, config: ImageConfig.ImageLoadConfig): DiskCache {
        val cacheDir = getCacheDirectory(context)
        val maxSize = ImageConfig.getDiskCacheSize()
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建磁盘缓存，目录: ${cacheDir.absolutePath}, 最大大小: ${maxSize / 1024 / 1024}MB")
        
        return DiskCache.Builder()
            .directory(cacheDir.absolutePath.toPath())
            .maxSizeBytes(maxSize)
            .build()
    }
    
    /**
     * 获取缓存目录
     */
    private fun getCacheDirectory(context: Context): File {
        val cacheDir = File(context.cacheDir, ImageConfig.getCacheDirectoryName())
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        return cacheDir
    }
    
    /**
     * 清理内存缓存
     */
    fun clearMemoryCache(memoryCache: MemoryCache?) {
        try {
            memoryCache?.clear()
            LogManager.tag(LogConfig.Tags.IMAGE).i("内存缓存已清理")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "清理内存缓存失败")
        }
    }
    
    /**
     * 清理磁盘缓存
     */
    fun clearDiskCache(diskCache: DiskCache?) {
        try {
            diskCache?.clear()
            LogManager.tag(LogConfig.Tags.IMAGE).i("磁盘缓存已清理")
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "清理磁盘缓存失败")
        }
    }
    
    /**
     * 清理所有缓存
     */
    fun clearAllCache(memoryCache: MemoryCache?, diskCache: DiskCache?) {
        clearMemoryCache(memoryCache)
        clearDiskCache(diskCache)
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(memoryCache: MemoryCache?, diskCache: DiskCache?): CacheStats {
        return CacheStats(
            memoryCacheSize = memoryCache?.size ?: 0L,
            memoryCacheMaxSize = memoryCache?.maxSize ?: 0L,
            memoryCacheHitCount = 0L, // Coil3暂不提供此统计
            memoryCacheMissCount = 0L,
            diskCacheSize = diskCache?.size ?: 0L,
            diskCacheMaxSize = diskCache?.maxSize ?: 0L,
            diskCacheHitCount = 0L, // Coil3暂不提供此统计
            diskCacheMissCount = 0L
        )
    }
    
    /**
     * 检查缓存健康状态
     */
    fun checkCacheHealth(context: Context, memoryCache: MemoryCache?, diskCache: DiskCache?): Boolean {
        return try {
            // 检查内存缓存
            val memoryOk = memoryCache?.let { cache ->
                cache.size >= 0 && cache.maxSize > 0
            } ?: false
            
            // 检查磁盘缓存
            val diskOk = diskCache?.let { cache ->
                cache.size >= 0 && cache.maxSize > 0
            } ?: false
            
            // 检查缓存目录
            val cacheDir = getCacheDirectory(context)
            val dirOk = cacheDir.exists() && cacheDir.canRead() && cacheDir.canWrite()
            
            val isHealthy = memoryOk && diskOk && dirOk
            
            LogManager.tag(LogConfig.Tags.IMAGE).d(
                "缓存健康检查: 内存缓存=$memoryOk, 磁盘缓存=$diskOk, 目录=$dirOk, 总体=$isHealthy"
            )
            
            isHealthy
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "缓存健康检查失败")
            false
        }
    }
    
    /**
     * 优化缓存配置
     * 根据设备性能和可用空间动态调整缓存大小
     */
    fun optimizeCacheConfig(context: Context): ImageConfig.ImageLoadConfig {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val availableMemory = maxMemory - totalMemory + freeMemory
        
        // 根据可用内存调整缓存策略
        val memoryCachePercent = when {
            availableMemory > 512 * 1024 * 1024 -> 0.30 // 512MB以上，使用30%
            availableMemory > 256 * 1024 * 1024 -> 0.25 // 256MB以上，使用25%
            availableMemory > 128 * 1024 * 1024 -> 0.20 // 128MB以上，使用20%
            else -> 0.15 // 低内存设备，使用15%
        }
        
        // 根据存储空间调整磁盘缓存
        val cacheDir = getCacheDirectory(context)
        val availableSpace = cacheDir.freeSpace
        val diskCacheMB = when {
            availableSpace > 2L * 1024 * 1024 * 1024 -> 500L // 2GB以上，使用500MB
            availableSpace > 1L * 1024 * 1024 * 1024 -> 300L // 1GB以上，使用300MB
            availableSpace > 512L * 1024 * 1024 -> 200L // 512MB以上，使用200MB
            else -> 100L // 低存储空间，使用100MB
        }
        
        LogManager.tag(LogConfig.Tags.IMAGE).i(
            "缓存配置优化: 内存缓存=${(memoryCachePercent * 100).toInt()}%, 磁盘缓存=${diskCacheMB}MB"
        )
        
        return ImageConfig.getCurrentConfig().copy(
            memoryCacheSizePercent = memoryCachePercent,
            diskCacheSizeMB = diskCacheMB
        )
    }
    
    /**
     * 获取缓存使用情况报告
     */
    fun getCacheUsageReport(memoryCache: MemoryCache?, diskCache: DiskCache?): String {
        val stats = getCacheStats(memoryCache, diskCache)
        return buildString {
            appendLine("=== 图片缓存使用报告 ===")
            appendLine("内存缓存:")
            appendLine("  当前大小: ${stats.memoryCacheSize / 1024 / 1024}MB")
            appendLine("  最大大小: ${stats.memoryCacheMaxSize / 1024 / 1024}MB")
            appendLine("  使用率: ${(stats.memoryCacheSize * 100 / stats.memoryCacheMaxSize.coerceAtLeast(1))}%")
            appendLine("磁盘缓存:")
            appendLine("  当前大小: ${stats.diskCacheSize / 1024 / 1024}MB")
            appendLine("  最大大小: ${stats.diskCacheMaxSize / 1024 / 1024}MB")
            appendLine("  使用率: ${(stats.diskCacheSize * 100 / stats.diskCacheMaxSize.coerceAtLeast(1))}%")
        }
    }
}
