package com.yjsoft.roadtravel.basiclibrary.network.models

/**
 * API错误信息类
 */
data class ApiError(
    val code: Int,
    val message: String,
    val details: String? = null,
    val timestamp: Long = System.currentTimeMillis()
) {
    companion object {
        // 网络错误类型
        const val NETWORK_ERROR = -1
        const val TIMEOUT_ERROR = -2
        const val PARSE_ERROR = -3
        const val UNKNOWN_ERROR = -4
        
        // 业务错误类型
        const val BUSINESS_ERROR = 1000
        const val VALIDATION_ERROR = 1001
        const val PERMISSION_ERROR = 1002
        
        /**
         * 创建网络错误
         */
        fun networkError(message: String = "网络连接失败"): ApiError {
            return ApiError(NETWORK_ERROR, message)
        }
        
        /**
         * 创建超时错误
         */
        fun timeoutError(message: String = "请求超时"): ApiError {
            return ApiError(TIMEOUT_ERROR, message)
        }
        
        /**
         * 创建解析错误
         */
        fun parseError(message: String = "数据解析失败"): ApiError {
            return ApiError(PARSE_ERROR, message)
        }
        
        /**
         * 创建未知错误
         */
        fun unknownError(message: String = "未知错误"): ApiError {
            return ApiError(UNKNOWN_ERROR, message)
        }
        
        /**
         * 创建业务错误
         */
        fun businessError(code: Int, message: String): ApiError {
            return ApiError(code, message)
        }
    }
}

/**
 * API异常类
 */
class ApiException(
    val code: Int,
    override val message: String,
    val details: String? = null
) : Exception(message) {
    
    /**
     * 转换为ApiError
     */
    fun toApiError(): ApiError {
        return ApiError(code, message, details)
    }
    
    /**
     * 判断是否为网络错误
     */
    fun isNetworkError(): Boolean {
        return code == ApiError.NETWORK_ERROR
    }
    
    /**
     * 判断是否为超时错误
     */
    fun isTimeoutError(): Boolean {
        return code == ApiError.TIMEOUT_ERROR
    }
    
    /**
     * 判断是否为认证错误
     */
    fun isAuthError(): Boolean {
        return code == ApiResponse.UNAUTHORIZED_CODE || code == ApiResponse.TOKEN_EXPIRED_CODE
    }
    
    /**
     * 判断是否为服务器错误
     */
    fun isServerError(): Boolean {
        return code >= 500
    }
}
