# Hilt 依赖注入迁移指南

## 概述

本指南帮助您将现有的手动初始化代码逐步迁移到Hilt依赖注入框架。我们采用渐进式迁移策略，确保在迁移过程中不会破坏现有功能。

## 迁移策略

### 阶段1：基础设施准备 ✅
- [x] 添加Hilt依赖和配置
- [x] 创建Hilt模块和配置类
- [x] 修改Application类添加@HiltAndroidApp
- [x] 保持现有手动初始化逻辑

### 阶段2：Activity层迁移
- [ ] 为Activity添加@AndroidEntryPoint注解
- [ ] 注入ManagerAdapter或直接注入Manager
- [ ] 逐步替换手动获取Manager的代码

### 阶段3：ViewModel层迁移
- [ ] 创建@HiltViewModel注解的ViewModel
- [ ] 注入Repository和Manager依赖
- [ ] 替换现有ViewModel的手动依赖获取

### 阶段4：Repository层迁移
- [ ] 为Repository添加@Singleton注解
- [ ] 注入数据源和Manager依赖
- [ ] 更新Repository的创建方式

### 阶段5：完全迁移
- [ ] 移除手动初始化代码
- [ ] 清理不需要的单例模式代码
- [ ] 优化依赖关系

## 详细迁移步骤

### 1. Activity迁移

#### 迁移前
```kotlin
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 手动获取Manager
        val networkManager = NetworkManager
        val logManager = LogManager
        
        setContent {
            // UI内容
        }
    }
}
```

#### 迁移后
```kotlin
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var managerAdapter: ManagerAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 使用注入的Manager
        val networkManager = managerAdapter.getNetworkManager()
        val logManager = managerAdapter.getLogManager()
        
        setContent {
            // UI内容
        }
    }
}
```

### 2. ViewModel迁移

#### 迁移前
```kotlin
class MainViewModel : ViewModel() {
    private val repository = MainRepository.getInstance()
    private val networkManager = NetworkManager
    
    fun loadData() {
        // 使用手动获取的依赖
    }
}

// 在Composable中
@Composable
fun MainScreen() {
    val viewModel = viewModel<MainViewModel>()
    // ...
}
```

#### 迁移后
```kotlin
@HiltViewModel
class MainViewModel @Inject constructor(
    private val repository: MainRepository,
    private val networkManager: NetworkManager
) : ViewModel() {
    
    fun loadData() {
        // 使用注入的依赖
    }
}

// 在Composable中
@Composable
fun MainScreen() {
    val viewModel: MainViewModel = hiltViewModel()
    // ...
}
```

### 3. Repository迁移

#### 迁移前
```kotlin
class MainRepository private constructor() {
    companion object {
        @Volatile
        private var INSTANCE: MainRepository? = null
        
        fun getInstance(): MainRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: MainRepository().also { INSTANCE = it }
            }
        }
    }
    
    private val apiService = RetrofitInstance.getInstance().createService(ApiService::class.java)
    private val dataStoreManager = DataStoreManager
}
```

#### 迁移后
```kotlin
@Singleton
class MainRepository @Inject constructor(
    private val apiService: ApiService,
    private val dataStoreManager: DataStoreManager
) {
    // 移除单例模式代码
    // 直接使用注入的依赖
}
```

## 兼容性处理

### ManagerAdapter使用
在迁移期间，使用ManagerAdapter来兼容两种方式：

```kotlin
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var managerAdapter: ManagerAdapter
    
    private fun getNetworkManager(): NetworkManager {
        return if (::managerAdapter.isInitialized) {
            // 使用依赖注入
            managerAdapter.getNetworkManager()
        } else {
            // 回退到手动初始化
            NetworkManager
        }
    }
}
```

### 渐进式替换
1. **第一步**：添加@AndroidEntryPoint和注入代码，但保持原有逻辑
2. **第二步**：逐个方法替换为使用注入的依赖
3. **第三步**：移除手动初始化代码

## 常见问题和解决方案

### 1. 循环依赖
**问题**：A依赖B，B依赖A
**解决**：
- 重新设计依赖关系
- 使用Provider<T>延迟注入
- 提取公共接口

```kotlin
@Inject
lateinit var providerA: Provider<A>

fun someMethod() {
    val a = providerA.get()
}
```

### 2. 作用域不匹配
**问题**：@Singleton依赖注入到@ActivityScoped组件
**解决**：确保依赖的作用域兼容

### 3. 初始化顺序
**问题**：某些Manager需要特定的初始化顺序
**解决**：
- 在Module中控制初始化顺序
- 使用@Binds和@Provides的组合

### 4. 测试问题
**问题**：测试中如何mock注入的依赖
**解决**：
```kotlin
@HiltAndroidTest
class MainActivityTest {
    
    @get:Rule
    var hiltRule = HiltAndroidRule(this)
    
    @BindValue
    @JvmField
    val mockNetworkManager: NetworkManager = mockk()
    
    @Test
    fun testSomething() {
        // 测试代码
    }
}
```

## 迁移检查清单

### 准备阶段
- [ ] 确认Hilt依赖已正确添加
- [ ] Application类已添加@HiltAndroidApp
- [ ] 所有Hilt模块已创建并正确配置

### Activity迁移
- [ ] 添加@AndroidEntryPoint注解
- [ ] 注入ManagerAdapter或直接注入Manager
- [ ] 测试Activity功能正常

### ViewModel迁移
- [ ] 添加@HiltViewModel注解
- [ ] 构造函数注入依赖
- [ ] 更新Composable中的ViewModel获取方式
- [ ] 测试ViewModel功能正常

### Repository迁移
- [ ] 添加@Singleton注解
- [ ] 构造函数注入依赖
- [ ] 移除单例模式代码
- [ ] 更新Repository的使用方式

### 清理阶段
- [ ] 移除不需要的手动初始化代码
- [ ] 清理单例模式相关代码
- [ ] 更新文档和注释
- [ ] 进行全面测试

## 回滚计划

如果迁移过程中遇到问题，可以按以下步骤回滚：

1. **移除@AndroidEntryPoint注解**
2. **移除@Inject注解的属性**
3. **恢复手动初始化代码**
4. **移除@HiltViewModel注解**
5. **恢复原有的ViewModel创建方式**

## 性能考虑

### 优化建议
1. **使用@Singleton谨慎**：只对真正需要单例的类使用
2. **避免过度注入**：不要注入不必要的依赖
3. **合理使用作用域**：选择合适的生命周期作用域
4. **延迟初始化**：对于重量级对象考虑使用Provider

### 性能监控
```kotlin
// 在DIConfig中启用性能监控
val config = DIConfig.getCurrentConfiguration()
if (config.performanceMonitoring) {
    // 记录依赖注入性能指标
}
```

## 总结

Hilt依赖注入迁移是一个渐进的过程，关键是：
1. **保持兼容性**：在迁移过程中不破坏现有功能
2. **逐步替换**：一次迁移一个组件
3. **充分测试**：每个迁移步骤都要进行测试
4. **文档更新**：及时更新相关文档

通过遵循本指南，您可以安全、有序地将项目迁移到Hilt依赖注入框架。
