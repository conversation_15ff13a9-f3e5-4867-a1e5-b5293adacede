package com.yjsoft.roadtravel.basiclibrary.location.service

import android.content.Context
import com.amap.api.location.AMapLocationClient
import com.amap.api.location.AMapLocationListener
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationConfig
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationError
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * 高德定位服务
 * 封装高德地图定位API，提供响应式的定位数据流
 */
class AMapLocationService(private val context: Context) {
    
    companion object {
        private const val TAG = "AMapLocationService %s"
    }
    
    // 高德定位客户端
    private var locationClient: AMapLocationClient? = null
    
    // 当前配置
    private var currentConfig: LocationConfig? = null
    
    // 是否正在定位
    private var isLocating = false
    
    /**
     * 初始化定位服务
     */
    fun initialize(config: LocationConfig = LocationConfig.default()): Result<Unit> {
        return try {
            LogManager.d(TAG, "初始化高德定位服务")
            AMapLocationClient.updatePrivacyShow(context.applicationContext,true,true)
            AMapLocationClient.updatePrivacyAgree(context.applicationContext,true)
            
            // 创建定位客户端
            locationClient = AMapLocationClient(context.applicationContext)
            
            // 设置定位参数
            setLocationConfig(config)
            
            LogManager.d(TAG, "高德定位服务初始化成功")
            Result.success(Unit)
            
        } catch (e: Exception) {
            LogManager.e(TAG, "高德定位服务初始化失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 设置定位配置
     */
    fun setLocationConfig(config: LocationConfig) {
        currentConfig = config
        locationClient?.setLocationOption(config.toAMapLocationClientOption())
        LogManager.d(TAG, "设置定位配置: ${config.locationMode.description}")
    }
    
    /**
     * 开始定位
     * 返回定位数据流
     */
    fun startLocation(): Flow<Result<LocationData>> = callbackFlow<Result<LocationData>> {
        val client = locationClient
        if (client == null) {
            trySend(Result.failure(IllegalStateException("定位服务未初始化")))
            close()
            return@callbackFlow
        }
        
        if (isLocating) {
            LogManager.w(TAG, "定位已在进行中")
            close()
            return@callbackFlow
        }
        
        LogManager.d(TAG, "开始定位")
        isLocating = true
        
        // 创建定位监听器
        val locationListener = AMapLocationListener { aMapLocation ->
            try {
                if (aMapLocation != null) {
                    if (aMapLocation.errorCode == 0) {
                        // 定位成功
                        val locationData = LocationData.fromAMapLocation(aMapLocation)
                        LogManager.d(TAG, "定位成功: ${locationData.shortAddress}, 精度: ${locationData.accuracy}m")
                        trySend(Result.success(locationData))
                        
                        // 如果是单次定位，停止定位
                        if (currentConfig?.isOnceLocation == true) {
                            stopLocation()
                        }
                    } else {
                        // 定位失败
                        val error = LocationError.fromAMapLocation(aMapLocation)
                        LogManager.w(TAG, "定位失败: ${error.message}")
                        trySend(Result.failure(Exception(error.message)))
                    }
                } else {
                    LogManager.w(TAG, "定位结果为空")
                    trySend(Result.failure(Exception("定位结果为空")))
                }
            } catch (e: Exception) {
                LogManager.e(TAG, "处理定位结果时发生错误", e)
                trySend(Result.failure(e))
            }
        }
        
        // 设置定位监听器
        client.setLocationListener(locationListener)
        
        // 启动定位
        client.startLocation()
        
        awaitClose {
            LogManager.d(TAG, "停止定位流")
            stopLocation()
        }
    }.distinctUntilChanged { old, new ->
        // 过滤重复的定位结果
        if (old.isSuccess && new.isSuccess) {
            val oldData = old.getOrNull()
            val newData = new.getOrNull()
            if (oldData != null && newData != null) {
                // 如果位置变化小于最小距离，认为是重复结果
                oldData.distanceTo(newData) < 10f
            } else {
                false
            }
        } else {
            false
        }
    }
    
    /**
     * 停止定位
     */
    fun stopLocation() {
        if (isLocating) {
            LogManager.d(TAG, "停止定位")
            locationClient?.stopLocation()
            isLocating = false
        }
    }
    
    /**
     * 获取最后一次定位结果
     */
    fun getLastKnownLocation(): LocationData? {
        return try {
            val lastLocation = locationClient?.lastKnownLocation
            if (lastLocation != null && lastLocation.errorCode == 0) {
                LocationData.fromAMapLocation(lastLocation)
            } else {
                null
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "获取最后定位结果失败", e)
            null
        }
    }
    
    /**
     * 检查定位服务是否可用
     */
    fun isLocationServiceAvailable(): Boolean {
        return try {
            locationClient != null
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取当前定位状态
     */
    fun isLocating(): Boolean {
        return isLocating
    }
    
    /**
     * 销毁定位服务
     */
    fun destroy() {
        LogManager.d(TAG, "销毁定位服务")
        
        try {
            stopLocation()
            locationClient?.onDestroy()
            locationClient = null
            currentConfig = null
        } catch (e: Exception) {
            LogManager.e(TAG, "销毁定位服务时发生错误", e)
        }
    }
    
    /**
     * 单次定位
     */
    suspend fun getSingleLocation(config: LocationConfig = LocationConfig.singleHighAccuracy()): Result<LocationData> {
        return try {
            // 保存当前配置
            val originalConfig = currentConfig
            
            // 设置单次定位配置
            setLocationConfig(config)
            
            // 开始定位并获取第一个结果
            var result: Result<LocationData>? = null
            startLocation().collect { locationResult ->
                result = locationResult
                return@collect // 只获取第一个结果
            }
            
            // 恢复原始配置
            originalConfig?.let { setLocationConfig(it) }
            
            result ?: Result.failure(Exception("未获取到定位结果"))
            
        } catch (e: Exception) {
            LogManager.e(TAG, "单次定位失败", e)
            Result.failure(e)
        }
    }
}
