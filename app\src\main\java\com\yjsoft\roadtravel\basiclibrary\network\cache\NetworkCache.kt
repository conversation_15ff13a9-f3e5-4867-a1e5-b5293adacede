package com.yjsoft.roadtravel.basiclibrary.network.cache

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import kotlinx.coroutines.launch
import okhttp3.Cache
import okhttp3.Interceptor
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.util.Locale

/**
 * 网络缓存管理器
 * 支持异步初始化以避免阻塞主线程
 */
class NetworkCache(private val context: Context) {

    private val cacheDir: File by lazy {
        createCacheDirectory()
    }

    private val cacheInstance: Cache by lazy {
        createCacheInstance()
    }

    // 缓存初始化状态
    @Volatile
    private var isInitialized = false

    /**
     * 创建缓存目录
     */
    private fun createCacheDirectory(): File {
        val dir = File(context.cacheDir, "http_cache")
        if (!dir.exists()) {
            dir.mkdirs()
        }
        return dir
    }

    /**
     * 创建缓存实例
     */
    private fun createCacheInstance(): Cache {
        return Cache(cacheDir, NetworkConfig.getCacheSize()).also {
            isInitialized = true
        }
    }
    
    /**
     * 异步初始化缓存（避免阻塞主线程）
     */
    fun initAsync() {
        kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
            try {
                // 触发缓存目录和实例的创建
                cacheDir
                cacheInstance
            } catch (e: Exception) {
                android.util.Log.w("NetworkCache", "异步初始化失败: ${e.message}")
            }
        }
    }

    /**
     * 检查缓存是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * 获取OkHttp缓存实例
     */
    fun getCache(): Cache = cacheInstance
    
    /**
     * 清除所有缓存
     */
    fun clearCache() {
        try {
            cacheInstance.evictAll()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }
    
    /**
     * 获取缓存大小（字节）
     */
    fun getCacheSize(): Long {
        return try {
            cacheInstance.size()
        } catch (e: IOException) {
            0L
        }
    }
    
    /**
     * 获取缓存目录
     */
    fun getCacheDirectory(): File = cacheDir
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): CacheStats {
        return try {
            CacheStats(
                size = cacheInstance.size(),
                maxSize = cacheInstance.maxSize(),
                hitCount = cacheInstance.hitCount(),
                requestCount = cacheInstance.requestCount(),
                networkCount = cacheInstance.networkCount()
            )
        } catch (e: Exception) {
            CacheStats()
        }
    }
    
    /**
     * 关闭缓存
     */
    fun close() {
        try {
            cacheInstance.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }
}

/**
 * 缓存统计信息
 */
data class CacheStats(
    val size: Long = 0L,
    val maxSize: Long = 0L,
    val hitCount: Int = 0,
    val requestCount: Int = 0,
    val networkCount: Int = 0
) {
    /**
     * 计算缓存命中率
     */
    fun getHitRate(): Double {
        return if (requestCount > 0) {
            hitCount.toDouble() / requestCount.toDouble()
        } else {
            0.0
        }
    }
    
    /**
     * 获取格式化的缓存大小
     */
    fun getFormattedSize(): String {
        return formatBytes(size)
    }
    
    /**
     * 获取格式化的最大缓存大小
     */
    fun getFormattedMaxSize(): String {
        return formatBytes(maxSize)
    }
    
    private fun formatBytes(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format(Locale.getDefault(), "%.2f GB", gb)
            mb >= 1 -> String.format(Locale.getDefault(), "%.2f MB", mb)
            kb >= 1 -> String.format(Locale.getDefault(), "%.2f KB", kb)
            else -> "$bytes B"
        }
    }
}

/**
 * 缓存拦截器
 */
class CacheInterceptor(
    private val defaultCacheConfig: CacheConfig = CacheConfig()
) : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 从请求头中获取缓存配置，如果没有则使用默认配置
        val cacheConfig = getCacheConfigFromRequest(originalRequest) ?: defaultCacheConfig
        
        // 应用缓存控制
        val request = originalRequest.newBuilder()
            .cacheControl(cacheConfig.toCacheControl())
            .apply {
                // 添加自定义头部
                cacheConfig.customHeaders.forEach { (key, value) ->
                    header(key, value)
                }
            }
            .build()
        
        val response = chain.proceed(request)
        
        // 为响应添加缓存控制头
        return response.newBuilder()
            .header("Cache-Control", "public, max-age=${cacheConfig.maxAge}")
            .removeHeader("Pragma") // 移除可能影响缓存的Pragma头
            .build()
    }
    
    /**
     * 从请求中获取缓存配置
     */
    private fun getCacheConfigFromRequest(request: okhttp3.Request): CacheConfig? {
        // 可以通过自定义头部传递缓存配置
        val strategyHeader = request.header("X-Cache-Strategy")
        val maxAgeHeader = request.header("X-Cache-Max-Age")
        val maxStaleHeader = request.header("X-Cache-Max-Stale")
        
        if (strategyHeader != null) {
            val strategy = try {
                CacheStrategy.valueOf(strategyHeader.uppercase())
            } catch (e: IllegalArgumentException) {
                return null
            }
            
            return CacheConfig(
                strategy = strategy,
                maxAge = maxAgeHeader?.toIntOrNull() ?: NetworkConfig.getCacheMaxAge(),
                maxStale = maxStaleHeader?.toIntOrNull() ?: NetworkConfig.getCacheMaxStale()
            )
        }
        
        return null
    }
}

/**
 * 网络缓存拦截器（用于处理网络响应的缓存）
 */
class NetworkCacheInterceptor : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        
        // 只对成功的响应进行缓存处理
        return if (response.isSuccessful) {
            response.newBuilder()
                .header("Cache-Control", "public, max-age=${NetworkConfig.getCacheMaxAge()}")
                .removeHeader("Pragma")
                .build()
        } else {
            response
        }
    }
}
