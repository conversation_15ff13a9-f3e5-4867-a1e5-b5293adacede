# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# ========== 日志框架相关混淆规则 ==========

# 保留Timber相关类
-keep class timber.log.** { *; }
-dontwarn timber.log.**

# 保留自定义日志框架类
-keep class com.yjsoft.roadtravel.logger.** { *; }

# 保留日志相关的异常信息，用于崩溃分析
-keepattributes SourceFile,LineNumberTable
-keepattributes Signature
-keepattributes *Annotation*

# 保留异常类的构造函数和消息
-keepclassmembers class * extends java.lang.Throwable {
    <init>(...);
    public java.lang.String getMessage();
}

# 保留用于日志输出的方法名和行号信息
-keepattributes MethodParameters

# 如果使用了反射调用日志方法，保留相关方法
-keepclassmembers class * {
    @com.yjsoft.roadtravel.logger.** <methods>;
}

# ========== Hilt 依赖注入框架混淆规则 ==========

# 保留Hilt生成的类
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
-keep class dagger.** { *; }

# 保留Hilt注解
-keep @dagger.hilt.android.HiltAndroidApp class *
-keep @dagger.hilt.android.AndroidEntryPoint class *
-keep @dagger.hilt.android.lifecycle.HiltViewModel class *

# 保留Hilt模块
-keep @dagger.Module class *
-keep @dagger.hilt.InstallIn class *

# 保留注入的字段和方法
-keepclassmembers class * {
    @javax.inject.Inject <fields>;
    @javax.inject.Inject <methods>;
    @javax.inject.Inject <init>(...);
}

# 保留Hilt生成的组件
-keep class **_HiltComponents$* { *; }
-keep class **Hilt_* { *; }
-keep class **_Factory { *; }
-keep class **_MembersInjector { *; }

# 保留自定义的DI相关类
-keep class com.yjsoft.roadtravel.basiclibrary.di.** { *; }

# 保留限定符注解
-keep @interface javax.inject.Qualifier
-keep @javax.inject.Qualifier class *

# 保留作用域注解
-keep @interface javax.inject.Scope
-keep @javax.inject.Scope class *

# 保留Provider接口
-keep interface javax.inject.Provider