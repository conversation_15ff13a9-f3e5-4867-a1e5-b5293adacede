package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.network.ApiService
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.network.RetrofitInstance
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton

/**
 * 网络框架依赖注入模块
 * 
 * 功能：
 * - 提供NetworkManager的依赖注入
 * - 提供Retrofit实例的依赖注入
 * - 提供ApiService的依赖注入
 * - 管理网络框架的生命周期
 * 
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 延迟初始化避免循环依赖
 * - 兼容现有代码逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    /**
     * 提供NetworkManager实例
     */
    @Provides
    @Singleton
    fun provideNetworkManager(
        @ApplicationContext context: Context
    ): NetworkManager {
        return NetworkManagerProvider.getInstance(context)
    }
    
    /**
     * 提供Retrofit实例
     */
    @Provides
    @Singleton
    fun provideRetrofit(
        @ApplicationContext context: Context
    ): Retrofit {
        return RetrofitInstanceProvider.getInstance(context)
    }
    
    /**
     * 提供ApiService实例
     */
    @Provides
    @Singleton
    fun provideApiService(retrofit: Retrofit): ApiService {
        return retrofit.create(ApiService::class.java)
    }
    
    /**
     * NetworkManager提供器
     */
    private object NetworkManagerProvider {
        @Volatile
        private var instance: NetworkManager? = null
        
        fun getInstance(context: Context): NetworkManager {
            return instance ?: synchronized(this) {
                instance ?: createNetworkManager(context).also { instance = it }
            }
        }
        
        private fun createNetworkManager(context: Context): NetworkManager {
            // 检查NetworkManager是否已经通过手动方式初始化
            return if (NetworkManager.isInitialized()) {
                // 如果已经初始化，直接返回现有实例
                NetworkManager
            } else {
                // 如果未初始化，进行初始化
                NetworkManager.apply {
                    init(context)
                }
            }
        }
    }
    
    /**
     * RetrofitInstance提供器
     */
    private object RetrofitInstanceProvider {
        @Volatile
        private var instance: Retrofit? = null
        
        fun getInstance(context: Context): Retrofit {
            return instance ?: synchronized(this) {
                instance ?: createRetrofitInstance(context).also { instance = it }
            }
        }
        
        private fun createRetrofitInstance(context: Context): Retrofit {
            // 确保NetworkManager已初始化
            NetworkManagerProvider.getInstance(context)
            
            // 获取RetrofitInstance
            return RetrofitInstance.getInstance(context).getRetrofit()
        }
    }
}

