package com.yjsoft.roadtravel.basiclibrary.image.interceptors

import coil3.intercept.Interceptor
import coil3.request.ImageRequest
import coil3.request.ImageResult
import coil3.request.SuccessResult
import coil3.request.ErrorResult
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * 图片加载日志拦截器
 * 记录图片加载的详细信息，用于调试和监控
 */
class ImageLoggingInterceptor : Interceptor {
    
    override suspend fun intercept(chain: Interceptor.Chain): ImageResult {
        val request = chain.request
        val startTime = System.currentTimeMillis()
        
        // 记录请求开始
        logRequest(request, startTime)
        
        return try {
            val result = chain.proceed()
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime

            // 记录请求结果
            logResult(request, result, duration)

            result
        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime

            // 记录请求错误
            logError(request, e, duration)

            throw e
        }
    }
    
    /**
     * 记录请求信息
     */
    private fun logRequest(request: ImageRequest, startTime: Long) {
        val url = request.data.toString()
        val size = "${request.sizeResolver}"
        
        LogManager.tag(LogConfig.Tags.IMAGE).d(
            "🖼️ 开始加载图片: $url, 尺寸: $size, 时间: $startTime"
        )
    }
    
    /**
     * 记录请求结果
     */
    private fun logResult(request: ImageRequest, result: ImageResult, duration: Long) {
        val url = request.data.toString()
        
        when (result) {
            is SuccessResult -> {
                val image = result.image
                LogManager.tag(LogConfig.Tags.IMAGE).d(
                    "✅ 图片加载成功: $url, 耗时: ${duration}ms, 图片: $image"
                )
            }
            is ErrorResult -> {
                LogManager.tag(LogConfig.Tags.IMAGE).w(
                    result.throwable,
                    "❌ 图片加载失败: $url, 耗时: ${duration}ms"
                )
            }
        }
    }
    
    /**
     * 记录请求错误
     */
    private fun logError(request: ImageRequest, error: Exception, duration: Long) {
        val url = request.data.toString()
        
        LogManager.tag(LogConfig.Tags.IMAGE).e(
            error,
            "💥 图片加载异常: $url, 耗时: ${duration}ms"
        )
    }
}
