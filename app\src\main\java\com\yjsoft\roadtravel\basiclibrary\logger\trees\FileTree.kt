package com.yjsoft.roadtravel.basiclibrary.logger.trees

import android.content.Context
import android.util.Log
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import timber.log.Timber
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.LinkedBlockingQueue

/**
 * 文件日志Tree
 * 将日志输出到文件，支持日志轮转和异步写入
 */
class FileTree(private val context: Context) : Timber.Tree() {
    
    private val logDir: File = LogConfig.FileLog.getLogDir(context.filesDir)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    private val logQueue = LinkedBlockingQueue<LogEntry>()
    private val executor = Executors.newSingleThreadExecutor { r ->
        Thread(r, "FileLogWriter").apply {
            isDaemon = true
        }
    }
    
    init {
        // 启动日志写入线程
        if (LogConfig.Performance.enableAsyncLogging) {
            executor.execute(LogWriterRunnable())
        }
        
        // 清理旧日志文件
        cleanupOldLogFiles()
    }
    
    /**
     * 日志条目数据类
     */
    private data class LogEntry(
        val timestamp: Long,
        val priority: Int,
        val tag: String?,
        val message: String,
        val throwable: Throwable?
    )
    
    /**
     * 日志写入线程
     */
    private inner class LogWriterRunnable : Runnable {
        override fun run() {
            while (!Thread.currentThread().isInterrupted) {
                try {
                    val entry = logQueue.take()
                    writeLogToFile(entry)
                } catch (e: InterruptedException) {
                    Thread.currentThread().interrupt()
                    break
                } catch (e: Exception) {
                    // 避免在日志系统中使用日志，直接输出到控制台
                    System.err.println("FileTree写入日志失败: ${e.message}")
                }
            }
        }
    }
    
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        if (!LogConfig.FileLog.isEnabled) {
            return
        }
        
        val entry = LogEntry(
            timestamp = System.currentTimeMillis(),
            priority = priority,
            tag = tag,
            message = message,
            throwable = t
        )
        
        if (LogConfig.Performance.enableAsyncLogging) {
            // 异步写入
            if (!logQueue.offer(entry)) {
                // 队列满了，直接丢弃（避免内存溢出）
                System.err.println("日志队列已满，丢弃日志: $message")
            }
        } else {
            // 同步写入
            writeLogToFile(entry)
        }
    }
    
    /**
     * 将日志写入文件
     */
    private fun writeLogToFile(entry: LogEntry) {
        try {
            val logFile = getCurrentLogFile()
            val formattedMessage = formatLogMessage(entry)
            
            FileWriter(logFile, true).use { writer ->
                writer.write(formattedMessage)
                writer.write("\n")
                writer.flush()
            }
            
            // 检查文件大小，如果超过限制则轮转
            checkAndRotateLogFile(logFile)
            
        } catch (e: IOException) {
            System.err.println("写入日志文件失败: ${e.message}")
        }
    }
    
    /**
     * 格式化日志消息
     */
    private fun formatLogMessage(entry: LogEntry): String {
        val timestamp = dateFormat.format(Date(entry.timestamp))
        val level = getPriorityString(entry.priority)
        val tag = entry.tag ?: "Unknown"
        val threadName = Thread.currentThread().name
        
        val builder = StringBuilder()
        builder.append("$timestamp $level/$tag [$threadName] ${entry.message}")
        
        // 添加异常信息
        entry.throwable?.let { throwable ->
            builder.append("\n")
            builder.append(Log.getStackTraceString(throwable))
        }
        
        return builder.toString()
    }
    
    /**
     * 获取优先级字符串
     */
    private fun getPriorityString(priority: Int): String {
        return when (priority) {
            Log.VERBOSE -> "V"
            Log.DEBUG -> "D"
            Log.INFO -> "I"
            Log.WARN -> "W"
            Log.ERROR -> "E"
            Log.ASSERT -> "A"
            else -> "U"
        }
    }
    
    /**
     * 获取当前日志文件
     */
    private fun getCurrentLogFile(): File {
        val fileName = LogConfig.FileLog.generateLogFileName()
        return File(logDir, fileName)
    }
    
    /**
     * 检查并轮转日志文件
     */
    private fun checkAndRotateLogFile(logFile: File) {
        val maxSizeBytes = LogConfig.FileLog.MAX_FILE_SIZE_MB * 1024 * 1024
        if (logFile.length() > maxSizeBytes) {
            // 文件过大，创建新文件（按日期自动轮转）
            cleanupOldLogFiles()
        }
    }
    
    /**
     * 清理旧日志文件
     */
    private fun cleanupOldLogFiles() {
        try {
            val logFiles = logDir.listFiles { _, name ->
                name.startsWith(LogConfig.FileLog.LOG_FILE_PREFIX) &&
                        name.endsWith(LogConfig.FileLog.LOG_FILE_EXTENSION)
            }
            
            if (logFiles != null && logFiles.size > LogConfig.FileLog.MAX_FILE_COUNT) {
                // 按修改时间排序，删除最旧的文件
                logFiles.sortBy { it.lastModified() }
                val filesToDelete = logFiles.size - LogConfig.FileLog.MAX_FILE_COUNT
                
                for (i in 0 until filesToDelete) {
                    if (logFiles[i].delete()) {
                        System.out.println("删除旧日志文件: ${logFiles[i].name}")
                    }
                }
            }
        } catch (e: Exception) {
            System.err.println("清理旧日志文件失败: ${e.message}")
        }
    }
    
    /**
     * 获取所有日志文件
     */
    fun getLogFiles(): List<File> {
        return logDir.listFiles { _, name ->
            name.startsWith(LogConfig.FileLog.LOG_FILE_PREFIX) &&
                    name.endsWith(LogConfig.FileLog.LOG_FILE_EXTENSION)
        }?.toList() ?: emptyList()
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        executor.shutdown()
    }
}
