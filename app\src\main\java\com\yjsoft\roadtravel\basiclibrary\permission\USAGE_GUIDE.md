# 权限框架使用指南

## 快速开始

### 1. 基础设置

在 `Application` 中初始化权限框架配置：

```kotlin
class RoadTravelApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        // 初始化权限框架（全局配置）
        PermissionConfig.initializeDefaults()
    }
}
```

在 `Activity` 中初始化权限管理器：

```kotlin
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化权限管理器（Activity级别）
        // 重要：必须在setContent之前初始化
        PermissionManager.getInstance(this).initialize(this)

        setContent {
            // UI内容
        }
    }
}
```

### 2. 使用Compose组件申请权限

#### 申请单个权限

```kotlin
@Composable
fun CameraFeature() {
    SinglePermissionRequester(
        permission = Manifest.permission.CAMERA,
        onPermissionResult = { granted ->
            if (granted) {
                // 权限已授予
            } else {
                // 权限被拒绝
            }
        }
    ) { state ->
        val scope = rememberCoroutineScope()
        
        Button(
            onClick = {
                scope.launch {
                    state.requestPermissions()
                }
            }
        ) {
            Text("申请相机权限")
        }
    }
}
```

#### 申请位置权限

```kotlin
@Composable
fun LocationFeature() {
    LocationPermissionRequester(
        includeBackgroundLocation = false,
        onPermissionResult = { result ->
            when (result) {
                is PermissionResult.Granted -> {
                    // 位置权限已授予
                }
                is PermissionResult.Denied -> {
                    // 位置权限被拒绝
                }
            }
        }
    ) { state ->
        // UI内容
    }
}
```

### 3. 使用ViewModel管理权限

```kotlin
class LocationViewModel(application: Application) : PermissionViewModel(application) {
    
    fun requestLocationPermissions() {
        requestLocationPermissions { result ->
            when (result) {
                is PermissionResult.Granted -> {
                    // 处理权限授予
                }
                is PermissionResult.Denied -> {
                    // 处理权限拒绝
                }
            }
        }
    }
}
```

### 4. 权限状态显示

```kotlin
@Composable
fun PermissionStatus() {
    val permissionState = PermissionState(
        permission = Manifest.permission.CAMERA,
        status = PermissionStatus.GRANTED
    )
    
    PermissionIndicator(
        permissionState = permissionState,
        showLabel = true,
        showDescription = true
    )
}
```

## 高级功能

### 自定义权限配置

```kotlin
// 全局配置
PermissionConfig.defaultRequestConfig = PermissionRequestConfig(
    showRationaleDialog = true,
    rationaleTitle = "权限申请",
    rationaleMessage = "应用需要此权限"
)

// 权限组配置
PermissionConfig.setPermissionGroupConfig(
    PermissionGroups.LOCATION_GROUP,
    PermissionRequestConfig(
        rationaleTitle = "位置权限申请",
        rationaleMessage = "需要位置权限提供导航服务"
    )
)
```

### 自定义权限文案

```kotlin
PermissionMessages.setCustomMessage(
    Manifest.permission.CAMERA,
    PermissionMessage(
        title = "相机权限",
        rationale = "需要相机权限拍摄照片",
        settingsMessage = "请在设置中开启相机权限"
    )
)
```

### 使用DSL构建权限请求

```kotlin
val request = permissionRequest {
    permissions(
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO
    )
    rationale(
        title = "媒体权限",
        message = "需要相机和麦克风权限录制视频"
    )
    settings(
        title = "开启媒体权限",
        autoNavigate = false
    )
    source("video_recording")
}
```

## 权限组

框架预定义了以下权限组：

- `LOCATION_GROUP` - 位置权限
- `STORAGE_GROUP` - 存储权限  
- `CAMERA_GROUP` - 相机权限
- `MICROPHONE_GROUP` - 麦克风权限
- `CONTACTS_GROUP` - 联系人权限
- `PHONE_GROUP` - 电话权限
- `SMS_GROUP` - 短信权限
- `CALENDAR_GROUP` - 日历权限
- `SENSORS_GROUP` - 传感器权限
- `NOTIFICATION_GROUP` - 通知权限

## 最佳实践

1. **在合适的时机申请权限** - 在用户需要使用相关功能时申请
2. **提供清晰的权限说明** - 解释为什么需要这个权限
3. **优雅处理权限拒绝** - 提供替代方案或引导用户到设置
4. **避免一次性申请过多权限** - 按需申请，提升用户体验
5. **使用权限组管理相关权限** - 简化权限管理逻辑

## 重要注意事项

### ⚠️ 初始化时机
**权限管理器必须在Activity的onCreate中初始化，且在setContent之前！**

```kotlin
// ✅ 正确的初始化方式
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 在setContent之前初始化
        PermissionManager.getInstance(this).initialize(this)

        setContent { /* UI内容 */ }
    }
}

// ❌ 错误的初始化方式 - 会导致崩溃
setContent {
    LaunchedEffect(Unit) {
        PermissionManager.getInstance(this@MainActivity).initialize(this@MainActivity) // 崩溃！
    }
}
```

### 其他注意事项

1. 确保在 `AndroidManifest.xml` 中声明所需权限
2. 权限框架会自动处理Android版本差异
3. 所有API都是线程安全的
4. 权限管理器会自动处理Activity生命周期
