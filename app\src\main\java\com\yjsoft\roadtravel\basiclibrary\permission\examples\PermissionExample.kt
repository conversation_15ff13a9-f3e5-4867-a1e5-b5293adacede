package com.yjsoft.roadtravel.basiclibrary.permission.examples

import android.Manifest
import android.os.Build
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.yjsoft.roadtravel.basiclibrary.permission.compose.CameraPermissionRequester
import com.yjsoft.roadtravel.basiclibrary.permission.compose.LocationPermissionRequester
import com.yjsoft.roadtravel.basiclibrary.permission.compose.SinglePermissionRequester
import com.yjsoft.roadtravel.basiclibrary.permission.compose.StoragePermissionRequester
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionConfig
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionResult
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionStatus
import com.yjsoft.roadtravel.basiclibrary.permission.ui.PermissionIndicator
import com.yjsoft.roadtravel.basiclibrary.permission.ui.PermissionSummaryIndicator
import com.yjsoft.roadtravel.basiclibrary.permission.viewmodel.PermissionViewModel

import kotlinx.coroutines.launch

/**
 * 权限框架使用示例
 * 展示各种权限申请场景和用法
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun PermissionExampleScreen() {
    val context = LocalContext.current
    val activity = context as ComponentActivity
    
    // 初始化权限配置
    LaunchedEffect(Unit) {
        PermissionConfig.initializeDefaults()
    }
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "权限框架使用示例",
                style = MaterialTheme.typography.headlineMedium
            )
        }
        
        item {
            HorizontalDivider()
        }
        
        // 1. 基础权限申请示例
        item {
            BasicPermissionExample()
        }
        
        item {
            HorizontalDivider()
        }
        
        // 2. 位置权限申请示例
        item {
            LocationPermissionExample()
        }
        
        item {
            HorizontalDivider()
        }

        // 3. 相机权限申请示例
        item {
            CameraPermissionExample()
        }

        item {
            HorizontalDivider()
        }

        // 4. 存储权限申请示例
        item {
            StoragePermissionExample()
        }

        item {
            HorizontalDivider()
        }

        // 5. ViewModel集成示例
        item {
            ViewModelPermissionExample()
        }

        item {
            HorizontalDivider()
        }
        
        // 6. 权限状态显示示例
        item {
            PermissionStatusExample()
        }
    }
}

/**
 * 基础权限申请示例
 */
@Composable
fun BasicPermissionExample() {
    var result by remember { mutableStateOf<String>("未申请") }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "1. 基础权限申请",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "结果: $result",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            SinglePermissionRequester(
                permission = Manifest.permission.CAMERA,
                onPermissionResult = { granted ->
                    result = if (granted) "相机权限已授予" else "相机权限被拒绝"
                }
            ) { state ->
                val scope = rememberCoroutineScope()
                
                Button(
                    onClick = {
                        scope.launch {
                            state.requestPermissions()
                        }
                    },
                    enabled = !state.isRequesting
                ) {
                    if (state.isRequesting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text("申请相机权限")
                    }
                }
            }
        }
    }
}

/**
 * 位置权限申请示例
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun LocationPermissionExample() {
    var result by remember { mutableStateOf<String>("未申请") }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "2. 位置权限申请",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "结果: $result",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            LocationPermissionRequester(
                includeBackgroundLocation = false,
                onPermissionResult = { permissionResult ->
                    result = when (permissionResult) {
                        is PermissionResult.Granted -> "位置权限已授予"
                        is PermissionResult.Denied -> "位置权限被拒绝: ${permissionResult.deniedPermissions}"
                        else -> "位置权限申请失败"
                    }
                }
            ) { state ->
                val scope = rememberCoroutineScope()
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = {
                            scope.launch {
                                state.requestPermissions()
                            }
                        },
                        enabled = !state.isRequesting
                    ) {
                        Icon(
                            imageVector = Icons.Default.LocationOn,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("申请位置权限")
                    }
                }
            }
        }
    }
}

/**
 * 相机权限申请示例
 */
@Composable
fun CameraPermissionExample() {
    var result by remember { mutableStateOf<String>("未申请") }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "3. 相机权限申请",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "结果: $result",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            CameraPermissionRequester(
                onPermissionResult = { granted ->
                    result = if (granted) "相机权限已授予" else "相机权限被拒绝"
                }
            ) { state ->
                val scope = rememberCoroutineScope()
                
                Button(
                    onClick = {
                        scope.launch {
                            state.requestPermissions()
                        }
                    },
                    enabled = !state.isRequesting
                ) {
                    Icon(
                        imageVector = Icons.Default.AccountCircle,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("申请相机权限")
                }
            }
        }
    }
}

/**
 * 存储权限申请示例
 */
@Composable
fun StoragePermissionExample() {
    var result by remember { mutableStateOf<String>("未申请") }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "4. 存储权限申请",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "结果: $result",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            StoragePermissionRequester(
                onPermissionResult = { permissionResult ->
                    result = when (permissionResult) {
                        is PermissionResult.Granted -> "存储权限已授予"
                        is PermissionResult.Denied -> "存储权限被拒绝: ${permissionResult.deniedPermissions}"
                        else -> "存储权限申请失败"
                    }
                }
            ) { state ->
                val scope = rememberCoroutineScope()
                
                Button(
                    onClick = {
                        scope.launch {
                            state.requestPermissions()
                        }
                    },
                    enabled = !state.isRequesting
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.List,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("申请存储权限")
                }
            }
        }
    }
}

/**
 * ViewModel集成示例
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun ViewModelPermissionExample() {
    val viewModel: PermissionViewModel = viewModel()
    val context = LocalContext.current
    val activity = context as ComponentActivity
    
    var result by remember { mutableStateOf<String>("未申请") }
    
    // 初始化ViewModel
    LaunchedEffect(Unit) {
        viewModel.initialize(activity)
    }
    
    // 监听权限结果
    val lastResult by viewModel.lastResult.collectAsState()
    LaunchedEffect(lastResult) {
        lastResult?.let { permissionResult ->
            result = when (permissionResult) {
                is PermissionResult.Granted -> "权限已授予: ${permissionResult.permissions}"
                is PermissionResult.Denied -> "权限被拒绝: ${permissionResult.deniedPermissions}"
                else -> "权限申请失败"
            }
        }
    }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "5. ViewModel集成示例",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "结果: $result",
                style = MaterialTheme.typography.bodyMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        viewModel.requestLocationPermissions()
                    }
                ) {
                    Text("位置权限")
                }
                
                Button(
                    onClick = {
                        viewModel.requestCameraPermission()
                    }
                ) {
                    Text("相机权限")
                }
            }
        }
    }
}

/**
 * 权限状态显示示例
 */
@Composable
fun PermissionStatusExample() {
    val context = LocalContext.current
    val activity = context as ComponentActivity
    
    // 模拟权限状态
    val permissionStates = remember {
        listOf(
            PermissionState(
                permission = Manifest.permission.CAMERA,
                status = PermissionStatus.GRANTED
            ),
            PermissionState(
                permission = Manifest.permission.ACCESS_FINE_LOCATION,
                status = PermissionStatus.DENIED
            ),
            PermissionState(
                permission = Manifest.permission.WRITE_EXTERNAL_STORAGE,
                status = PermissionStatus.PERMANENTLY_DENIED
            )
        )
    }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "6. 权限状态显示示例",
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 权限摘要指示器
            PermissionSummaryIndicator(
                permissionStates = permissionStates
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 单个权限指示器
            permissionStates.forEach { permissionState ->
                PermissionIndicator(
                    permissionState = permissionState,
                    showLabel = true,
                    showDescription = true
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}
