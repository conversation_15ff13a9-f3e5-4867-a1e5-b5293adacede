package com.yjsoft.roadtravel.basiclibrary.image.loader

import android.content.Context
import coil3.ImageLoader
import com.yjsoft.roadtravel.basiclibrary.image.config.ImageConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import okhttp3.OkHttpClient

/**
 * ImageLoader工厂类
 * 负责根据不同场景创建特定配置的ImageLoader实例
 */
object ImageLoaderFactory {
    
    /**
     * ImageLoader类型枚举
     */
    enum class LoaderType {
        DEFAULT,        // 默认加载器
        AVATAR,         // 头像专用加载器
        THUMBNAIL,      // 缩略图加载器
        HIGH_QUALITY,   // 高质量图片加载器
        PERFORMANCE,    // 性能优先加载器
        OFFLINE         // 离线模式加载器
    }
    
    /**
     * 创建默认ImageLoader
     */
    fun createDefault(
        context: Context,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        val config = ImageConfig.getCurrentConfig()
        return ImageLoaderInstance.getInstance(context, config, okHttpClient).getImageLoader()
    }
    
    /**
     * 创建头像专用ImageLoader
     * 特点：小尺寸、圆形裁剪、快速加载
     */
    fun createAvatarLoader(
        context: Context,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        val config = ImageConfig.getCurrentConfig().copy(
            imageQuality = ImageConfig.ImageQuality.MEDIUM,
            cacheStrategy = ImageConfig.CacheStrategy.MEMORY_AND_DISK,
            memoryCacheSizePercent = 0.15, // 头像通常较小，减少内存占用
            crossfadeDurationMs = 200, // 更快的动画
            allowRgb565 = true // 头像可以使用RGB565节省内存
        )
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建头像专用ImageLoader")
        return ImageLoaderInstance.getInstance(context, config, okHttpClient).getImageLoader()
    }
    
    /**
     * 创建缩略图ImageLoader
     * 特点：低质量、快速加载、内存优先
     */
    fun createThumbnailLoader(
        context: Context,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        val config = ImageConfig.getCurrentConfig().copy(
            imageQuality = ImageConfig.ImageQuality.LOW,
            cacheStrategy = ImageConfig.CacheStrategy.MEMORY_ONLY,
            memoryCacheSizePercent = 0.10,
            crossfadeDurationMs = 100,
            allowRgb565 = true,
            allowHardwareBitmaps = true
        )
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建缩略图专用ImageLoader")
        return ImageLoaderInstance.getInstance(context, config, okHttpClient).getImageLoader()
    }
    
    /**
     * 创建高质量ImageLoader
     * 特点：高质量、大缓存、适合详情页
     */
    fun createHighQualityLoader(
        context: Context,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        val config = ImageConfig.getCurrentConfig().copy(
            imageQuality = ImageConfig.ImageQuality.HIGH,
            cacheStrategy = ImageConfig.CacheStrategy.MEMORY_AND_DISK,
            memoryCacheSizePercent = 0.35,
            diskCacheSizeMB = 500L,
            crossfadeDurationMs = 400,
            allowRgb565 = false,
            allowHardwareBitmaps = true
        )
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建高质量ImageLoader")
        return ImageLoaderInstance.getInstance(context, config, okHttpClient).getImageLoader()
    }
    
    /**
     * 创建性能优先ImageLoader
     * 特点：最小内存占用、最快加载速度
     */
    fun createPerformanceLoader(
        context: Context,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        val config = ImageConfig.createPerformanceConfig(okHttpClient)
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建性能优先ImageLoader")
        return ImageLoaderInstance.getInstance(context, config, okHttpClient).getImageLoader()
    }
    
    /**
     * 创建离线模式ImageLoader
     * 特点：仅使用缓存，不进行网络请求
     */
    fun createOfflineLoader(
        context: Context,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        val config = ImageConfig.getCurrentConfig().copy(
            cacheStrategy = ImageConfig.CacheStrategy.MEMORY_AND_DISK,
            // 注意：Coil3中离线模式需要通过自定义Fetcher实现
            // 这里先使用基础配置，后续可以扩展
        )
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建离线模式ImageLoader")
        return ImageLoaderInstance.getInstance(context, config, okHttpClient).getImageLoader()
    }
    
    /**
     * 根据类型创建ImageLoader
     */
    fun createByType(
        type: LoaderType,
        context: Context,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        return when (type) {
            LoaderType.DEFAULT -> createDefault(context, okHttpClient)
            LoaderType.AVATAR -> createAvatarLoader(context, okHttpClient)
            LoaderType.THUMBNAIL -> createThumbnailLoader(context, okHttpClient)
            LoaderType.HIGH_QUALITY -> createHighQualityLoader(context, okHttpClient)
            LoaderType.PERFORMANCE -> createPerformanceLoader(context, okHttpClient)
            LoaderType.OFFLINE -> createOfflineLoader(context, okHttpClient)
        }
    }
    
    /**
     * 根据使用场景自动选择最适合的ImageLoader类型
     */
    fun getRecommendedType(
        imageSize: ImageSize = ImageSize.MEDIUM,
        loadingContext: LoadingContext = LoadingContext.NORMAL,
        networkCondition: NetworkCondition = NetworkCondition.GOOD
    ): LoaderType {
        return when {
            // 头像场景
            imageSize == ImageSize.SMALL && loadingContext == LoadingContext.AVATAR -> LoaderType.AVATAR
            
            // 缩略图场景
            imageSize == ImageSize.SMALL && loadingContext == LoadingContext.LIST -> LoaderType.THUMBNAIL
            
            // 高质量场景
            imageSize == ImageSize.LARGE && loadingContext == LoadingContext.DETAIL -> LoaderType.HIGH_QUALITY
            
            // 性能优先场景
            networkCondition == NetworkCondition.POOR || loadingContext == LoadingContext.PERFORMANCE_CRITICAL -> LoaderType.PERFORMANCE
            
            // 离线场景
            networkCondition == NetworkCondition.OFFLINE -> LoaderType.OFFLINE
            
            // 默认场景
            else -> LoaderType.DEFAULT
        }
    }
    
    /**
     * 图片尺寸枚举
     */
    enum class ImageSize {
        SMALL,      // 小图（如头像、图标）
        MEDIUM,     // 中等图片（如列表项图片）
        LARGE       // 大图（如详情页图片、轮播图）
    }
    
    /**
     * 加载上下文枚举
     */
    enum class LoadingContext {
        NORMAL,                 // 普通场景
        LIST,                   // 列表场景
        DETAIL,                 // 详情页场景
        AVATAR,                 // 头像场景
        PERFORMANCE_CRITICAL    // 性能关键场景
    }
    
    /**
     * 网络状况枚举
     */
    enum class NetworkCondition {
        GOOD,       // 网络良好
        POOR,       // 网络较差
        OFFLINE     // 离线状态
    }
    
    /**
     * 创建自定义配置的ImageLoader
     */
    fun createCustom(
        context: Context,
        configBuilder: (ImageConfig.ImageLoadConfig) -> ImageConfig.ImageLoadConfig,
        okHttpClient: OkHttpClient? = null
    ): ImageLoader {
        val baseConfig = ImageConfig.getCurrentConfig()
        val customConfig = configBuilder(baseConfig)
        
        LogManager.tag(LogConfig.Tags.IMAGE).d("创建自定义配置ImageLoader")
        return ImageLoaderInstance.getInstance(context, customConfig, okHttpClient).getImageLoader()
    }
    
    /**
     * 获取工厂状态信息
     */
    fun getFactoryInfo(): String {
        return buildString {
            appendLine("=== ImageLoader工厂信息 ===")
            appendLine("支持的加载器类型:")
            LoaderType.values().forEach { type ->
                appendLine("  - ${type.name}: ${getTypeDescription(type)}")
            }
        }
    }
    
    /**
     * 获取类型描述
     */
    private fun getTypeDescription(type: LoaderType): String {
        return when (type) {
            LoaderType.DEFAULT -> "默认配置，适合大多数场景"
            LoaderType.AVATAR -> "头像专用，小尺寸快速加载"
            LoaderType.THUMBNAIL -> "缩略图专用，低质量快速显示"
            LoaderType.HIGH_QUALITY -> "高质量图片，适合详情页"
            LoaderType.PERFORMANCE -> "性能优先，最小资源占用"
            LoaderType.OFFLINE -> "离线模式，仅使用缓存"
        }
    }
}
