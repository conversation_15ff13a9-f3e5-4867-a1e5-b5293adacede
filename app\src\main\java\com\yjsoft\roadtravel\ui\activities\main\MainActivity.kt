package com.yjsoft.roadtravel.ui.activities.main

import android.os.Build
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.core.view.WindowCompat
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.yjsoft.roadtravel.R
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.navigation.core.navigateToAIChat
import com.yjsoft.roadtravel.basiclibrary.navigation.models.NavigationParams
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionManager
import com.yjsoft.roadtravel.ui.components.GradientBackground
import com.yjsoft.roadtravel.ui.fragments.home.HomeScreen
import com.yjsoft.roadtravel.ui.fragments.profile.ProfileScreen
import com.yjsoft.roadtravel.ui.theme.BottomNavTextSelected
import com.yjsoft.roadtravel.ui.theme.BottomNavTextUnselected
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


/**
 * 主Activity - MVVM架构
 *
 * 功能：
 * - 底部导航管理
 * - Fragment容器
 * - 全局状态管理
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@AndroidEntryPoint
class MainActivity : BaseActivity() {

    private val viewModel: MainViewModel by viewModels()

    //    @RequiresApi(Build.VERSION_CODES.Q)
    @RequiresApi(Build.VERSION_CODES.Q)
    override fun onCreate(savedInstanceState: Bundle?) {
        // 安装SplashScreen（必须在super.onCreate之前）
        installSplashScreen()
        super.onCreate(savedInstanceState)
        // 初始化权限管理器（必须在onCreate中，setContent之前）
        PermissionManager.getInstance(this).initialize(this)

        //穿透状态栏
        enableEdgeToEdge()

        // 设置返回键处理
        setupBackPressedHandler()

        setupContent {
            MainScreenWithSplash(viewModel = viewModel)
        }

        observeEvents(
            uiEvents = viewModel.uiEvents
        )

        // Observe navigation events
        observeNavigationEvents()
    }

    /**
     * 设置返回键处理器
     */
    private fun setupBackPressedHandler() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (!viewModel.handleBackPressed()) {
                    // 如果ViewModel没有处理返回键，则执行默认行为
                    isEnabled = false
                    onBackPressedDispatcher.onBackPressed()
                }
            }
        })
    }

    /**
     * 观察导航事件
     * 增强版本：处理所有类型的导航事件，包括程序化导航
     */
    private fun observeNavigationEvents() {
        lifecycleScope.launch {
            viewModel.tabNavigationEvents.collect { event ->
                handleNavigationEvent(event)
            }
        }
    }

    /**
     * 处理导航事件
     * 增强版本：支持更完整的事件处理和错误恢复
     */
    private fun handleNavigationEvent(event: NavigationEvent) {
        try {
            when (event) {
                is NavigationEvent.NavigateToTab -> {
                }

                NavigationEvent.ShowExitToast -> {
                    showToast("再按一次返回键退出应用")
                }

                NavigationEvent.ExitApp -> {
                    finish()
                }
            }
        } catch (e: Exception) {
            // 导航事件处理失败时的恢复机制
            showToast("导航操作失败，请重试")
        }
    }
}

/**
 * 带开屏页的主屏幕组件
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun MainScreenWithSplash(viewModel: MainViewModel) {
    var showSplash by remember { mutableStateOf(true) }

    if (showSplash) {
        // 显示自定义开屏页
        CustomSplashScreen(
            onSplashFinished = { showSplash = false }
        )
    } else {
        // 显示主界面
        MainScreen(viewModel = viewModel)
    }
}

/**
 * 自定义开屏页组件
 */
@Composable
fun CustomSplashScreen(
    onSplashFinished: () -> Unit
) {
    // 配置状态栏样式 - 透明状态栏，浅色图标
    val view = LocalView.current
    SideEffect {
        val window = (view.context as MainActivity).window
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val insetsController = WindowCompat.getInsetsController(window, view)
        insetsController.isAppearanceLightStatusBars = true
    }

    // 延迟跳转逻辑
    LaunchedEffect(Unit) {
        delay(1000) // 延迟1秒
        onSplashFinished()
    }

    // 开屏页面UI
    GradientBackground {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // splash_top图片：距离顶部120dp，尺寸375*375，水平居中
            Image(
                painter = painterResource(id = R.drawable.splash_top),
                contentDescription = "应用启动图标",
                modifier = Modifier
                    .size(375.dp, 375.dp)
                    .align(Alignment.TopCenter)
                    .offset(y = 120.dp)
            )

            // splash_bottom图片：距离底部80dp，尺寸120*30，水平居中
            Image(
                painter = painterResource(id = R.drawable.splash_bottom),
                contentDescription = "应用底部标识",
                modifier = Modifier
                    .size(120.dp, 30.dp)
                    .align(Alignment.BottomCenter)
                    .offset(y = (-80).dp)
            )
        }
    }
}

/**
 * 主屏幕组件
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun MainScreen(viewModel: MainViewModel) {
    val selectedBottomNavItem by viewModel.selectedBottomNavItem.collectAsStateWithLifecycle()
    val navController = rememberNavController()
    val context = LocalContext.current
    val navBackStackEntry by navController.currentBackStackEntryAsState()

    // 增强的程序化导航事件处理（如返回键导航）
    // 注意：用户点击导航使用直接导航，程序化导航使用事件系统
    LaunchedEffect(navController) {
        viewModel.tabNavigationEvents.collect { event ->
            when (event) {
                is NavigationEvent.NavigateToTab -> {
                    handleProgrammaticNavigation(navController, event.tab)
                }

                else -> { /* 其他事件在MainActivity中处理 */
                }
            }
        }
    }

    // 增强的NavController状态监听，确保ViewModel状态同步
    LaunchedEffect(navBackStackEntry) {
        handleNavControllerStateChange(navBackStackEntry, selectedBottomNavItem, viewModel)
    }

    // 配置状态栏样式 - 固定样式，不跟随系统色改变
    val view = LocalView.current
    SideEffect {
        val window = (view.context as MainActivity).window
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val insetsController = WindowCompat.getInsetsController(window, view)
        insetsController.isAppearanceLightStatusBars = true
    }

    GradientBackground {
        Scaffold(
            modifier = Modifier.fillMaxSize(),
            containerColor = Color.Transparent, // 让Scaffold背景透明
            bottomBar = {
                CustomBottomNavigationBar(
                    selectedItem = selectedBottomNavItem,
                    onItemSelected = { item ->
                        handleUserTabSelection(item, navController, viewModel)
                    },
                    onAiButtonClick = {
                        val conversationId = ""
//                        conversationId =
                        // 使用NavigationManager跳转到AI规划Activity，界面进入动画从下向上
                        (context as MainActivity).navigateToAIChat(
                            params = NavigationParams.builder()
                                .putString("conversationId", conversationId)
                                .build()
                        )
                    }
                )
            }
        ) { paddingValues ->
            NavHost(
                navController = navController,
                startDestination = BottomNavItem.HOME.route,
                modifier = Modifier.padding(paddingValues)
            ) {
                composable(BottomNavItem.HOME.route) {
                    HomeScreen()
                }
                composable(BottomNavItem.PROFILE.route) {
                    ProfileScreen()
                }
            }
        }
    }
}

/**
 * 处理程序化导航（如返回键触发的导航）
 * 增强版本：包含错误处理、状态验证和恢复机制
 */
private suspend fun handleProgrammaticNavigation(
    navController: NavController,
    targetTab: BottomNavItem
) {
    val currentRoute = navController.currentDestination?.route
    val targetRoute = targetTab.route

    android.util.Log.d("MainActivity", "程序化导航: $currentRoute -> $targetRoute")

    if (currentRoute != targetRoute) {
        try {
            // 验证导航目标是否有效
            val isValidTarget = when (targetRoute) {
                BottomNavItem.HOME.route, BottomNavItem.PROFILE.route -> true
                else -> false
            }

            if (!isValidTarget) {
                android.util.Log.w("MainActivity", "无效的导航目标: $targetRoute")
                return
            }

            // 执行导航
            navController.navigate(targetRoute) {
                popUpTo(navController.graph.findStartDestination().id) {
                    saveState = true
                }
                launchSingleTop = true
                restoreState = true
            }

            android.util.Log.d("MainActivity", "程序化导航成功: ${targetTab.title}")

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "程序化导航失败: ${e.message}", e)

            // 导航失败时的恢复机制
            try {
                // 尝试导航到首页作为fallback
                if (targetRoute != BottomNavItem.HOME.route) {
                    navController.navigate(BottomNavItem.HOME.route) {
                        popUpTo(navController.graph.findStartDestination().id) {
                            saveState = true
                        }
                        launchSingleTop = true
                        restoreState = true
                    }
                    android.util.Log.d("MainActivity", "导航失败，已恢复到首页")
                }
            } catch (fallbackException: Exception) {
                android.util.Log.e(
                    "MainActivity",
                    "导航恢复也失败: ${fallbackException.message}",
                    fallbackException
                )
            }
        }
    } else {
        android.util.Log.d("MainActivity", "目标路由与当前路由相同，跳过导航")
    }
}

/**
 * 处理NavController状态变化，确保与ViewModel状态同步
 * 增强版本：包含状态验证、冲突解决和错误恢复
 */
private fun handleNavControllerStateChange(
    navBackStackEntry: androidx.navigation.NavBackStackEntry?,
    selectedBottomNavItem: BottomNavItem,
    viewModel: MainViewModel
) {
    navBackStackEntry?.destination?.route?.let { currentRoute ->
        val expectedTab = when (currentRoute) {
            BottomNavItem.HOME.route -> BottomNavItem.HOME
            BottomNavItem.PROFILE.route -> BottomNavItem.PROFILE
            else -> {
                android.util.Log.w("MainActivity", "未知的路由: $currentRoute")
                null
            }
        }

        expectedTab?.let { tab ->
            // 检查ViewModel状态是否与NavController状态匹配
            if (selectedBottomNavItem != tab) {
                android.util.Log.d(
                    "MainActivity",
                    "检测到状态不同步 - NavController: ${tab.title}, ViewModel: ${selectedBottomNavItem.title}"
                )

                // 验证状态变化是否合理
                val isValidStateChange = isValidNavigationStateChange(selectedBottomNavItem, tab)

                if (isValidStateChange) {
                    // 使用内部同步方法，避免触发导航事件循环
                    viewModel.syncNavigationState(tab)
                    android.util.Log.d(
                        "MainActivity",
                        "ViewModel状态已同步: ${selectedBottomNavItem.title} -> ${tab.title}"
                    )
                } else {
                    android.util.Log.w(
                        "MainActivity",
                        "状态变化被拒绝: ${selectedBottomNavItem.title} -> ${tab.title}"
                    )
                }
            }
        }
    }
}

/**
 * 验证导航状态变化是否合理
 * 防止无效的状态同步
 */
private fun isValidNavigationStateChange(from: BottomNavItem, to: BottomNavItem): Boolean {
    // 所有定义的底部导航项之间的切换都是有效的
    val validTabs = setOf(BottomNavItem.HOME, BottomNavItem.PROFILE)
    return from in validTabs && to in validTabs
}

/**
 * 处理用户点击底部导航栏的选择
 * 增强版本：确保与程序化导航的协调和错误处理
 */
private fun handleUserTabSelection(
    item: BottomNavItem,
    navController: NavController,
    viewModel: MainViewModel
) {
    try {
        android.util.Log.d("MainActivity", "用户选择导航项: ${item.title}")

        // 更新ViewModel状态
        viewModel.selectBottomNavItem(item)

        // 执行导航（用于用户点击导航）
        val currentRoute = navController.currentDestination?.route
        if (currentRoute != item.route) {
            navController.navigate(item.route) {
                popUpTo(navController.graph.findStartDestination().id) {
                    saveState = true
                }
                launchSingleTop = true
                restoreState = true
            }
            android.util.Log.d("MainActivity", "用户导航成功: $currentRoute -> ${item.route}")
        } else {
            android.util.Log.d("MainActivity", "用户点击当前页面，无需导航")
        }

    } catch (e: Exception) {
        android.util.Log.e("MainActivity", "用户导航失败: ${e.message}", e)
        // 用户导航失败时，尝试恢复到一致状态
        try {
            // 重新同步ViewModel状态到当前NavController状态
            navController.currentDestination?.route?.let { currentRoute ->
                val currentTab = when (currentRoute) {
                    BottomNavItem.HOME.route -> BottomNavItem.HOME
                    BottomNavItem.PROFILE.route -> BottomNavItem.PROFILE
                    else -> BottomNavItem.HOME // 默认到首页
                }
                viewModel.syncNavigationState(currentTab)
                android.util.Log.d("MainActivity", "用户导航失败后状态已恢复")
            }
        } catch (recoveryException: Exception) {
            android.util.Log.e(
                "MainActivity",
                "状态恢复也失败: ${recoveryException.message}",
                recoveryException
            )
        }
    }
}

/**
 * 自定义底部导航栏组件
 */
@Composable
fun CustomBottomNavigationBar(
    selectedItem: BottomNavItem,
    onItemSelected: (BottomNavItem) -> Unit,
    onAiButtonClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        // 首页导航项
        CustomNavItem(
            item = BottomNavItem.HOME,
            isSelected = selectedItem == BottomNavItem.HOME,
            onClick = { onItemSelected(BottomNavItem.HOME) }
        )

        // AI规划导航项（中间特殊按钮）
        Box(
            modifier = Modifier.offset(y = (-5).dp) // 中间按钮向上越界5dp
        ) {
            CustomAiNavItem(
                onClick = onAiButtonClick
            )
        }

        // 我的导航项
        CustomNavItem(
            item = BottomNavItem.PROFILE,
            isSelected = selectedItem == BottomNavItem.PROFILE,
            onClick = { onItemSelected(BottomNavItem.PROFILE) }
        )
    }
}

/**
 * 普通导航项组件（首页、我的）
 */
@Composable
fun CustomNavItem(
    item: BottomNavItem,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .clickable(
                indication = null, // 移除点击的半透明黑色效果
                interactionSource = remember { MutableInteractionSource() }
            ) { onClick() }
            .padding(horizontal = 12.dp, vertical = 5.dp), // 增加点击范围
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 图标（24*24）
        Image(
            painter = painterResource(
                id = when (item) {
                    BottomNavItem.HOME -> if (isSelected) R.drawable.home_selected else R.drawable.home_unselected
                    BottomNavItem.PROFILE -> if (isSelected) R.drawable.profile_selected else R.drawable.profile_unselected
                    else -> R.drawable.home_unselected // 默认情况
                }
            ),
            contentDescription = item.title,
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.height(4.dp))

        // 文字（12sp）
        Text(
            text = item.title,
            fontSize = 12.sp,
            color = if (isSelected) BottomNavTextSelected else BottomNavTextUnselected,
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
        )
    }
}

/**
 * AI规划导航项组件（中间特殊按钮）
 */
@Composable
fun CustomAiNavItem(
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clickable(
                indication = null, // 移除点击的半透明黑色效果
                interactionSource = remember { MutableInteractionSource() }
            ) { onClick() }
            .padding(0.dp), // 增加内间距，扩大可点击范围
        contentAlignment = Alignment.Center
    ) {
        // 带白色圆环的AI图标
        Box(
            modifier = Modifier
                .size(58.dp) // 42 + 8*2 = 58dp
                .clip(CircleShape)
                .background(Color.White),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.ai_travel),
                contentDescription = "AI规划",
                modifier = Modifier.size(42.dp)
            )
        }
    }
}

