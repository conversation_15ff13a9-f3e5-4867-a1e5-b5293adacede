# 支付框架 (Payment Framework)

## 概述

统一支付框架，支持微信支付、支付宝、云闪付等国内主流支付方式。基于策略模式设计，提供统一的API接口和现代化的Compose UI组件。

## 特性

- 🚀 **统一接口**：一套API适配多种支付方式
- 📱 **现代化UI**：基于Jetpack Compose的支付组件
- 🔄 **响应式状态**：基于协程和Flow的状态管理
- 🛡️ **安全可靠**：完善的错误处理和安全机制
- 🔧 **易于扩展**：策略模式设计，便于添加新的支付方式
- 📦 **开箱即用**：提供完整的UI组件和使用示例

## 支持的支付方式

- ✅ 支付宝 (Alipay)
- ✅ 微信支付 (WeChat Pay)  
- ✅ 云闪付 (UnionPay)

## 快速开始

### 1. 初始化

支付框架已在Application中自动初始化，无需手动初始化。

```kotlin
// 已在RoadTravelApplication中自动初始化
class RoadTravelApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        // 支付框架自动初始化
        initializePaymentFramework()
    }
}
```

### 2. 配置支付参数

在实际使用前，需要配置真实的支付参数：

```kotlin
// 配置支付宝参数
PaymentConfig.configureAlipay(
    appId = "your_alipay_app_id",
    privateKey = "your_alipay_private_key",
    publicKey = "your_alipay_public_key"
)

// 配置微信支付参数
PaymentConfig.configureWeChatPay(
    appId = "your_wechat_app_id",
    merchantId = "your_wechat_merchant_id",
    apiKey = "your_wechat_api_key"
)
```

### 3. 发起支付

#### 方式一：使用PaymentViewModel

```kotlin
@Composable
fun OrderScreen(viewModel: PaymentViewModel = viewModel()) {
    val context = LocalContext.current
    val paymentState by viewModel.paymentState.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.init(context)
    }

    Button(
        onClick = {
            val request = PaymentRequest.simple(
                orderId = "ORDER_${System.currentTimeMillis()}",
                amount = 99.99,
                title = "商品购买"
            )

            viewModel.startPayment(
                context = context,
                paymentType = PaymentType.ALIPAY,
                request = request
            ) { result ->
                // 处理支付结果
                when (result) {
                    is PaymentResult.Success -> {
                        // 支付成功
                    }
                    is PaymentResult.Cancel -> {
                        // 用户取消
                    }
                    is PaymentResult.Error -> {
                        // 支付失败
                    }
                }
            }
        }
    ) {
        Text("支付")
    }
}
```

#### 方式二：直接使用PaymentManager

```kotlin
class OrderViewModel : ViewModel() {
    suspend fun pay(context: Context, paymentType: PaymentType, amount: Double) {
        val request = PaymentRequest.simple(
            orderId = "ORDER_${System.currentTimeMillis()}",
            amount = amount,
            title = "商品购买"
        )

        val result = PaymentManager.pay(context, paymentType, request)
        when (result) {
            is PaymentResult.Success -> {
                // 支付成功
                LogManager.d("支付成功: ${result.transactionId}")
            }
            is PaymentResult.Cancel -> {
                // 用户取消
                LogManager.d("支付取消: ${result.reason}")
            }
            is PaymentResult.Error -> {
                // 支付失败
                LogManager.e("支付失败: ${result.errorMessage}")
            }
            is PaymentResult.Processing -> {
                // 支付处理中
                LogManager.d("支付处理中: ${result.message}")
            }
        }
    }
}
```

### 4. 使用UI组件

#### PaymentButton - 支付按钮

```kotlin
@Composable
fun SimplePaymentButton() {
    PaymentButton(
        amount = 99.99,
        paymentTypes = listOf(PaymentType.ALIPAY, PaymentType.WECHAT_PAY),
        orderId = "ORDER_123",
        title = "商品购买",
        onPaymentResult = { result ->
            when (result) {
                is PaymentResult.Success -> {
                    // 支付成功处理
                }
                is PaymentResult.Cancel -> {
                    // 支付取消处理
                }
                is PaymentResult.Error -> {
                    // 支付失败处理
                }
            }
        }
    )
}
```

#### SinglePaymentButton - 单一支付方式按钮

```kotlin
@Composable
fun AlipayButton() {
    SinglePaymentButton(
        paymentType = PaymentType.ALIPAY,
        amount = 99.99,
        title = "支付宝支付",
        onPaymentResult = { result ->
            // 处理支付结果
        }
    )
}
```

#### PaymentScreen - 完整支付页面

```kotlin
@Composable
fun OrderPaymentScreen() {
    val orderInfo = OrderInfo(
        orderId = "ORDER_123456789",
        title = "商品名称",
        description = "商品描述",
        amount = 99.99
    )

    PaymentScreen(
        orderInfo = orderInfo,
        onNavigateBack = {
            // 返回上一页
        },
        onPaymentComplete = { result ->
            when (result) {
                is PaymentResult.Success -> {
                    // 跳转到支付成功页面
                }
                is PaymentResult.Cancel -> {
                    // 处理支付取消
                }
                is PaymentResult.Error -> {
                    // 处理支付失败
                }
            }
        }
    )
}
```

## 架构设计

```
payment/
├── config/          # 配置和常量
├── core/            # 核心接口和管理器
├── strategies/      # 支付策略实现
├── models/          # 数据模型
├── repository/      # 数据仓库
├── state/           # 状态管理
├── ui/              # UI组件
└── utils/           # 工具类
```

## 更新日志

### v1.0.0 (2025-07-21)
- 初始版本发布
- 支持支付宝、微信支付、云闪付
- 提供完整的Compose UI组件
- 基于策略模式的可扩展架构

## 许可证

本项目遵循项目主许可证。
