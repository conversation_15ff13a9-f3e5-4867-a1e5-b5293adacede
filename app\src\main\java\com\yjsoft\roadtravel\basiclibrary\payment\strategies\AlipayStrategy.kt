package com.yjsoft.roadtravel.basiclibrary.payment.strategies

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import com.alipay.sdk.app.PayTask
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConfig
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConstants
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentStrategy
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.utils.PaymentUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.net.URLEncoder
import kotlin.coroutines.resume

/**
 * 支付宝支付策略实现
 * 集成支付宝SDK，提供支付宝支付功能
 */
class AlipayStrategy : PaymentStrategy {
    
    companion object {
        private const val TAG = "AlipayStrategy %s"
        private const val ALIPAY_SDK_VERSION = "15.8.18"
    }
    
    override fun getPaymentType(): PaymentType = PaymentType.ALIPAY
    
    override suspend fun isAvailable(context: Context): Boolean {
        return try {
            // 检查支付宝SDK是否可用
            val payTask = PayTask(context as Activity)
            val version = payTask.version
            LogManager.d(TAG, "支付宝SDK版本: $version")
            !TextUtils.isEmpty(version) && isConfigured()
        } catch (e: Exception) {
            LogManager.e(TAG, "检查支付宝可用性失败", e)
            false
        }
    }
    
    override suspend fun initialize(context: Context): Boolean {
        return try {
            // 支付宝SDK不需要特殊初始化
            LogManager.d(TAG, "支付宝SDK初始化完成")
            true
        } catch (e: Exception) {
            LogManager.e(TAG, "支付宝SDK初始化失败", e)
            false
        }
    }
    
    override suspend fun pay(context: Context, request: PaymentRequest): PaymentResult = withContext(Dispatchers.IO) {
        LogManager.d(TAG, "开始支付宝支付，订单ID: ${request.orderId}")
        
        try {
            // 构建支付参数
            val payInfo = buildPayInfo(request)
            LogManager.d(TAG, "支付参数: $payInfo")
            
            // 调用支付宝SDK进行支付
            val result = callAlipaySDK(context, payInfo)
            
            // 解析支付结果
            parsePaymentResult(request, result)
            
        } catch (e: Exception) {
            LogManager.e(TAG, "支付宝支付异常", e)
            PaymentResult.Error(
                orderId = request.orderId,
                paymentType = PaymentType.ALIPAY,
                errorCode = "ALIPAY_EXCEPTION",
                errorMessage = e.message ?: "支付宝支付异常",
                cause = e
            )
        }
    }
    
    override fun isConfigured(): Boolean {
        return PaymentConfig.isAlipayConfigured()
    }
    
    override fun getSdkVersion(): String = ALIPAY_SDK_VERSION
    
    override fun cleanup() {
        LogManager.d(TAG, "支付宝策略清理完成")
    }
    
    /**
     * 构建支付宝支付参数
     */
    private fun buildPayInfo(request: PaymentRequest): String {
        val params = mutableMapOf<String, String>()
        
        // 基本参数
        params[PaymentConstants.ParamKey.Alipay.APP_ID] = PaymentConfig.alipayAppId ?: ""
        params[PaymentConstants.ParamKey.Alipay.METHOD] = "alipay.trade.app.pay"
        params[PaymentConstants.ParamKey.Alipay.CHARSET] = PaymentConstants.Security.CHARSET_UTF8
        params[PaymentConstants.ParamKey.Alipay.SIGN_TYPE] = PaymentConstants.Security.SIGN_TYPE_RSA2
        params[PaymentConstants.ParamKey.Alipay.VERSION] = "1.0"
        params[PaymentConstants.ParamKey.TIMESTAMP] = PaymentUtils.getCurrentTimestamp()
        
        // 业务参数
        val bizContent = buildBizContent(request)
        params[PaymentConstants.ParamKey.Alipay.BIZ_CONTENT] = bizContent
        
        // 生成签名
        val sign = PaymentUtils.generateAlipaySign(params, PaymentConfig.alipayPrivateKey ?: "")
        params[PaymentConstants.ParamKey.SIGN] = sign
        
        // 构建最终的支付参数字符串
        return buildPayInfoString(params)
    }
    
    /**
     * 构建业务参数
     */
    private fun buildBizContent(request: PaymentRequest): String {
        val bizParams = mutableMapOf<String, Any>()
        bizParams["out_trade_no"] = request.orderId
        bizParams["total_amount"] = request.getAmountString()
        bizParams["subject"] = request.title
        bizParams["product_code"] = "QUICK_MSECURITY_PAY"
        
        request.description?.let {
            bizParams["body"] = it
        }
        
        request.notifyUrl?.let {
            bizParams["notify_url"] = it
        }
        
        return PaymentUtils.toJsonString(bizParams)
    }
    
    /**
     * 构建支付参数字符串
     */
    private fun buildPayInfoString(params: Map<String, String>): String {
        val sortedParams = params.toSortedMap()
        val paramList = mutableListOf<String>()
        
        for ((key, value) in sortedParams) {
            if (value.isNotEmpty()) {
                paramList.add("$key=${URLEncoder.encode(value, "UTF-8")}")
            }
        }
        
        return paramList.joinToString("&")
    }
    
    /**
     * 调用支付宝SDK
     */
    private suspend fun callAlipaySDK(context: Context, payInfo: String): Map<String, String> = 
        suspendCancellableCoroutine { continuation ->
            
            val activity = context as Activity
            val payTask = PayTask(activity)
            
            // 在子线程中调用支付宝SDK
            Thread {
                val result = payTask.payV2(payInfo, true)
                
                // 回到主线程处理结果
                Handler(Looper.getMainLooper()).post {
                    if (continuation.isActive) {
                        continuation.resume(result)
                    }
                }
            }.start()
            
            // 设置取消回调
            continuation.invokeOnCancellation {
                LogManager.d(TAG, "支付宝支付被取消")
            }
        }
    
    /**
     * 解析支付结果
     */
    private fun parsePaymentResult(request: PaymentRequest, result: Map<String, String>): PaymentResult {
        val resultStatus = result["resultStatus"] ?: ""
        val resultInfo = result["result"] ?: ""
        val memo = result["memo"] ?: ""
        
        LogManager.d(TAG, "支付结果 - Status: $resultStatus, Info: $resultInfo, Memo: $memo")
        
        return when (resultStatus) {
            PaymentConstants.ResultCode.Alipay.SUCCESS -> {
                // 解析result字段获取交易号等信息
                val transactionId = PaymentUtils.extractTransactionId(resultInfo)
                PaymentResult.Success(
                    orderId = request.orderId,
                    transactionId = transactionId,
                    amount = request.getAmountString(),
                    paymentType = PaymentType.ALIPAY,
                    extraData = result
                )
            }
            PaymentConstants.ResultCode.Alipay.CANCEL -> {
                PaymentResult.Cancel(
                    orderId = request.orderId,
                    paymentType = PaymentType.ALIPAY,
                    reason = memo
                )
            }
            PaymentConstants.ResultCode.Alipay.PROCESSING -> {
                PaymentResult.Processing(
                    orderId = request.orderId,
                    paymentType = PaymentType.ALIPAY,
                    message = "支付处理中，请稍候..."
                )
            }
            else -> {
                PaymentResult.Error(
                    orderId = request.orderId,
                    paymentType = PaymentType.ALIPAY,
                    errorCode = resultStatus,
                    errorMessage = memo.ifEmpty { "支付宝支付失败" }
                )
            }
        }
    }
}
