package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 日志框架依赖注入模块
 * 
 * 功能：
 * - 提供LogManager的依赖注入
 * - 管理日志框架的生命周期
 * - 兼容现有的手动初始化方式
 * 
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 延迟初始化避免循环依赖
 * - 兼容现有代码逻辑
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object LoggerModule {
    
    /**
     * 提供LogManager实例
     * 
     * 注意：这里不直接调用LogManager.init()，而是返回一个包装器
     * 这样可以保持与现有手动初始化的兼容性
     */
    @Provides
    @Singleton
    fun provideLogManager(
        @ApplicationContext context: Context
    ): LogManager {
        return LogManagerProvider.getInstance(context)
    }
    
    /**
     * LogManager提供器
     * 用于管理LogManager的创建和初始化
     */
    private object LogManagerProvider {
        @Volatile
        private var instance: LogManager? = null
        
        fun getInstance(context: Context): LogManager {
            return instance ?: synchronized(this) {
                instance ?: createLogManager(context).also { instance = it }
            }
        }
        
        private fun createLogManager(context: Context): LogManager {
            // 检查LogManager是否已经通过手动方式初始化
            return if (LogManager.isInitialized()) {
                // 如果已经初始化，直接返回现有实例
                LogManager
            } else {
                // 如果未初始化，进行初始化
                LogManager.apply {
                    init(context)
                }
            }
        }
    }
}

