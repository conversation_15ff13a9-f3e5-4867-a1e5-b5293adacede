package com.yjsoft.roadtravel.basiclibrary.payment.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material3.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentState
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentStatus
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.state.PaymentViewModel
import com.yjsoft.roadtravel.basiclibrary.payment.ui.components.PaymentButton
import com.yjsoft.roadtravel.basiclibrary.payment.ui.components.PaymentMethodItem
import java.util.Locale
import java.math.BigDecimal

/** 完整的支付页面 包含订单信息、支付方式选择、支付状态等完整功能 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentScreen(
        modifier: Modifier = Modifier,
        orderInfo: OrderInfo,
        onNavigateBack: () -> Unit = {},
        onPaymentComplete: (PaymentResult) -> Unit = {},
        viewModel: PaymentViewModel = viewModel()
) {
    val context = LocalContext.current
    val paymentState by viewModel.paymentState.collectAsState()
    val availablePaymentTypes by viewModel.availablePaymentTypes.collectAsState()

    var selectedPaymentType by remember { mutableStateOf<PaymentType?>(null) }

    // 初始化ViewModel
    LaunchedEffect(Unit) { viewModel.init(context) }

    Scaffold() { paddingValues ->
        Column(modifier = modifier.padding(paddingValues).verticalScroll(rememberScrollState())) {
            when (paymentState.status) {
                PaymentStatus.SUCCESS -> {
                    PaymentSuccessContent(
                            result = paymentState.result as? PaymentResult.Success,
                            onComplete = onPaymentComplete
                    )
                }
                PaymentStatus.FAILED -> {
                    PaymentErrorContent(
                            result = paymentState.result as? PaymentResult.Error,
                            onRetry = {
                                selectedPaymentType?.let { paymentType ->
                                    startPayment(
                                            viewModel,
                                            context,
                                            orderInfo,
                                            paymentType,
                                            onPaymentComplete
                                    )
                                }
                            },
                            onComplete = onPaymentComplete
                    )
                }
                PaymentStatus.CANCELLED -> {
                    PaymentCancelContent(
                            result = paymentState.result as? PaymentResult.Cancel,
                            onRetry = {
                                selectedPaymentType?.let { paymentType ->
                                    startPayment(
                                            viewModel,
                                            context,
                                            orderInfo,
                                            paymentType,
                                            onPaymentComplete
                                    )
                                }
                            },
                            onComplete = onPaymentComplete
                    )
                }
                else -> {
                    PaymentFormContent(
                            orderInfo = orderInfo,
                            paymentState = paymentState,
                            availablePaymentTypes = availablePaymentTypes,
                            selectedPaymentType = selectedPaymentType,
                            onPaymentTypeSelected = { selectedPaymentType = it },
                            onStartPayment = { paymentType ->
                                selectedPaymentType = paymentType
                                startPayment(
                                        viewModel,
                                        context,
                                        orderInfo,
                                        paymentType,
                                        onPaymentComplete
                                )
                            }
                    )
                }
            }
        }
    }
}

/** 支付表单内容 */
@Composable
private fun PaymentFormContent(
        orderInfo: OrderInfo,
        paymentState: PaymentState,
        availablePaymentTypes: List<PaymentType>,
        selectedPaymentType: PaymentType?,
        onPaymentTypeSelected: (PaymentType) -> Unit,
        onStartPayment: (PaymentType) -> Unit
) {
    Column(modifier = Modifier.padding(16.dp), verticalArrangement = Arrangement.spacedBy(16.dp)) {
        // 订单信息卡片
        OrderInfoCard(orderInfo = orderInfo)

        // 支付方式选择
        PaymentMethodSection(
                availablePaymentTypes = availablePaymentTypes,
                selectedPaymentType = selectedPaymentType,
                onPaymentTypeSelected = onPaymentTypeSelected,
                enabled = !paymentState.isLoading
        )

        // 支付按钮
        PaymentButton(
                amount = orderInfo.amount,
                paymentTypes = selectedPaymentType?.let { listOf(it) } ?: availablePaymentTypes,
                orderId = orderInfo.orderId,
                title = orderInfo.title,
                description = orderInfo.description,
                onPaymentResult = { /* 由ViewModel处理 */},
                enabled = selectedPaymentType != null && !paymentState.isLoading,
                buttonText = if (paymentState.isLoading) "支付中..." else null
        )

        // 支付状态提示
        if (paymentState.progressMessage != null) {
            Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors =
                            CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                            )
            ) {
                Text(
                        text = paymentState.progressMessage,
                        modifier = Modifier.padding(16.dp),
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/** 订单信息卡片 */
@Composable
private fun OrderInfoCard(orderInfo: OrderInfo) {
    Card(modifier = Modifier.fillMaxWidth(), shape = RoundedCornerShape(12.dp)) {
        Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(text = "订单信息", fontSize = 16.sp, fontWeight = FontWeight.Bold)

            Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("订单号：")
                Text(orderInfo.orderId)
            }

            Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("商品名称：")
                Text(orderInfo.title)
            }

            if (orderInfo.description != null) {
                Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("商品描述：")
                    Text(orderInfo.description)
                }
            }

            HorizontalDivider(Modifier, DividerDefaults.Thickness, DividerDefaults.color)

            Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(text = "支付金额：", fontSize = 16.sp, fontWeight = FontWeight.Medium)
                Text(
                        text = "¥${String.format(Locale.getDefault(), "%.2f", orderInfo.amount)}",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/** 支付方式选择区域 */
@Composable
private fun PaymentMethodSection(
        availablePaymentTypes: List<PaymentType>,
        selectedPaymentType: PaymentType?,
        onPaymentTypeSelected: (PaymentType) -> Unit,
        enabled: Boolean
) {
    Card(modifier = Modifier.fillMaxWidth(), shape = RoundedCornerShape(12.dp)) {
        Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(text = "选择支付方式", fontSize = 16.sp, fontWeight = FontWeight.Bold)

            availablePaymentTypes.forEach { paymentType ->
                PaymentMethodItem(
                        paymentType = paymentType,
                        onClick = { onPaymentTypeSelected(paymentType) },
                        enabled = enabled,
                        modifier =
                                Modifier.then(
                                        if (selectedPaymentType == paymentType) {
                                            Modifier.padding(2.dp)
                                        } else {
                                            Modifier
                                        }
                                )
                )
            }
        }
    }
}

/** 支付成功内容 */
@Composable
private fun PaymentSuccessContent(
        result: PaymentResult.Success?,
        onComplete: (PaymentResult) -> Unit
) {
    Column(
            modifier = Modifier.fillMaxSize().padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
    ) {
        Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = Color(0xFF4CAF50)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "支付成功", fontSize = 24.sp, fontWeight = FontWeight.Bold)

        Spacer(modifier = Modifier.height(8.dp))

        if (result != null) {
            Text(
                    text = "交易号：${result.transactionId}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(text = "支付金额：¥${result.amount}", fontSize = 16.sp, fontWeight = FontWeight.Medium)
        }

        Spacer(modifier = Modifier.height(32.dp))

        Button(onClick = { result?.let { onComplete(it) } }, modifier = Modifier.fillMaxWidth()) {
            Text("完成")
        }
    }
}

/** 支付失败内容 */
@Composable
private fun PaymentErrorContent(
        result: PaymentResult.Error?,
        onRetry: () -> Unit,
        onComplete: (PaymentResult) -> Unit
) {
    Column(
            modifier = Modifier.fillMaxSize().padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
    ) {
        Icon(
                imageVector = Icons.Default.Error,
                contentDescription = null,
                modifier = Modifier.size(80.dp),
                tint = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "支付失败", fontSize = 24.sp, fontWeight = FontWeight.Bold)

        Spacer(modifier = Modifier.height(8.dp))

        if (result != null) {
            Text(
                    text = result.getUserFriendlyMessage(),
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                    onClick = { result?.let { onComplete(it) } },
                    modifier = Modifier.weight(1f)
            ) { Text("取消") }

            Button(onClick = onRetry, modifier = Modifier.weight(1f)) { Text("重试") }
        }
    }
}

/** 支付取消内容 */
@Composable
private fun PaymentCancelContent(
        result: PaymentResult.Cancel?,
        onRetry: () -> Unit,
        onComplete: (PaymentResult) -> Unit
) {
    Column(
            modifier = Modifier.fillMaxSize().padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
    ) {
        Text(text = "支付已取消", fontSize = 24.sp, fontWeight = FontWeight.Bold)

        Spacer(modifier = Modifier.height(8.dp))

        Text(
                text = result?.reason ?: "用户取消了支付",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(32.dp))

        Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                    onClick = { result?.let { onComplete(it) } },
                    modifier = Modifier.weight(1f)
            ) { Text("返回") }

            Button(onClick = onRetry, modifier = Modifier.weight(1f)) { Text("重新支付") }
        }
    }
}

/** 开始支付的辅助函数 */
private fun startPayment(
        viewModel: PaymentViewModel,
        context: android.content.Context,
        orderInfo: OrderInfo,
        paymentType: PaymentType,
        onPaymentComplete: (PaymentResult) -> Unit
) {
    val request =
            PaymentRequest(
                    orderId = orderInfo.orderId,
                    amount = BigDecimal.valueOf(orderInfo.amount),
                    title = orderInfo.title,
                    description = orderInfo.description
            )

    viewModel.startPayment(context, paymentType, request) { result -> onPaymentComplete(result) }
}

/** 订单信息数据类 */
data class OrderInfo(
        val orderId: String,
        val title: String,
        val description: String? = null,
        val amount: Double
)

@Preview(showBackground = true)
@Composable
private fun PaymentScreenPreview() {
    MaterialTheme {
        PaymentScreen(
                orderInfo =
                        OrderInfo(
                                orderId = "ORDER_123456789",
                                title = "测试商品",
                                description = "这是一个测试商品",
                                amount = 99.99
                        )
        )
    }
}
