package com.yjsoft.roadtravel.basiclibrary.location.viewmodel

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.yjsoft.roadtravel.basiclibrary.location.config.LocationConfig
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationData
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationError
import com.yjsoft.roadtravel.basiclibrary.location.model.LocationState
import com.yjsoft.roadtravel.basiclibrary.location.service.LocationManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * 定位状态持有者
 * 管理定位状态的响应式数据流
 */
class LocationStateHolder(
    private val context: Context,
    private val locationManager: LocationManager
) {
    
    companion object {
        private const val TAG = "LocationStateHolder %s"
    }
    
    // 定位状态流
    val locationState: StateFlow<LocationState> = locationManager.locationState
    
    // 最新的定位数据流
    @OptIn(DelicateCoroutinesApi::class)
    val latestLocationData: StateFlow<LocationData?> = locationState
        .map { it.locationData }
        .stateIn(
            scope = GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    // 是否正在定位流
    @OptIn(DelicateCoroutinesApi::class)
    val isLocating: StateFlow<Boolean> = locationState
        .map { it.isLocating }
        .stateIn(
            scope = GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
    
    // 定位错误流
    @OptIn(DelicateCoroutinesApi::class)
    val locationError: StateFlow<LocationError?> = locationState
        .map { it.error }
        .stateIn(
            scope = GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    // 是否有有效定位数据流
    @OptIn(DelicateCoroutinesApi::class)
    val hasValidLocation: StateFlow<Boolean> = locationState
        .map { it.hasValidLocation }
        .stateIn(
            scope = GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )
    
    /**
     * 初始化定位服务
     */
    fun initialize(config: LocationConfig = LocationConfig.default()): Result<Unit> {
        LogManager.d(TAG, "初始化定位状态持有者")
        return locationManager.initialize(config)
    }
    
    /**
     * 开始定位
     */
    fun startLocation(config: LocationConfig? = null) {
        LogManager.d(TAG, "开始定位")
        locationManager.startLocation(config)
    }
    
    /**
     * 停止定位
     */
    fun stopLocation() {
        LogManager.d(TAG, "停止定位")
        locationManager.stopLocation()
    }
    
    /**
     * 单次定位
     */
    suspend fun getSingleLocation(config: LocationConfig = LocationConfig.singleHighAccuracy()): Result<LocationData> {
        LogManager.d(TAG, "执行单次定位")
        return locationManager.getSingleLocation(config)
    }
    
    /**
     * 重试定位
     */
    fun retryLocation() {
        LogManager.d(TAG, "重试定位")
        locationManager.retryLocation()
    }
    
    /**
     * 获取最后一次定位结果
     */
    fun getLastKnownLocation(): LocationData? {
        return locationManager.getLastKnownLocation()
    }
    
    /**
     * 设置定位配置
     */
    fun setLocationConfig(config: LocationConfig) {
        locationManager.setLocationConfig(config)
    }
    
    /**
     * 获取当前配置
     */
    fun getCurrentConfig(): LocationConfig {
        return locationManager.getCurrentConfig()
    }
    
    /**
     * 检查定位服务是否可用
     */
    fun isLocationServiceAvailable(): Boolean {
        return locationManager.isLocationServiceAvailable()
    }
    
    /**
     * 清除定位状态
     */
    fun clearLocationState() {
        locationManager.clearLocationState()
    }
    
    /**
     * 处理权限被拒绝
     */
    fun handlePermissionDenied(deniedPermissions: List<String>) {
        locationManager.handlePermissionDenied(deniedPermissions)
    }
    
    /**
     * 处理GPS未开启
     */
    fun handleGPSDisabled() {
        locationManager.handleGPSDisabled()
    }
    
    /**
     * 处理网络不可用
     */
    fun handleNetworkUnavailable() {
        locationManager.handleNetworkUnavailable()
    }
    
    /**
     * 监听定位状态变化
     */
    fun observeLocationState(
        lifecycleOwner: LifecycleOwner,
        onStateChanged: (LocationState) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            locationState.collect { state ->
                onStateChanged(state)
            }
        }
    }
    
    /**
     * 监听定位数据变化
     */
    fun observeLocationData(
        lifecycleOwner: LifecycleOwner,
        onLocationChanged: (LocationData?) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            latestLocationData.collect { locationData ->
                onLocationChanged(locationData)
            }
        }
    }
    
    /**
     * 监听定位错误
     */
    fun observeLocationError(
        lifecycleOwner: LifecycleOwner,
        onError: (LocationError?) -> Unit
    ) {
        lifecycleOwner.lifecycleScope.launch {
            locationError.collect { error ->
                onError(error)
            }
        }
    }
    
    /**
     * 创建定位数据的组合流
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun combineLocationData(): StateFlow<LocationDataCombined> {
        return combine(
            locationState,
            latestLocationData,
            isLocating,
            locationError,
            hasValidLocation
        ) { state, data, locating, error, hasValid ->
            LocationDataCombined(
                state = state,
                locationData = data,
                isLocating = locating,
                error = error,
                hasValidLocation = hasValid
            )
        }.stateIn(
            scope = GlobalScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = LocationDataCombined()
        )
    }
}

/**
 * 组合的定位数据
 */
data class LocationDataCombined(
    val state: LocationState = LocationState.initial(),
    val locationData: LocationData? = null,
    val isLocating: Boolean = false,
    val error: LocationError? = null,
    val hasValidLocation: Boolean = false
)
