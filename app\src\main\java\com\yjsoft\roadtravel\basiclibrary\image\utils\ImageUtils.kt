package com.yjsoft.roadtravel.basiclibrary.image.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.request.ImageRequest
import coil3.request.allowHardware
import coil3.request.crossfade
import coil3.size.Size
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogConfig
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import java.io.File
import java.io.FileOutputStream
import kotlin.math.min
import androidx.core.graphics.get
import androidx.core.graphics.scale

/**
 * 图片工具类
 * 提供图片处理、转换、优化等实用功能
 */
object ImageUtils {
    
    /**
     * 图片格式枚举
     */
    enum class ImageFormat(val mimeType: String, val extension: String) {
        JPEG("image/jpeg", ".jpg"),
        PNG("image/png", ".png"),
        WEBP("image/webp", ".webp")
    }
    
    /**
     * 图片质量枚举
     */
    enum class ImageQuality(val value: Int) {
        LOW(60),
        MEDIUM(80),
        HIGH(95),
        MAXIMUM(100)
    }
    
    /**
     * 创建标准的ImageRequest
     */
    fun createImageRequest(
        context: Context,
        data: Any,
        size: Size? = null,
        crossfadeEnabled: Boolean = true,
        crossfadeDuration: Int = 300,
        allowHardware: Boolean = true
    ): ImageRequest {
        return ImageRequest.Builder(context)
            .data(data)
            .apply {
                size?.let { size(it) }
                if (crossfadeEnabled) {
                    crossfade(crossfadeDuration)
                }
                allowHardware(allowHardware)
            }
            .build()
    }
    
    /**
     * 创建头像专用的ImageRequest
     */
    fun createAvatarImageRequest(
        context: Context,
        data: Any,
        size: Dp = 48.dp
    ): ImageRequest {
        val sizeValue = (size.value * context.resources.displayMetrics.density).toInt()
        return ImageRequest.Builder(context)
            .data(data)
            .size(sizeValue, sizeValue)
            .crossfade(200)
            .allowHardware(false) // 头像通常需要圆形裁剪，禁用硬件加速
            .build()
    }
    
    /**
     * 创建缩略图ImageRequest
     */
    fun createThumbnailImageRequest(
        context: Context,
        data: Any,
        maxSize: Dp = 120.dp
    ): ImageRequest {
        val maxSizeValue = (maxSize.value * context.resources.displayMetrics.density).toInt()
        return ImageRequest.Builder(context)
            .data(data)
            .size(maxSizeValue, maxSizeValue)
            .crossfade(100)
            .allowHardware(true)
            .build()
    }
    
    /**
     * 检查URL是否为有效的图片URL
     */
    fun isValidImageUrl(url: String?): Boolean {
        if (url.isNullOrBlank()) return false
        
        return try {
            val lowerUrl = url.lowercase()
            lowerUrl.startsWith("http://") || 
            lowerUrl.startsWith("https://") ||
            lowerUrl.startsWith("file://") ||
            lowerUrl.startsWith("content://") ||
            lowerUrl.startsWith("android.resource://") ||
            hasImageExtension(url)
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "检查图片URL有效性失败: $url")
            false
        }
    }
    
    /**
     * 检查URL是否有图片扩展名
     */
    fun hasImageExtension(url: String): Boolean {
        val imageExtensions = listOf(".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg")
        val lowerUrl = url.lowercase()
        return imageExtensions.any { lowerUrl.contains(it) }
    }
    
    /**
     * 从URI获取文件大小
     */
    fun getImageSize(context: Context, uri: Uri): Pair<Int, Int>? {
        return try {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeStream(inputStream, null, options)
                Pair(options.outWidth, options.outHeight)
            }
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "获取图片尺寸失败: $uri")
            null
        }
    }
    
    /**
     * 计算合适的采样率以减少内存使用
     */
    fun calculateInSampleSize(
        originalWidth: Int,
        originalHeight: Int,
        targetWidth: Int,
        targetHeight: Int
    ): Int {
        var inSampleSize = 1
        
        if (originalHeight > targetHeight || originalWidth > targetWidth) {
            val halfHeight = originalHeight / 2
            val halfWidth = originalWidth / 2
            
            while ((halfHeight / inSampleSize) >= targetHeight && 
                   (halfWidth / inSampleSize) >= targetWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
    
    /**
     * 压缩图片到指定大小
     */
    fun compressImage(
        context: Context,
        sourceUri: Uri,
        targetFile: File,
        maxWidth: Int = 1080,
        maxHeight: Int = 1080,
        quality: ImageQuality = ImageQuality.MEDIUM,
        format: ImageFormat = ImageFormat.JPEG
    ): Boolean {
        return try {
            context.contentResolver.openInputStream(sourceUri)?.use { inputStream ->
                // 获取原始尺寸
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeStream(inputStream, null, options)
                
                // 计算采样率
                val inSampleSize = calculateInSampleSize(
                    options.outWidth, options.outHeight,
                    maxWidth, maxHeight
                )
                
                // 重新打开流进行实际解码
                context.contentResolver.openInputStream(sourceUri)?.use { decodeStream ->
                    val decodeOptions = BitmapFactory.Options().apply {
                        inPreferredConfig = Bitmap.Config.RGB_565
                    }
                    
                    val bitmap = BitmapFactory.decodeStream(decodeStream, null, decodeOptions)
                    bitmap?.let { bmp ->
                        // 进一步缩放到目标尺寸
                        val scaledBitmap = if (bmp.width > maxWidth || bmp.height > maxHeight) {
                            val scale = min(
                                maxWidth.toFloat() / bmp.width,
                                maxHeight.toFloat() / bmp.height
                            )
                            val newWidth = (bmp.width * scale).toInt()
                            val newHeight = (bmp.height * scale).toInt()
                            bmp.scale(newWidth, newHeight)
                        } else {
                            bmp
                        }
                        
                        // 保存压缩后的图片
                        FileOutputStream(targetFile).use { outputStream ->
                            val compressFormat = when (format) {
                                ImageFormat.JPEG -> Bitmap.CompressFormat.JPEG
                                ImageFormat.PNG -> Bitmap.CompressFormat.PNG
                                ImageFormat.WEBP -> Bitmap.CompressFormat.WEBP
                            }
                            scaledBitmap.compress(compressFormat, quality.value, outputStream)
                        }
                        
                        if (scaledBitmap != bmp) {
                            scaledBitmap.recycle()
                        }
                        bmp.recycle()
                        
                        LogManager.tag(LogConfig.Tags.IMAGE).d(
                            "图片压缩完成: ${sourceUri} -> ${targetFile.absolutePath}"
                        )
                        true
                    } ?: false
                }
            } ?: false
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "图片压缩失败: $sourceUri")
            false
        }
    }
    
    /**
     * 获取图片的主要颜色
     */
    fun getDominantColor(bitmap: Bitmap): Color {
        return try {
            val width = bitmap.width
            val height = bitmap.height
            val pixelCount = width * height
            
            var redSum = 0L
            var greenSum = 0L
            var blueSum = 0L
            
            // 采样像素点计算平均颜色
            val sampleSize = if (pixelCount > 10000) 10 else 1
            
            for (x in 0 until width step sampleSize) {
                for (y in 0 until height step sampleSize) {
                    val pixel = bitmap[x, y]
                    redSum += android.graphics.Color.red(pixel)
                    greenSum += android.graphics.Color.green(pixel)
                    blueSum += android.graphics.Color.blue(pixel)
                }
            }
            
            val sampledPixelCount = (width / sampleSize) * (height / sampleSize)
            val avgRed = (redSum / sampledPixelCount).toInt()
            val avgGreen = (greenSum / sampledPixelCount).toInt()
            val avgBlue = (blueSum / sampledPixelCount).toInt()
            
            Color(avgRed, avgGreen, avgBlue)
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "获取图片主要颜色失败")
            Color.Gray
        }
    }
    
    /**
     * 清理图片缓存目录
     */
    fun cleanImageCache(context: Context): Boolean {
        return try {
            val imageManager = ImageManager.getImageLoaderInstance(context)
            imageManager.clearCache()
            LogManager.tag(LogConfig.Tags.IMAGE).i("图片缓存清理完成")
            true
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).e(e, "清理图片缓存失败")
            false
        }
    }
    
    /**
     * 获取图片缓存大小
     */
    fun getImageCacheSize(context: Context): Long {
        return try {
            val imageManager = ImageManager.getImageLoaderInstance(context)
            val stats = imageManager.getCacheStats()
            stats.memoryCacheSize + stats.diskCacheSize
        } catch (e: Exception) {
            LogManager.tag(LogConfig.Tags.IMAGE).w(e, "获取图片缓存大小失败")
            0L
        }
    }
    
    /**
     * 格式化文件大小显示
     */
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }
}
