package com.yjsoft.roadtravel.basiclibrary.payment.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentCallback
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.state.PaymentViewModel
import java.math.BigDecimal
import java.util.Locale

/**
 * 支付按钮组件
 * 提供统一的支付按钮UI和交互
 */
@Composable
fun PaymentButton(
    amount: Double,
    paymentTypes: List<PaymentType> = PaymentType.values().toList(),
    orderId: String? = null,
    title: String = "商品购买",
    description: String? = null,
    onPaymentResult: (PaymentResult) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    buttonText: String? = null,
    viewModel: PaymentViewModel? = null
) {
    val context = LocalContext.current
    var showPaymentDialog by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    
    // 支付回调
    val paymentCallback = remember {
        object : PaymentCallback {
            override fun onPaymentStart(orderId: String, paymentType: PaymentType) {
                isLoading = true
            }
            
            override fun onPaymentSuccess(result: PaymentResult.Success) {
                isLoading = false
                onPaymentResult(result)
            }
            
            override fun onPaymentCancel(result: PaymentResult.Cancel) {
                isLoading = false
                onPaymentResult(result)
            }
            
            override fun onPaymentError(result: PaymentResult.Error) {
                isLoading = false
                onPaymentResult(result)
            }
            
            override fun onPaymentProcessing(result: PaymentResult.Processing) {
                // 保持加载状态
                onPaymentResult(result)
            }
        }
    }
    
    // 支付按钮
    Button(
        onClick = {
            if (paymentTypes.size == 1) {
                // 只有一种支付方式，直接支付
                startPayment(
                    context = context,
                    paymentType = paymentTypes.first(),
                    amount = amount,
                    orderId = orderId,
                    title = title,
                    description = description,
                    callback = paymentCallback,
                    viewModel = viewModel
                )
            } else {
                // 多种支付方式，显示选择对话框
                showPaymentDialog = true
            }
        },
        enabled = enabled && !isLoading,
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp),
        shape = RoundedCornerShape(12.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.primary,
            contentColor = MaterialTheme.colorScheme.onPrimary
        )
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = MaterialTheme.colorScheme.onPrimary,
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("支付中...")
        } else {
            Text(
                text = buttonText ?: "立即支付 ¥${String.format(Locale.getDefault(), "%.2f", amount)}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
    
    // 支付方式选择对话框
    if (showPaymentDialog) {
        PaymentDialog(
            amount = amount,
            paymentTypes = paymentTypes,
            onPaymentSelected = { paymentType ->
                showPaymentDialog = false
                startPayment(
                    context = context,
                    paymentType = paymentType,
                    amount = amount,
                    orderId = orderId,
                    title = title,
                    description = description,
                    callback = paymentCallback,
                    viewModel = viewModel
                )
            },
            onDismiss = {
                showPaymentDialog = false
            }
        )
    }
}

/**
 * 单一支付方式按钮
 */
@Composable
fun SinglePaymentButton(
    paymentType: PaymentType,
    amount: Double,
    orderId: String? = null,
    title: String = "商品购买",
    description: String? = null,
    onPaymentResult: (PaymentResult) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    viewModel: PaymentViewModel? = null
) {
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(false) }
    
    // 支付回调
    val paymentCallback = remember {
        object : PaymentCallback {
            override fun onPaymentStart(orderId: String, paymentType: PaymentType) {
                isLoading = true
            }
            
            override fun onPaymentSuccess(result: PaymentResult.Success) {
                isLoading = false
                onPaymentResult(result)
            }
            
            override fun onPaymentCancel(result: PaymentResult.Cancel) {
                isLoading = false
                onPaymentResult(result)
            }
            
            override fun onPaymentError(result: PaymentResult.Error) {
                isLoading = false
                onPaymentResult(result)
            }
        }
    }
    
    Button(
        onClick = {
            startPayment(
                context = context,
                paymentType = paymentType,
                amount = amount,
                orderId = orderId,
                title = title,
                description = description,
                callback = paymentCallback,
                viewModel = viewModel
            )
        },
        enabled = enabled && !isLoading,
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
        shape = RoundedCornerShape(8.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = getPaymentTypeColor(paymentType),
            contentColor = Color.White
        )
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                color = Color.White,
                strokeWidth = 2.dp
            )
            Spacer(modifier = Modifier.width(8.dp))
        } else {
            Icon(
                painter = painterResource(id = paymentType.iconRes),
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        
        Text(
            text = if (isLoading) "支付中..." else stringResource(id = paymentType.displayNameRes),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 获取支付方式对应的颜色
 */
private fun getPaymentTypeColor(paymentType: PaymentType): Color {
    return when (paymentType) {
        PaymentType.ALIPAY -> Color(0xFF1677FF)
        PaymentType.WECHAT_PAY -> Color(0xFF07C160)
        PaymentType.UNION_PAY -> Color(0xFFE21836)
    }
}

/**
 * 开始支付的辅助函数
 */
private fun startPayment(
    context: android.content.Context,
    paymentType: PaymentType,
    amount: Double,
    orderId: String?,
    title: String,
    description: String?,
    callback: PaymentCallback,
    viewModel: PaymentViewModel?
) {
    val request = PaymentRequest(
        orderId = orderId ?: "ORDER_${System.currentTimeMillis()}",
        amount = BigDecimal.valueOf(amount),
        title = title,
        description = description
    )
    
    viewModel?.startPayment(context, paymentType, request, callback)
}

@Preview(showBackground = true)
@Composable
private fun PaymentButtonPreview() {
    MaterialTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            PaymentButton(
                amount = 99.99,
                onPaymentResult = {}
            )
            
            SinglePaymentButton(
                paymentType = PaymentType.ALIPAY,
                amount = 99.99,
                onPaymentResult = {}
            )
            
            SinglePaymentButton(
                paymentType = PaymentType.WECHAT_PAY,
                amount = 99.99,
                onPaymentResult = {}
            )
        }
    }
}
