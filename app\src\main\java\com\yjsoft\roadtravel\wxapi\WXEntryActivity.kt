package com.yjsoft.roadtravel.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatConfig
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatLoginService
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatLoginStateManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * 微信回调Activity
 * 
 * 注意：
 * 1. 这个Activity必须放在包名.wxapi包下
 * 2. 类名必须是WXEntryActivity
 * 3. 需要在AndroidManifest.xml中注册，并设置为exported="true"
 */
class WXEntryActivity : Activity(), IWXAPIEventHandler {
    
    companion object {
        private const val TAG = "WXEntryActivity %s"
    }
    
    // 使用单例，不再依赖注入
    private val weChatLoginService: WeChatLoginService by lazy {
        WeChatLoginService.getInstance()
    }

    private var wxApi: IWXAPI? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            // 初始化微信API
            wxApi = WXAPIFactory.createWXAPI(this, WeChatConfig.APP_ID, false)
            
            // 处理微信回调
            val handled = wxApi?.handleIntent(intent, this) ?: false
            
            if (!handled) {
                LogManager.w(TAG, "微信回调处理失败")
                // 通知登录失败
                safeHandleLoginCallback(
                    code = null,
                    state = null,
                    errCode = -998 // 自定义错误码表示回调处理失败
                )
                finish()
            }

        } catch (e: Exception) {
            LogManager.e(TAG, "处理微信回调异常", e)
            // 通知登录异常
            safeHandleLoginCallback(
                code = null,
                state = null,
                errCode = -997 // 自定义错误码表示回调异常
            )
            finish()
        }
    }
    
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        
        try {
            val handled = wxApi?.handleIntent(intent, this) ?: false
            if (!handled) {
                LogManager.w(TAG, "处理新Intent失败")
                // 通知登录失败
                safeHandleLoginCallback(
                    code = null,
                    state = null,
                    errCode = -996 // 自定义错误码表示新Intent处理失败
                )
                finish()
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "处理新Intent异常", e)
            // 通知登录异常
            safeHandleLoginCallback(
                code = null,
                state = null,
                errCode = -995 // 自定义错误码表示新Intent异常
            )
            finish()
        }
    }
    
    /**
     * 微信发送请求到第三方应用时，会回调到该方法
     */
    override fun onReq(req: BaseReq?) {
        LogManager.d(TAG, "收到微信请求: ${req?.type}")
        // 一般情况下不需要处理
        finish()
    }
    
    /**
     * 第三方应用发送到微信的请求处理后的响应结果，会回调到该方法
     */
    override fun onResp(resp: BaseResp?) {
        LogManager.d(TAG, "收到微信响应: type=${resp?.type}, errCode=${resp?.errCode}")
        
        when (resp?.type) {
            ConstantsAPI.COMMAND_SENDAUTH -> {
                // 处理登录授权回调
                handleAuthResponse(resp as? SendAuth.Resp)
            }
            else -> {
                LogManager.w(TAG, "未知的响应类型: ${resp?.type}")
                // 对于未知响应类型，也通知登录失败
                safeHandleLoginCallback(
                    code = null,
                    state = null,
                    errCode = -994 // 自定义错误码表示未知响应类型
                )
            }
        }

        finish()
    }
    
    /**
     * 处理登录授权响应
     */
    private fun handleAuthResponse(resp: SendAuth.Resp?) {
        if (resp == null) {
            LogManager.w(TAG, "登录响应为空")
            // 通知登录失败
            safeHandleLoginCallback(
                code = null,
                state = null,
                errCode = -1 // 自定义错误码表示响应为空
            )
            return
        }

        LogManager.d(TAG, "处理登录响应: errCode=${resp.errCode}, code=${resp.code}, state=${resp.state}")

        try {
            // 将结果传递给WeChatLoginService处理
            safeHandleLoginCallback(
                code = resp.code,
                state = resp.state,
                errCode = resp.errCode
            )

        } catch (e: Exception) {
            LogManager.e(TAG, "处理登录响应异常", e)
            // 通知登录异常
            safeHandleLoginCallback(
                code = null,
                state = null,
                errCode = -999 // 自定义错误码表示处理异常
            )
        }
    }

    /**
     * 安全地处理登录回调
     */
    private fun safeHandleLoginCallback(code: String?, state: String?, errCode: Int) {
        try {
            // 直接调用服务处理回调，因为现在使用单例模式
            weChatLoginService.handleLoginCallback(code, state, errCode)
        } catch (e: Exception) {
            LogManager.e(TAG, "处理登录回调时发生异常", e)
            // 通过单例状态管理器发送登录失败事件
            try {
                WeChatLoginStateManager.getInstance().sendLoginFailedEvent("微信登录处理异常: ${e.message}")
            } catch (ex: Exception) {
                LogManager.e(TAG, "发送登录失败事件失败", ex)
            }
        }
    }
}
