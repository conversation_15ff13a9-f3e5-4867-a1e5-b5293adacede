package com.yjsoft.roadtravel.basiclibrary.di.qualifiers

import javax.inject.Qualifier

/**
 * 自定义依赖注入限定符
 * 
 * 功能：
 * - 区分相同类型的不同实现
 * - 提供类型安全的依赖标识
 * - 支持多种配置和环境
 * 
 * 使用方式：
 * ```kotlin
 * @Module
 * object ExampleModule {
 *     @Provides
 *     @DebugRetrofit
 *     fun provideDebugRetrofit(): Retrofit = ...
 *     
 *     @Provides
 *     @ReleaseRetrofit
 *     fun provideReleaseRetrofit(): Retrofit = ...
 * }
 * 
 * @Inject
 * @DebugRetrofit
 * lateinit var debugRetrofit: Retrofit
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * 应用上下文限定符
 * 用于区分Application Context和其他Context
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ApplicationContext

/**
 * Activity上下文限定符
 * 用于区分Activity Context
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ActivityContext

/**
 * 调试环境限定符
 * 用于标识调试环境下的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DebugEnvironment

/**
 * 生产环境限定符
 * 用于标识生产环境下的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ProductionEnvironment

/**
 * 测试环境限定符
 * 用于标识测试环境下的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class TestEnvironment

/**
 * 调试Retrofit限定符
 * 用于调试环境的Retrofit实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DebugRetrofit

/**
 * 生产Retrofit限定符
 * 用于生产环境的Retrofit实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ReleaseRetrofit

/**
 * 缓存OkHttpClient限定符
 * 用于带缓存的OkHttpClient实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CachedOkHttpClient

/**
 * 无缓存OkHttpClient限定符
 * 用于不带缓存的OkHttpClient实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class NonCachedOkHttpClient

/**
 * 主数据库限定符
 * 用于主数据库实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MainDatabase

/**
 * 缓存数据库限定符
 * 用于缓存数据库实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CacheDatabase

/**
 * 用户DataStore限定符
 * 用于用户相关的DataStore实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class UserDataStore

/**
 * 设置DataStore限定符
 * 用于应用设置的DataStore实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class SettingsDataStore

/**
 * 缓存DataStore限定符
 * 用于缓存数据的DataStore实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class CacheDataStore

/**
 * 主线程调度器限定符
 * 用于主线程的协程调度器
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MainDispatcher

/**
 * IO线程调度器限定符
 * 用于IO操作的协程调度器
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class IoDispatcher

/**
 * 默认线程调度器限定符
 * 用于默认的协程调度器
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DefaultDispatcher

/**
 * 无限制线程调度器限定符
 * 用于无限制的协程调度器
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class UnconfinedDispatcher

/**
 * 内存缓存限定符
 * 用于内存缓存实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MemoryCache

/**
 * 磁盘缓存限定符
 * 用于磁盘缓存实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class DiskCache

/**
 * 网络缓存限定符
 * 用于网络缓存实例
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class NetworkCache

/**
 * 支付宝支付限定符
 * 用于支付宝支付相关的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class AlipayPayment

/**
 * 微信支付限定符
 * 用于微信支付相关的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class WeChatPayment

/**
 * 云闪付限定符
 * 用于云闪付相关的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class UnionPayment

/**
 * 高德地图限定符
 * 用于高德地图相关的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class AMapLocation

/**
 * GPS定位限定符
 * 用于GPS定位相关的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class GpsLocation

/**
 * 网络定位限定符
 * 用于网络定位相关的依赖
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class NetworkLocation
