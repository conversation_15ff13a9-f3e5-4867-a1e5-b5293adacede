package com.yjsoft.roadtravel.basiclibrary.mvvm.state

/**
 * 通用UI状态基类
 * 
 * 功能：
 * - 统一管理UI的加载、成功、失败状态
 * - 提供通用的状态转换方法
 * - 支持泛型数据类型
 * 
 * 设计原则：
 * - 状态互斥：同一时间只能处于一种状态
 * - 类型安全：使用密封类确保状态完整性
 * - 扩展性：支持自定义状态和数据类型
 * 
 * 使用方式：
 * ```kotlin
 * // 在ViewModel中
 * private val _uiState = MutableStateFlow<UiState<List<User>>>(UiState.Idle)
 * val uiState: StateFlow<UiState<List<User>>> = _uiState.asStateFlow()
 * 
 * // 在Composable中
 * val state by viewModel.uiState.collectAsState()
 * when (state) {
 *     is UiState.Idle -> { /* 初始状态 */ }
 *     is UiState.Loading -> { /* 显示加载 */ }
 *     is UiState.Success -> { /* 显示数据 */ }
 *     is UiState.Error -> { /* 显示错误 */ }
 * }
 * ```
 * 
 * @param T 数据类型
 * <AUTHOR> Team
 * @since 1.0.0
 */
sealed class UiState<out T> {
    
    /**
     * 空闲状态 - 初始状态，未开始任何操作
     */
    object Idle : UiState<Nothing>()
    
    /**
     * 加载状态 - 正在执行操作
     * @param message 加载提示信息
     */
    data class Loading(val message: String = "加载中...") : UiState<Nothing>()
    
    /**
     * 成功状态 - 操作成功完成
     * @param data 成功返回的数据
     * @param message 成功提示信息
     */
    data class Success<T>(
        val data: T,
        val message: String = ""
    ) : UiState<T>()
    
    /**
     * 错误状态 - 操作失败
     * @param exception 异常信息
     * @param message 错误提示信息
     * @param code 错误代码
     */
    data class Error(
        val exception: Throwable? = null,
        val message: String = "操作失败",
        val code: Int = -1
    ) : UiState<Nothing>()
    
    /**
     * 空数据状态 - 操作成功但无数据
     * @param message 空数据提示信息
     */
    data class Empty(val message: String = "暂无数据") : UiState<Nothing>()
    
    // ========== 便捷方法 ==========
    
    /**
     * 是否为加载状态
     */
    val isLoading: Boolean
        get() = this is Loading
    
    /**
     * 是否为成功状态
     */
    val isSuccess: Boolean
        get() = this is Success
    
    /**
     * 是否为错误状态
     */
    val isError: Boolean
        get() = this is Error
    
    /**
     * 是否为空闲状态
     */
    val isIdle: Boolean
        get() = this is Idle
    
    /**
     * 是否为空数据状态
     */
    val isEmpty: Boolean
        get() = this is Empty
    
    /**
     * 获取数据（如果是成功状态）
     */
    fun getDataOrNull(): T? {
        return if (this is Success) data else null
    }
    
    /**
     * 获取错误信息
     */
    fun getErrorMessage(): String {
        return when (this) {
            is Error -> message
            else -> ""
        }
    }
    
    /**
     * 获取显示消息
     */
    fun getDisplayMessage(): String {
        return when (this) {
            is Loading -> message
            is Success -> message
            is Error -> message
            is Empty -> message
            is Idle -> ""
        }
    }
    
    companion object {
        /**
         * 创建加载状态
         */
        fun loading(message: String = "加载中..."): UiState<Nothing> = Loading(message)
        
        /**
         * 创建成功状态
         */
        fun <T> success(data: T, message: String = ""): UiState<T> = Success(data, message)
        
        /**
         * 创建错误状态
         */
        fun error(
            exception: Throwable? = null,
            message: String = "操作失败",
            code: Int = -1
        ): UiState<Nothing> = Error(exception, message, code)
        
        /**
         * 创建空数据状态
         */
        fun empty(message: String = "暂无数据"): UiState<Nothing> = Empty(message)
        
        /**
         * 创建空闲状态
         */
        fun idle(): UiState<Nothing> = Idle
    }
}

/**
 * UiState扩展函数
 */

/**
 * 映射数据类型
 */
inline fun <T, R> UiState<T>.map(transform: (T) -> R): UiState<R> {
    return when (this) {
        is UiState.Success -> UiState.Success(transform(data), message)
        is UiState.Loading -> UiState.Loading(message)
        is UiState.Error -> UiState.Error(exception, message, code)
        is UiState.Empty -> UiState.Empty(message)
        is UiState.Idle -> UiState.Idle
    }
}

/**
 * 当成功时执行操作
 */
inline fun <T> UiState<T>.onSuccess(action: (T) -> Unit): UiState<T> {
    if (this is UiState.Success) {
        action(data)
    }
    return this
}

/**
 * 当错误时执行操作
 */
inline fun <T> UiState<T>.onError(action: (Throwable?, String, Int) -> Unit): UiState<T> {
    if (this is UiState.Error) {
        action(exception, message, code)
    }
    return this
}

/**
 * 当加载时执行操作
 */
inline fun <T> UiState<T>.onLoading(action: (String) -> Unit): UiState<T> {
    if (this is UiState.Loading) {
        action(message)
    }
    return this
}
