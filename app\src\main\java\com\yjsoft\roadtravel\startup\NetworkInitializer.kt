package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.auth.TokenManager
import com.yjsoft.roadtravel.basiclibrary.auth.WeChatTokenProvider
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.network.RetrofitInstance

/**
 * 网络框架初始化器
 * 使用androidx.startup进行网络框架的初始化
 * 
 * 优先级：中（依赖LogManager和DataStore）
 * 依赖：LogManager, DataStore
 */
class NetworkInitializer : Initializer<NetworkManager> {

    companion object {
        private const val TAG = "NetworkInitializer"
    }

    /**
     * 创建并初始化网络框架（轻量级版本）
     * @param context 应用上下文
     * @return NetworkManager实例
     */
    override fun create(context: Context): NetworkManager {
        try {
            LogManager.w(TAG, "NetworkInitializer已弃用，请使用NetworkCoreInitializer")

            // 委托给NetworkCoreInitializer
            val coreInitializer = NetworkCoreInitializer()
            return coreInitializer.create(context)

        } catch (e: Exception) {
            LogManager.e(TAG, "网络框架初始化失败", e)
            throw e
        }
    }

    /**
     * 返回此初始化器的依赖项
     * 网络框架依赖LogManager和DataStore
     * @return 依赖LogManagerInitializer和DataStoreInitializer
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return listOf(
            LogManagerInitializer::class.java,
            DataStoreInitializer::class.java
        )
    }
}
