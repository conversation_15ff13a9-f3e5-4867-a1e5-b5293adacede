package com.yjsoft.roadtravel.basiclibrary.mvvm.state

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 单次事件封装类
 * 
 * 功能：
 * - 确保事件只被消费一次
 * - 防止配置变更时重复触发
 * - 支持Compose和传统View系统
 * 
 * 设计原则：
 * - 单次消费：每个事件只能被处理一次
 * - 线程安全：支持多线程环境
 * - 生命周期感知：自动处理生命周期
 * 
 * 使用方式：
 * ```kotlin
 * // 在ViewModel中
 * private val _navigationEvent = MutableSharedFlow<NavigationEvent>()
 * val navigationEvent: SharedFlow<NavigationEvent> = _navigationEvent.asSharedFlow()
 * 
 * fun navigateToDetail() {
 *     _navigationEvent.tryEmit(NavigationEvent.ToDetail("123"))
 * }
 * 
 * // 在Composable中
 * LaunchedEffect(Unit) {
 *     viewModel.navigationEvent.collect { event ->
 *         when (event) {
 *             is NavigationEvent.ToDetail -> navController.navigate("detail/${event.id}")
 *         }
 *     }
 * }
 * ```
 * 
 * @param T 事件数据类型
 * <AUTHOR> Team
 * @since 1.0.0
 */
open class Event<out T>(private val content: T) {
    
    private var hasBeenHandled = false
    
    /**
     * 获取内容并标记为已处理
     * @return 如果未被处理过则返回内容，否则返回null
     */
    fun getContentIfNotHandled(): T? {
        return if (hasBeenHandled) {
            null
        } else {
            hasBeenHandled = true
            content
        }
    }
    
    /**
     * 获取内容（无论是否已被处理）
     */
    fun peekContent(): T = content
    
    /**
     * 是否已被处理
     */
    fun hasBeenHandled(): Boolean = hasBeenHandled
    
    override fun toString(): String {
        return "Event(content=$content, hasBeenHandled=$hasBeenHandled)"
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is Event<*>) return false
        return content == other.content
    }
    
    override fun hashCode(): Int {
        return content?.hashCode() ?: 0
    }
}

/**
 * 事件处理器
 * 用于管理事件的发送和接收
 */
class EventHandler<T> {
    
    private val _events = MutableSharedFlow<Event<T>>(
        extraBufferCapacity = 1,
        replay = 0
    )
    
    /**
     * 事件流
     */
    val events: SharedFlow<Event<T>> = _events.asSharedFlow()
    
    /**
     * 发送事件
     */
    fun sendEvent(event: T) {
        _events.tryEmit(Event(event))
    }
    
    /**
     * 发送事件（挂起函数）
     */
    suspend fun emitEvent(event: T) {
        _events.emit(Event(event))
    }
}

/**
 * 简化的事件管理器
 * 直接使用SharedFlow管理事件，适用于不需要防重复处理的场景
 */
class SimpleEventHandler<T> {
    
    private val _events = MutableSharedFlow<T>(
        extraBufferCapacity = 1,
        replay = 0
    )
    
    /**
     * 事件流
     */
    val events: SharedFlow<T> = _events.asSharedFlow()
    
    /**
     * 发送事件
     */
    fun sendEvent(event: T) {
        _events.tryEmit(event)
    }
    
    /**
     * 发送事件（挂起函数）
     */
    suspend fun emitEvent(event: T) {
        _events.emit(event)
    }
}

/**
 * 常用事件类型定义
 */

/**
 * 导航事件
 */
sealed class NavigationEvent {
    object Back : NavigationEvent()
    data class ToRoute(val route: String) : NavigationEvent()
    data class ToActivity(val className: String, val params: Map<String, Any> = emptyMap()) : NavigationEvent()
}

/**
 * UI事件
 */
sealed class UiEvent {
    data class ShowToast(val message: String) : UiEvent()
    data class ShowSnackbar(val message: String, val actionLabel: String? = null) : UiEvent()
    data class ShowDialog(val title: String, val message: String) : UiEvent()
    object HideKeyboard : UiEvent()
    object ShowKeyboard : UiEvent()
}

/**
 * 网络事件
 */
sealed class NetworkEvent {
    object Connected : NetworkEvent()
    object Disconnected : NetworkEvent()
    data class Error(val message: String) : NetworkEvent()
}

/**
 * Compose扩展函数
 */

/**
 * 在Compose中处理事件
 */
@Composable
fun <T> HandleEvents(
    eventFlow: Flow<Event<T>>,
    onEvent: (T) -> Unit
) {
    LaunchedEffect(eventFlow) {
        eventFlow.collect { event ->
            event.getContentIfNotHandled()?.let { content ->
                onEvent(content)
            }
        }
    }
}

/**
 * 在Compose中处理简单事件
 */
@Composable
fun <T> HandleSimpleEvents(
    eventFlow: Flow<T>,
    onEvent: (T) -> Unit
) {
    LaunchedEffect(eventFlow) {
        eventFlow.collect { event ->
            onEvent(event)
        }
    }
}

/**
 * 收集事件状态
 */
@Composable
fun <T> Flow<Event<T>>.collectAsEventState(): Event<T>? {
    return collectAsStateWithLifecycle(initialValue = null).value
}

/**
 * 处理收集到的事件
 */
@Composable
fun <T> Event<T>?.HandleEvent(onEvent: (T) -> Unit) {
    this?.getContentIfNotHandled()?.let { content ->
        LaunchedEffect(content) {
            onEvent(content)
        }
    }
}

/**
 * 事件工具函数
 */
object EventUtils {
    
    /**
     * 创建事件处理器
     */
    fun <T> createEventHandler(): EventHandler<T> = EventHandler()
    
    /**
     * 创建简单事件处理器
     */
    fun <T> createSimpleEventHandler(): SimpleEventHandler<T> = SimpleEventHandler()
    
    /**
     * 批量发送事件
     */
    fun <T> EventHandler<T>.sendEvents(events: List<T>) {
        events.forEach { sendEvent(it) }
    }
    
    /**
     * 条件发送事件
     */
    fun <T> EventHandler<T>.sendEventIf(condition: Boolean, event: T) {
        if (condition) {
            sendEvent(event)
        }
    }
}
