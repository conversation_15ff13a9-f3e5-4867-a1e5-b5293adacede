package com.yjsoft.roadtravel.basiclibrary.di.examples

import androidx.activity.ComponentActivity
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.di.adapters.ManagerAdapter
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Hilt依赖注入使用示例
 * 
 * 本文件包含了各种Hilt依赖注入的使用示例，包括：
 * - Activity中的依赖注入
 * - ViewModel中的依赖注入
 * - Composable中的依赖注入
 * - Repository中的依赖注入
 * - 自定义限定符的使用
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * 示例1：Activity中使用依赖注入
 */
// @AndroidEntryPoint // 在实际的Activity类上添加此注解
class ExampleActivity : ComponentActivity() {
    
    // 方式1：直接注入ManagerAdapter
    @Inject
    lateinit var managerAdapter: ManagerAdapter
    
    // 方式2：直接注入具体的Manager
    @Inject
    lateinit var logManager: LogManager
    
    @Inject
    lateinit var networkManager: NetworkManager
    
    fun exampleUsage() {
        // 使用注入的依赖
        logManager.d("ExampleActivity", "Activity中的依赖注入示例")
        
        val networkStatus = networkManager.getStatus()
        logManager.d("ExampleActivity", "网络状态: ${networkStatus.currentEnvironment?.name}")
        
        // 通过ManagerAdapter获取其他Manager
        val dataStoreManager = managerAdapter.getDataStoreManager()
        val imageManager = managerAdapter.getImageManager()
    }
}

/**
 * 示例2：ViewModel中使用依赖注入
 */
@HiltViewModel
class ExampleViewModel @Inject constructor(
    private val repository: DIExampleRepository,
    private val logManager: LogManager,
    private val networkManager: NetworkManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ExampleUiState())
    val uiState: StateFlow<ExampleUiState> = _uiState.asStateFlow()
    
    fun loadData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                logManager.d("ExampleViewModel", "开始加载数据")
                
                // 使用注入的Repository
                val data = repository.getExampleData()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    data = data,
                    isSuccess = true
                )
                
                logManager.d("ExampleViewModel", "数据加载成功: $data")
                
            } catch (e: Exception) {
                logManager.e("ExampleViewModel", "数据加载失败", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message,
                    isError = true
                )
            }
        }
    }
    
    fun refreshData() {
        loadData()
    }
}

/**
 * 示例UI状态
 */
data class ExampleUiState(
    val isLoading: Boolean = false,
    val isSuccess: Boolean = false,
    val isError: Boolean = false,
    val data: String = "",
    val error: String? = null
)

/**
 * 示例3：Composable中使用依赖注入
 */
@Composable
fun DIExampleScreen() {
    // 使用hiltViewModel()获取注入了依赖的ViewModel
    val viewModel: ExampleViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Hilt依赖注入示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        when {
            uiState.isLoading -> {
                CircularProgressIndicator()
                Text("正在加载数据...")
            }
            uiState.isSuccess -> {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "加载成功！",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Text(
                            text = uiState.data,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
            uiState.isError -> {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "加载失败",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Text(
                            text = uiState.error ?: "未知错误",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = { viewModel.loadData() }
            ) {
                Text("加载数据")
            }
            
            Button(
                onClick = { viewModel.refreshData() }
            ) {
                Text("刷新数据")
            }
        }
    }
}

/**
 * 示例4：在Composable中直接使用ManagerAdapter
 */
@Composable
fun ManagerAdapterExampleScreen() {
    val context = LocalContext.current
    
    // 注意：这种方式需要Activity添加@AndroidEntryPoint注解
    // 并且Activity中注入了ManagerAdapter
    val activity = context as? ComponentActivity
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "ManagerAdapter使用示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 这里展示如何在需要时获取Manager
        // 实际使用中建议通过ViewModel注入
        Button(
            onClick = {
                // 示例：如何在Composable中使用Manager
                // 注意：这不是推荐的做法，建议通过ViewModel
            }
        ) {
            Text("示例按钮")
        }
        
        Text(
            text = "推荐做法：通过ViewModel注入依赖，而不是在Composable中直接使用",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 示例5：依赖注入状态展示
 */
@Composable
fun DIStatusScreen() {
    val viewModel: DIExampleViewModel = hiltViewModel()
    val state by viewModel.exampleState.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.initializeExample()
    }
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "依赖注入状态",
                style = MaterialTheme.typography.headlineMedium
            )
        }
        
        item {
            if (state.isLoading) {
                LinearProgressIndicator(
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
        
        val statusItems = listOf(
            "LogManager" to state.logManagerStatus,
            "NetworkManager" to state.networkManagerStatus,
            "DataStoreManager" to state.dataStoreManagerStatus,
            "PaymentManager" to state.paymentManagerStatus,
            "Repository" to state.repositoryStatus
        )
        
        items(statusItems) { (name, status) ->
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Text(
                        text = name,
                        style = MaterialTheme.typography.titleSmall
                    )
                    Text(
                        text = status.ifEmpty { "未测试" },
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
        
        item {
            if (state.message.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = if (state.isSuccess) {
                            MaterialTheme.colorScheme.primaryContainer
                        } else if (state.isError) {
                            MaterialTheme.colorScheme.errorContainer
                        } else {
                            MaterialTheme.colorScheme.surfaceVariant
                        }
                    )
                ) {
                    Text(
                        text = state.message,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }
        
        item {
            Button(
                onClick = { viewModel.resetExample() },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("重置示例")
            }
        }
    }
}
