package com.yjsoft.roadtravel.basiclibrary.datastore.examples

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.yjsoft.roadtravel.basiclibrary.datastore.compose.rememberDataStoreValue
import com.yjsoft.roadtravel.basiclibrary.datastore.compose.rememberFavoriteCities
import com.yjsoft.roadtravel.basiclibrary.datastore.compose.rememberLastKnownCity
import com.yjsoft.roadtravel.basiclibrary.datastore.compose.rememberLoginState
import com.yjsoft.roadtravel.basiclibrary.datastore.compose.rememberThemeMode
import com.yjsoft.roadtravel.basiclibrary.datastore.model.CommonPreferenceKeys
import com.yjsoft.roadtravel.basiclibrary.datastore.model.PreferenceKey
import kotlinx.coroutines.launch

/**
 * DataStore使用示例
 * 展示各种DataStore功能的使用方法
 */

/**
 * 基本使用示例
 */
@Composable
fun BasicDataStoreExample() {
    // 使用预定义的键
    val (userName, setUserName) = rememberDataStoreValue(CommonPreferenceKeys.USER_NAME)
    val (isFirstLaunch, setIsFirstLaunch) = rememberDataStoreValue(CommonPreferenceKeys.IS_FIRST_LAUNCH)
    
    var inputText by remember { mutableStateOf("") }
    val scope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "基本DataStore使用示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        // 显示当前用户名
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "当前用户名: $userName",
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = "是否首次启动: $isFirstLaunch",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
        
        // 输入新用户名
        OutlinedTextField(
            value = inputText,
            onValueChange = { inputText = it },
            label = { Text("输入新用户名") },
            modifier = Modifier.fillMaxWidth()
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = {
                    scope.launch {
                        setUserName(inputText)
                        inputText = ""
                    }
                }
            ) {
                Text("保存用户名")
            }
            
            OutlinedButton(
                onClick = {
                    scope.launch {
                        setIsFirstLaunch(!isFirstLaunch)
                    }
                }
            ) {
                Text("切换首次启动状态")
            }
        }
    }
}

/**
 * 自定义键使用示例
 */
@Composable
fun CustomKeyExample() {
    // 创建自定义键
    val customStringKey = remember {
        PreferenceKey.string(
            name = "custom_message",
            defaultValue = "Hello DataStore!",
            description = "自定义消息"
        )
    }
    
    val customCountKey = remember {
        PreferenceKey.int(
            name = "click_count",
            defaultValue = 0,
            description = "点击次数"
        )
    }
    
    val (message, setMessage) = rememberDataStoreValue(customStringKey)
    val (count, setCount) = rememberDataStoreValue(customCountKey)
    
    var inputMessage by remember { mutableStateOf("") }
    val scope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "自定义键使用示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "自定义消息: $message",
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = "点击次数: $count",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
        
        OutlinedTextField(
            value = inputMessage,
            onValueChange = { inputMessage = it },
            label = { Text("输入自定义消息") },
            modifier = Modifier.fillMaxWidth()
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Button(
                onClick = {
                    scope.launch {
                        setMessage(inputMessage)
                        inputMessage = ""
                    }
                }
            ) {
                Text("保存消息")
            }
            
            Button(
                onClick = {
                    scope.launch {
                        setCount(count + 1)
                    }
                }
            ) {
                Text("增加计数")
            }
            
            OutlinedButton(
                onClick = {
                    scope.launch {
                        setCount(0)
                    }
                }
            ) {
                Text("重置计数")
            }
        }
    }
}

/**
 * 高级功能示例
 */
@Composable
fun AdvancedDataStoreExample() {
    val (isLoggedIn, logout) = rememberLoginState()
    val (themeMode, setThemeMode) = rememberThemeMode()
    val (lastCity, setLastCity) = rememberLastKnownCity()
    val (favoriteCities, addFavorite, removeFavorite) = rememberFavoriteCities()
    
    var newCityInput by remember { mutableStateOf("") }
    val scope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "高级功能示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        // 登录状态
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "登录状态",
                    style = MaterialTheme.typography.titleMedium
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = if (isLoggedIn) "已登录" else "未登录",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    if (isLoggedIn) {
                        Button(
                            onClick = {
                                scope.launch { logout() }
                            }
                        ) {
                            Text("登出")
                        }
                    }
                }
            }
        }
        
        // 主题设置
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "主题设置: $themeMode",
                    style = MaterialTheme.typography.titleMedium
                )
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("light", "dark", "system").forEach { theme ->
                        Button(
                            onClick = {
                                scope.launch { setThemeMode(theme) }
                            }
                        ) {
                            Text(theme)
                        }
                    }
                }
            }
        }
        
        // 城市管理
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "最后已知城市: $lastCity",
                    style = MaterialTheme.typography.titleMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = newCityInput,
                        onValueChange = { newCityInput = it },
                        label = { Text("城市名称") },
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = {
                            scope.launch {
                                setLastCity(newCityInput)
                                addFavorite(newCityInput)
                                newCityInput = ""
                            }
                        }
                    ) {
                        Text("设置")
                    }
                }
            }
        }
        
        // 收藏城市列表
        if (favoriteCities.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "收藏城市 (${favoriteCities.size})",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    LazyColumn(
                        modifier = Modifier.height(120.dp),
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        items(favoriteCities.toList()) { city ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = city,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                OutlinedButton(
                                    onClick = {
                                        scope.launch { removeFavorite(city) }
                                    }
                                ) {
                                    Text("移除")
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 开关设置示例
 */
@Composable
fun SettingsExample() {
    val (autoLocationEnabled, setAutoLocationEnabled) = rememberDataStoreValue(CommonPreferenceKeys.AUTO_LOCATION_ENABLED)
    val (notificationEnabled, setNotificationEnabled) = rememberDataStoreValue(CommonPreferenceKeys.NOTIFICATION_ENABLED)
    val (autoBackupEnabled, setAutoBackupEnabled) = rememberDataStoreValue(CommonPreferenceKeys.AUTO_BACKUP_ENABLED)
    
    val scope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "设置示例",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 自动定位开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "自动定位",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Switch(
                        checked = autoLocationEnabled,
                        onCheckedChange = { enabled ->
                            scope.launch { setAutoLocationEnabled(enabled) }
                        }
                    )
                }
                
                // 通知开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "推送通知",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Switch(
                        checked = notificationEnabled,
                        onCheckedChange = { enabled ->
                            scope.launch { setNotificationEnabled(enabled) }
                        }
                    )
                }
                
                // 自动备份开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "自动备份",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Switch(
                        checked = autoBackupEnabled,
                        onCheckedChange = { enabled ->
                            scope.launch { setAutoBackupEnabled(enabled) }
                        }
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun BasicDataStoreExamplePreview() {
    MaterialTheme {
        BasicDataStoreExample()
    }
}
