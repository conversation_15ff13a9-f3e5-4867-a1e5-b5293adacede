package com.yjsoft.roadtravel.basiclibrary.payment.models

import java.math.BigDecimal

/**
 * 支付请求数据模型
 * 包含发起支付所需的所有参数
 */
data class PaymentRequest(
    /**
     * 订单ID，必须唯一
     */
    val orderId: String,
    
    /**
     * 支付金额（元）
     */
    val amount: BigDecimal,
    
    /**
     * 商品标题
     */
    val title: String,
    
    /**
     * 商品描述（可选）
     */
    val description: String? = null,
    
    /**
     * 异步通知地址（可选）
     */
    val notifyUrl: String? = null,
    
    /**
     * 同步返回地址（可选）
     */
    val returnUrl: String? = null,
    
    /**
     * 扩展参数
     */
    val extraParams: Map<String, String> = emptyMap(),
    
    /**
     * 请求时间戳
     */
    val timestamp: Long = System.currentTimeMillis()
) {
    
    /**
     * 获取金额的分值（用于某些SDK）
     */
    fun getAmountInCents(): Long {
        return amount.multiply(BigDecimal(100)).toLong()
    }
    
    /**
     * 获取金额的字符串表示
     */
    fun getAmountString(): String {
        return amount.toPlainString()
    }
    
    /**
     * 验证支付请求参数是否有效
     */
    fun isValid(): Boolean {
        return orderId.isNotBlank() && 
               amount > BigDecimal.ZERO && 
               title.isNotBlank()
    }
    
    /**
     * 获取用于签名的参数字符串
     */
    fun getSignatureParams(): Map<String, String> {
        val params = mutableMapOf<String, String>()
        params["order_id"] = orderId
        params["amount"] = getAmountString()
        params["title"] = title
        description?.let { params["description"] = it }
        notifyUrl?.let { params["notify_url"] = it }
        returnUrl?.let { params["return_url"] = it }
        params["timestamp"] = timestamp.toString()
        params.putAll(extraParams)
        return params
    }
    
    companion object {
        /**
         * 创建简单的支付请求
         */
        fun simple(
            orderId: String,
            amount: Double,
            title: String
        ): PaymentRequest {
            return PaymentRequest(
                orderId = orderId,
                amount = BigDecimal.valueOf(amount),
                title = title
            )
        }
        
        /**
         * 创建带描述的支付请求
         */
        fun withDescription(
            orderId: String,
            amount: Double,
            title: String,
            description: String
        ): PaymentRequest {
            return PaymentRequest(
                orderId = orderId,
                amount = BigDecimal.valueOf(amount),
                title = title,
                description = description
            )
        }
    }
}
