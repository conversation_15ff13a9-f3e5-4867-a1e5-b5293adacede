package com.yjsoft.roadtravel.utils

import android.os.SystemClock
import android.util.Log

/**
 * 启动时间记录工具
 * 用于在LogManager初始化之前记录启动时间
 */
object StartupTimer {
    
    private const val TAG = "StartupTimer"
    
    // 启动时间记录
    private val startTimes = mutableMapOf<String, Long>()
    
    // 应用启动时间
    val appStartTime = SystemClock.elapsedRealtime()
    
    /**
     * 记录开始时间
     */
    fun start(key: String) {
        val currentTime = SystemClock.elapsedRealtime()
        startTimes[key] = currentTime
        Log.d(TAG, "开始计时: $key (${currentTime - appStartTime}ms)")
    }
    
    /**
     * 记录结束时间并返回耗时
     */
    fun end(key: String): Long {
        val endTime = SystemClock.elapsedRealtime()
        val startTime = startTimes[key] ?: appStartTime
        val duration = endTime - startTime
        
        Log.i(TAG, "计时完成: $key = ${duration}ms (总耗时: ${endTime - appStartTime}ms)")
        
        return duration
    }
    
    /**
     * 获取从应用启动到现在的总时间
     */
    fun getTotalTime(): Long {
        return SystemClock.elapsedRealtime() - appStartTime
    }
    
    /**
     * 记录里程碑
     */
    fun milestone(name: String) {
        val currentTime = SystemClock.elapsedRealtime()
        Log.i(TAG, "里程碑: $name (${currentTime - appStartTime}ms)")
    }
}
