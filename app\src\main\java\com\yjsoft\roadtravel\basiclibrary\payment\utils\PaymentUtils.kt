package com.yjsoft.roadtravel.basiclibrary.payment.utils

import android.util.Base64
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.text.SimpleDateFormat
import java.util.*

/**
 * 支付工具类
 * 提供支付相关的工具方法
 */
object PaymentUtils {

    private const val TAG = "PaymentUtils %s"
    
    /**
     * 获取当前时间戳字符串
     */
    fun getCurrentTimestamp(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return sdf.format(Date())
    }
    
    /**
     * 生成随机字符串
     */
    fun generateNonceStr(length: Int = 32): String {
        val chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        val random = Random()
        val sb = StringBuilder()
        
        repeat(length) {
            sb.append(chars[random.nextInt(chars.length)])
        }
        
        return sb.toString()
    }
    
    /**
     * 将对象转换为JSON字符串
     */
    fun toJsonString(obj: Any): String {
        return try {
            Gson().toJson(obj)
        } catch (e: Exception) {
            LogManager.e(TAG, "JSON序列化失败", e)
            "{}"
        }
    }
    
    /**
     * 从JSON字符串解析对象
     */
    inline fun <reified T> fromJsonString(json: String): T? {
        return try {
            Gson().fromJson(json, object : TypeToken<T>() {}.type)
        } catch (e: Exception) {
            LogManager.e("PaymentUtils", "JSON反序列化失败", e)
            null
        }
    }
    
    /**
     * 生成支付宝签名
     */
    fun generateAlipaySign(params: Map<String, String>, privateKey: String): String {
        return try {
            // 过滤空值并排序
            val filteredParams = params.filter { it.value.isNotEmpty() && it.key != "sign" }
            val sortedParams = filteredParams.toSortedMap()
            
            // 构建待签名字符串
            val signString = sortedParams.map { "${it.key}=${it.value}" }.joinToString("&")
            LogManager.d(TAG, "待签名字符串: $signString")
            
            // RSA2签名
            signWithRSA2(signString, privateKey)
        } catch (e: Exception) {
            LogManager.e(TAG, "生成支付宝签名失败", e)
            ""
        }
    }
    
    /**
     * RSA2签名
     */
    private fun signWithRSA2(content: String, privateKey: String): String {
        return try {
            // 解码私钥
            val keyBytes = Base64.decode(privateKey, Base64.DEFAULT)
            val keySpec = PKCS8EncodedKeySpec(keyBytes)
            val keyFactory = KeyFactory.getInstance("RSA")
            val priKey: PrivateKey = keyFactory.generatePrivate(keySpec)
            
            // 签名
            val signature = Signature.getInstance("SHA256WithRSA")
            signature.initSign(priKey)
            signature.update(content.toByteArray(StandardCharsets.UTF_8))
            val signed = signature.sign()
            
            // Base64编码
            Base64.encodeToString(signed, Base64.NO_WRAP)
        } catch (e: Exception) {
            LogManager.e(TAG, "RSA2签名失败", e)
            ""
        }
    }
    
    /**
     * 从支付结果中提取交易号
     */
    fun extractTransactionId(resultInfo: String): String {
        return try {
            // 解析result字段，提取trade_no
            val params = parseQueryString(resultInfo)
            params["trade_no"] ?: generateTransactionId()
        } catch (e: Exception) {
            LogManager.e(TAG, "提取交易号失败", e)
            generateTransactionId()
        }
    }
    
    /**
     * 解析查询字符串
     */
    private fun parseQueryString(queryString: String): Map<String, String> {
        val params = mutableMapOf<String, String>()
        
        queryString.split("&").forEach { param ->
            val keyValue = param.split("=", limit = 2)
            if (keyValue.size == 2) {
                params[keyValue[0]] = keyValue[1]
            }
        }
        
        return params
    }
    
    /**
     * 生成交易号
     */
    private fun generateTransactionId(): String {
        return "TXN_${System.currentTimeMillis()}_${generateNonceStr(8)}"
    }
    
    /**
     * 验证金额格式
     */
    fun isValidAmount(amount: String): Boolean {
        return try {
            val value = amount.toDouble()
            value > 0 && value <= 999999.99
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 格式化金额（保留两位小数）
     */
    fun formatAmount(amount: Double): String {
        return String.format(Locale.getDefault(), "%.2f", amount)
    }
    
    /**
     * 验证订单号格式
     */
    fun isValidOrderId(orderId: String): Boolean {
        return orderId.isNotBlank() && 
               orderId.length <= 64 && 
               orderId.matches(Regex("^[a-zA-Z0-9_-]+$"))
    }
    
    /**
     * 生成MD5哈希
     */
    fun md5(input: String): String {
        return try {
            val md = java.security.MessageDigest.getInstance("MD5")
            val digest = md.digest(input.toByteArray())
            digest.joinToString("") { "%02x".format(Locale.getDefault(), it) }
        } catch (e: Exception) {
            LogManager.e(TAG, "MD5计算失败", e)
            ""
        }
    }
    
    /**
     * URL编码
     */
    fun urlEncode(input: String): String {
        return try {
            java.net.URLEncoder.encode(input, "UTF-8")
        } catch (e: Exception) {
            LogManager.e(TAG, "URL编码失败", e)
            input
        }
    }
    
    /**
     * URL解码
     */
    fun urlDecode(input: String): String {
        return try {
            java.net.URLDecoder.decode(input, "UTF-8")
        } catch (e: Exception) {
            LogManager.e(TAG, "URL解码失败", e)
            input
        }
    }
}
