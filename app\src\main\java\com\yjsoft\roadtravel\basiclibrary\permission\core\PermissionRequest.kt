package com.yjsoft.roadtravel.basiclibrary.permission.core

/**
 * 权限请求模型
 * 封装权限请求的相关信息
 */
data class PermissionRequest(
    /** 请求的权限列表 */
    val permissions: List<String>,
    /** 请求配置 */
    val config: PermissionRequestConfig = PermissionRequestConfig(),
    /** 请求ID，用于标识和追踪请求 */
    val requestId: String = generateRequestId(),
    /** 请求时间戳 */
    val timestamp: Long = System.currentTimeMillis(),
    /** 请求来源标识（可选，用于日志和调试） */
    val source: String? = null
) {
    companion object {
        /**
         * 生成唯一的请求ID
         */
        private fun generateRequestId(): String {
            return "perm_req_${System.currentTimeMillis()}_${(1000..9999).random()}"
        }
        
        /**
         * 创建单个权限请求
         */
        fun single(
            permission: String,
            config: PermissionRequestConfig = PermissionRequestConfig(),
            source: String? = null
        ): PermissionRequest {
            return PermissionRequest(
                permissions = listOf(permission),
                config = config,
                source = source
            )
        }
        
        /**
         * 创建多个权限请求
         */
        fun multiple(
            permissions: List<String>,
            config: PermissionRequestConfig = PermissionRequestConfig(),
            source: String? = null
        ): PermissionRequest {
            return PermissionRequest(
                permissions = permissions,
                config = config,
                source = source
            )
        }
    }
    
    /**
     * 是否为单个权限请求
     */
    val isSinglePermission: Boolean
        get() = permissions.size == 1
    
    /**
     * 获取第一个权限（用于单个权限请求）
     */
    val firstPermission: String?
        get() = permissions.firstOrNull()
    
    /**
     * 权限数量
     */
    val permissionCount: Int
        get() = permissions.size
    
    /**
     * 检查是否包含指定权限
     */
    fun containsPermission(permission: String): Boolean {
        return permissions.contains(permission)
    }
    
    /**
     * 创建副本并修改配置
     */
    fun withConfig(newConfig: PermissionRequestConfig): PermissionRequest {
        return copy(config = newConfig)
    }
    
    /**
     * 创建副本并添加来源标识
     */
    fun withSource(newSource: String): PermissionRequest {
        return copy(source = newSource)
    }
}

/**
 * 权限请求构建器
 * 提供流式API来构建权限请求
 */
class PermissionRequestBuilder {
    private val permissions = mutableListOf<String>()
    private var config = PermissionRequestConfig()
    private var source: String? = null
    
    /**
     * 添加权限
     */
    fun permission(permission: String): PermissionRequestBuilder {
        permissions.add(permission)
        return this
    }
    
    /**
     * 添加多个权限
     */
    fun permissions(vararg permissions: String): PermissionRequestBuilder {
        this.permissions.addAll(permissions)
        return this
    }
    
    /**
     * 添加权限列表
     */
    fun permissions(permissions: List<String>): PermissionRequestBuilder {
        this.permissions.addAll(permissions)
        return this
    }
    
    /**
     * 设置权限说明对话框配置
     */
    fun rationale(
        show: Boolean = true,
        title: String = "权限申请",
        message: String = "应用需要此权限才能正常工作"
    ): PermissionRequestBuilder {
        config = config.copy(
            showRationaleDialog = show,
            rationaleTitle = title,
            rationaleMessage = message
        )
        return this
    }
    
    /**
     * 设置设置引导对话框配置
     */
    fun settings(
        show: Boolean = true,
        title: String = "权限设置",
        message: String = "请在设置中手动开启权限",
        autoNavigate: Boolean = false
    ): PermissionRequestBuilder {
        config = config.copy(
            showSettingsDialog = show,
            settingsTitle = title,
            settingsMessage = message,
            autoNavigateToSettings = autoNavigate
        )
        return this
    }
    
    /**
     * 设置来源标识
     */
    fun source(source: String): PermissionRequestBuilder {
        this.source = source
        return this
    }
    
    /**
     * 构建权限请求
     */
    fun build(): PermissionRequest {
        require(permissions.isNotEmpty()) { "至少需要指定一个权限" }
        
        return PermissionRequest(
            permissions = permissions.toList(),
            config = config,
            source = source
        )
    }
}

/**
 * 权限请求DSL扩展函数
 */
fun permissionRequest(block: PermissionRequestBuilder.() -> Unit): PermissionRequest {
    return PermissionRequestBuilder().apply(block).build()
}
