package com.yjsoft.roadtravel.basiclibrary.payment.models

/**
 * 支付状态数据类
 * 用于管理支付过程中的状态信息
 */
data class PaymentState(
    /**
     * 当前支付状态
     */
    val status: PaymentStatus = PaymentStatus.IDLE,
    
    /**
     * 当前支付类型
     */
    val paymentType: PaymentType? = null,
    
    /**
     * 当前订单ID
     */
    val orderId: String? = null,
    
    /**
     * 支付金额
     */
    val amount: String? = null,
    
    /**
     * 错误信息
     */
    val errorMessage: String? = null,
    
    /**
     * 错误码
     */
    val errorCode: String? = null,
    
    /**
     * 是否正在加载
     */
    val isLoading: Boolean = false,
    
    /**
     * 进度信息
     */
    val progressMessage: String? = null,
    
    /**
     * 支付结果
     */
    val result: PaymentResult? = null,
    
    /**
     * 额外数据
     */
    val extraData: Map<String, Any> = emptyMap()
) {
    
    /**
     * 是否为成功状态
     */
    val isSuccess: Boolean
        get() = status == PaymentStatus.SUCCESS
    
    /**
     * 是否为失败状态
     */
    val isError: Boolean
        get() = status == PaymentStatus.FAILED
    
    /**
     * 是否为取消状态
     */
    val isCancelled: Boolean
        get() = status == PaymentStatus.CANCELLED
    
    /**
     * 是否为处理中状态
     */
    val isProcessing: Boolean
        get() = status == PaymentStatus.PROCESSING
    
    /**
     * 是否可以重试
     */
    val canRetry: Boolean
        get() = status in listOf(PaymentStatus.FAILED, PaymentStatus.CANCELLED)
    
    companion object {
        /**
         * 创建空闲状态
         */
        fun idle(): PaymentState = PaymentState()
        
        /**
         * 创建加载状态
         */
        fun loading(
            paymentType: PaymentType? = null,
            orderId: String? = null,
            message: String? = null
        ): PaymentState = PaymentState(
            status = PaymentStatus.PREPARING,
            paymentType = paymentType,
            orderId = orderId,
            isLoading = true,
            progressMessage = message
        )
        
        /**
         * 创建支付中状态
         */
        fun paying(
            paymentType: PaymentType,
            orderId: String,
            amount: String
        ): PaymentState = PaymentState(
            status = PaymentStatus.PAYING,
            paymentType = paymentType,
            orderId = orderId,
            amount = amount,
            isLoading = true,
            progressMessage = "正在支付..."
        )
        
        /**
         * 创建成功状态
         */
        fun success(result: PaymentResult.Success): PaymentState = PaymentState(
            status = PaymentStatus.SUCCESS,
            paymentType = result.paymentType,
            orderId = result.orderId,
            amount = result.amount,
            result = result
        )
        
        /**
         * 创建失败状态
         */
        fun error(result: PaymentResult.Error): PaymentState = PaymentState(
            status = PaymentStatus.FAILED,
            paymentType = result.paymentType,
            orderId = result.orderId,
            errorMessage = result.errorMessage,
            errorCode = result.errorCode,
            result = result
        )
        
        /**
         * 创建取消状态
         */
        fun cancelled(result: PaymentResult.Cancel): PaymentState = PaymentState(
            status = PaymentStatus.CANCELLED,
            paymentType = result.paymentType,
            orderId = result.orderId,
            result = result
        )
        
        /**
         * 创建处理中状态
         */
        fun processing(result: PaymentResult.Processing): PaymentState = PaymentState(
            status = PaymentStatus.PROCESSING,
            paymentType = result.paymentType,
            orderId = result.orderId,
            isLoading = true,
            progressMessage = result.message,
            result = result
        )
    }
}

/**
 * 支付状态枚举
 */
enum class PaymentStatus {
    /**
     * 空闲状态
     */
    IDLE,
    
    /**
     * 初始化中
     */
    INITIALIZING,
    
    /**
     * 准备支付
     */
    PREPARING,
    
    /**
     * 支付中
     */
    PAYING,
    
    /**
     * 支付成功
     */
    SUCCESS,
    
    /**
     * 支付取消
     */
    CANCELLED,
    
    /**
     * 支付失败
     */
    FAILED,
    
    /**
     * 处理中
     */
    PROCESSING
}
