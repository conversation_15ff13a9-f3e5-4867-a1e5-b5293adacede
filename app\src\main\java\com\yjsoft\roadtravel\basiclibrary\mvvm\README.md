# MVVM架构框架

## 概述

本MVVM架构框架为RoadTravel项目提供了完整的Model-View-ViewModel架构实现，集成了Hilt依赖注入、Jetpack Compose、协程等现代Android开发技术。

## 架构特性

### 🎯 核心特性
- **完整的MVVM实现**：提供BaseViewModel、BaseRepository、BaseActivity等基类
- **状态管理**：统一的UiState和Resource状态管理
- **事件处理**：单次事件处理机制，防止配置变更重复触发
- **Hilt集成**：完美集成依赖注入框架
- **Compose优化**：专为Jetpack Compose设计的扩展函数
- **生命周期感知**：自动处理Android生命周期

### 🛠️ 技术栈
- **架构模式**：MVVM (Model-View-ViewModel)
- **依赖注入**：Hilt
- **UI框架**：Jetpack Compose
- **异步处理**：Kotlin Coroutines + Flow
- **状态管理**：StateFlow + UiState/Resource
- **网络请求**：Retrofit + 统一异常处理

## 目录结构

```
mvvm/
├── base/                    # 基类
│   ├── BaseViewModel.kt     # ViewModel基类
│   ├── BaseRepository.kt    # Repository基类
│   ├── BaseActivity.kt      # Activity基类
│   └── BaseFragment.kt      # Fragment基类
├── state/                   # 状态管理
│   ├── UiState.kt          # UI状态封装
│   ├── Resource.kt         # 资源状态封装
│   └── Event.kt            # 事件封装
├── extensions/             # 扩展函数
│   ├── ViewModelExtensions.kt
│   ├── RepositoryExtensions.kt
│   └── ComposeExtensions.kt
├── utils/                  # 工具类
│   ├── MVVMUtils.kt
│   └── StateUtils.kt
├── examples/               # 使用示例
│   ├── ExampleViewModel.kt
│   ├── ExampleRepository.kt
│   └── ExampleActivity.kt
└── README.md               # 本文档
```

## 快速开始

### 1. 创建数据类

```kotlin
// UI状态数据类
data class UserUiState(
    val isInitialized: Boolean = false,
    val userName: String = "",
    val lastAction: String = ""
)

// 业务数据类
data class User(
    val id: String,
    val name: String,
    val email: String
)
```

### 2. 创建Repository

```kotlin
@Singleton
class UserRepository @Inject constructor(
    private val apiService: UserApiService,
    private val userDao: UserDao
) : BaseRepository() {
    
    suspend fun getUser(id: String): Resource<User> = safeApiCall {
        apiService.getUser(id)
    }
    
    fun getUserFlow(id: String): Flow<Resource<User>> = networkBoundResource(
        query = { userDao.getUserById(id) },
        fetch = { apiService.getUser(id) },
        saveFetchResult = { user -> userDao.insertUser(user) }
    )
}
```

### 3. 创建ViewModel

```kotlin
@HiltViewModel
class UserViewModel @Inject constructor(
    private val userRepository: UserRepository
) : BaseViewModel<UserUiState>() {
    
    override fun createInitialState(): UserUiState = UserUiState()
    
    private val _userState = MutableStateFlow<UiState<User>>(UiState.Idle)
    val userState: StateFlow<UiState<User>> = _userState.asStateFlow()
    
    fun loadUser(id: String) {
        launchWithUiState(_userState) {
            userRepository.getUser(id).getDataOrNull() 
                ?: throw Exception("用户不存在")
        }
    }
    
    fun updateUserName(name: String) {
        updateState { it.copy(userName = name) }
        showToast("用户名已更新")
    }
}
```

### 4. 创建Activity

```kotlin
@AndroidEntryPoint
class UserActivity : BaseActivity() {
    
    private val viewModel: UserViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupContent {
            UserScreen(viewModel = viewModel)
        }
        
        observeEvents(
            uiEvents = viewModel.uiEvents,
            navigationEvents = viewModel.navigationEvents
        )
    }
}
```

### 5. 创建Compose UI

```kotlin
@Composable
fun UserScreen(viewModel: UserViewModel) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val userState by viewModel.userState.collectAsUiState()
    
    Column {
        // 显示UI状态
        Text("用户名: ${uiState.userName}")
        
        // 处理用户数据状态
        UiStateHandler(
            uiState = userState,
            onRetry = { viewModel.loadUser("123") }
        ) { user ->
            UserContent(user = user)
        }
        
        Button(
            onClick = { viewModel.updateUserName("新用户名") }
        ) {
            Text("更新用户名")
        }
    }
}

@Composable
fun UserContent(user: User) {
    Card {
        Column(modifier = Modifier.padding(16.dp)) {
            Text("ID: ${user.id}")
            Text("姓名: ${user.name}")
            Text("邮箱: ${user.email}")
        }
    }
}
```

## 核心组件详解

### BaseViewModel

提供统一的ViewModel基类，包含：
- 状态管理（StateFlow）
- 事件处理（UI事件、导航事件）
- 异常处理
- 协程管理
- 加载状态管理

### BaseRepository

提供统一的数据访问基类，包含：
- 网络请求封装
- 缓存策略
- 异常处理
- 线程切换
- 数据转换

### UiState

统一的UI状态管理：
- `Idle` - 空闲状态
- `Loading` - 加载状态
- `Success` - 成功状态
- `Error` - 错误状态
- `Empty` - 空数据状态

### Resource

网络请求结果封装：
- `Success` - 请求成功
- `Error` - 请求失败
- `Loading` - 请求中

## 最佳实践

### 1. 状态管理
- 使用StateFlow管理状态
- UI状态与业务数据分离
- 单向数据流

### 2. 事件处理
- UI事件与状态分离
- 使用Event包装防止重复触发
- 生命周期感知

### 3. 异常处理
- 统一的异常处理机制
- 用户友好的错误提示
- 异常日志记录

### 4. 性能优化
- 使用remember避免重组
- 合理使用LaunchedEffect
- 状态收集生命周期感知

## 扩展功能

### 缓存策略
- `CACHE_ONLY` - 仅使用缓存
- `NETWORK_ONLY` - 仅使用网络
- `CACHE_FIRST` - 缓存优先
- `NETWORK_FIRST` - 网络优先
- `CACHE_THEN_NETWORK` - 先缓存后网络

### 分页支持
```kotlin
fun getPagedUsers(page: Int): Flow<Resource<PagedResource<User>>> = 
    pagedNetworkResource(
        page = page,
        pageSize = 20,
        fetch = { p, size -> apiService.getUsers(p, size) }
    )
```

## 调试工具

### 日志记录
- 自动记录状态变化
- 性能监控
- 异常追踪

### 状态验证
- UiState完整性检查
- Resource数据验证
- ViewModel状态监控

## 注意事项

1. **ViewModel注入**：使用`by viewModels()`而不是`@Inject`
2. **状态收集**：使用`collectAsStateWithLifecycle()`确保生命周期安全
3. **异常处理**：始终处理可能的异常情况
4. **内存泄漏**：避免在ViewModel中持有Context引用
5. **线程安全**：网络请求和数据库操作使用适当的调度器

## 示例项目

查看`examples/`目录下的完整示例：
- `ExampleViewModel.kt` - ViewModel使用示例
- `ExampleRepository.kt` - Repository使用示例  
- `ExampleActivity.kt` - Activity使用示例

## 测试支持

### 测试基类
```kotlin
class UserViewModelTest : MVVMTestBase() {

    private lateinit var viewModel: UserViewModel
    private lateinit var repository: FakeUserRepository

    @Before
    override fun setUp() {
        super.setUp()
        repository = FakeUserRepository()
        viewModel = UserViewModel(repository)
    }

    @Test
    fun `test load user success`() = runTest {
        // Given
        val expectedUser = User("1", "Test User", "<EMAIL>")
        repository.setUser(expectedUser)

        // When
        viewModel.loadUser("1")

        // Then
        val state = viewModel.userState.first()
        assertUiStateSuccess(state, expectedUser)
    }
}
```

### 测试工具
- `MVVMTestBase` - 测试基类
- 协程测试支持
- UiState断言工具
- Resource断言工具
- Flow测试工具

## 依赖注入

### Hilt模块
框架提供了`MVVMModule`用于依赖注入配置：

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object MVVMModule {

    @Provides
    @Singleton
    fun provideExampleRepository(): ExampleRepository {
        return ExampleRepository()
    }
}
```

### 自定义限定符
```kotlin
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MVVMCache

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class StateCache
```

## 常见问题

### Q: ViewModel注入失败？
A: 使用`by viewModels()`而不是`@Inject`注解。

### Q: 状态收集导致内存泄漏？
A: 使用`collectAsStateWithLifecycle()`确保生命周期安全。

### Q: UiState和Resource的区别？
A: UiState用于UI状态管理，Resource用于网络请求结果封装。

### Q: 如何处理配置变更？
A: ViewModel会自动保持状态，使用Event处理一次性事件。

## 性能优化

### 1. 状态收集优化
```kotlin
// ✅ 推荐：生命周期感知
val state by viewModel.uiState.collectAsStateWithLifecycle()

// ❌ 不推荐：可能导致内存泄漏
val state by viewModel.uiState.collectAsState()
```

### 2. 重组优化
```kotlin
// ✅ 推荐：使用remember避免重组
val config = remember(uiState, onRetry) {
    Pair(uiState, onRetry)
}
```

### 3. 协程优化
```kotlin
// ✅ 推荐：使用扩展函数
launchWithUiState(_userState) {
    repository.getUser(id)
}

// ❌ 不推荐：手动管理状态
viewModelScope.launch {
    _userState.value = UiState.Loading()
    try {
        val user = repository.getUser(id)
        _userState.value = UiState.Success(user)
    } catch (e: Exception) {
        _userState.value = UiState.Error(e, e.message ?: "Error")
    }
}
```

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的MVVM架构实现
- Hilt集成
- Compose扩展函数
- 使用示例和文档
- 测试支持
- 依赖注入模块
