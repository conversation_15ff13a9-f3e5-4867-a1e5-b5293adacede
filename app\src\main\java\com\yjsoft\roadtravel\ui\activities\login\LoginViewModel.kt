package com.yjsoft.roadtravel.ui.activities.login

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatLoginService
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatLoginServiceResult
import com.yjsoft.roadtravel.basiclibrary.auth.wechat.WeChatUserInfo
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 登录页面ViewModel
 */
class LoginViewModel : ViewModel() {

    // 使用单例，不再依赖注入
    private val weChatLoginService: WeChatLoginService by lazy {
        WeChatLoginService.getInstance()
    }
    
    // DataStore 仓库
    private val dataStoreRepository: DataStoreRepository by lazy {
        DataStoreRepository.getInstance()
    }
    
    companion object {
        private const val TAG = "LoginViewModel %s"
    }
    
    // 登录状态
    private val _loginState = MutableStateFlow<UiState<LoginResult>>(UiState.Idle)
    val loginState: StateFlow<UiState<LoginResult>> = _loginState.asStateFlow()
    
    // 微信安装状态
    private val _isWeChatInstalled = MutableStateFlow(false)
    val isWeChatInstalled: StateFlow<Boolean> = _isWeChatInstalled.asStateFlow()
    
    // 错误消息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    /**
     * 初始化
     */
    fun initialize(context: Context) {
        viewModelScope.launch {
            try {
                // 初始化微信登录服务
                val initResult = weChatLoginService.initialize(context)
                LogManager.d(TAG, "微信登录服务初始化结果: $initResult")
                
                // 检查微信是否已安装
                _isWeChatInstalled.value = weChatLoginService.isWeChatInstalled()
                LogManager.d(TAG, "微信安装状态: ${_isWeChatInstalled.value}")
                
            } catch (e: Exception) {
                LogManager.e(TAG, "初始化失败", e)
                _errorMessage.value = "初始化失败: ${e.message}"
            }
        }
    }
    
    /**
     * 微信登录
     * @param context 应用上下文，用于网络服务调用
     */
    fun loginWithWeChat(context: Context) {
        viewModelScope.launch {
            try {
                LogManager.d(TAG, "开始微信登录")
                _loginState.value = UiState.Loading("正在启动微信...") as UiState<LoginResult>
                
                // 检查微信是否已安装
                if (!weChatLoginService.isWeChatInstalled()) {
                    _loginState.value = UiState.Error(message = "请先安装微信客户端") as UiState<LoginResult>
                    return@launch
                }
                
                // 执行微信登录
                val result = weChatLoginService.performLogin(context)
                
                when (result) {
                    is WeChatLoginServiceResult.Success -> {
                        LogManager.d(TAG, "微信登录成功: ${result.userInfo.nickname}")
                        
                        // 保存微信用户信息到 DataStore
                        saveWeChatUserInfoToDataStore(result)
                        
                        _loginState.value = UiState.Success(
                            LoginResult.WeChatSuccess(
                                userInfo = result.userInfo,
                                accessToken = result.accessToken,
                                refreshToken = result.refreshToken,
                                expiresIn = result.expiresIn
                            )
                        )
                    }
                    is WeChatLoginServiceResult.Cancelled -> {
                        LogManager.d(TAG, "用户取消微信登录")
                        _loginState.value = UiState.Error(message = "用户取消登录") as UiState<LoginResult>
                    }
                    is WeChatLoginServiceResult.Denied -> {
                        LogManager.d(TAG, "用户拒绝微信授权")
                        _loginState.value = UiState.Error(message = "用户拒绝授权") as UiState<LoginResult>
                    }
                    is WeChatLoginServiceResult.Error -> {
                        LogManager.w(TAG, "微信登录失败: ${result.message}")
                        _loginState.value = UiState.Error(message = "登录失败: ${result.message}") as UiState<LoginResult>
                    }
                }
                
            } catch (e: Exception) {
                LogManager.e(TAG, "微信登录异常", e)
                _loginState.value = UiState.Error(exception = e, message = "登录异常: ${e.message}") as UiState<LoginResult>
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _errorMessage.value = null
        if (_loginState.value is UiState.Error) {
            _loginState.value = UiState.Idle as UiState<LoginResult>
        }
    }

    /**
     * 重置登录状态
     */
    fun resetLoginState() {
        _loginState.value = UiState.Idle as UiState<LoginResult>
    }
    
    /**
     * 保存微信用户信息到 DataStore
     */
    private fun saveWeChatUserInfoToDataStore(result: WeChatLoginServiceResult.Success) {
        viewModelScope.launch {
            try {
                LogManager.d(TAG, "开始保存微信用户信息到 DataStore")
                
                dataStoreRepository.saveWeChatUserInfo(
                    openId = result.userInfo.openId,
                    unionId = result.userInfo.unionId,
                    nickname = result.userInfo.nickname,
                    avatar = result.userInfo.avatar,
                    sex = result.userInfo.sex,
                    province = result.userInfo.province,
                    city = result.userInfo.city,
                    country = result.userInfo.country,
                    accessToken = result.accessToken,
                    refreshToken = result.refreshToken,
                    expiresIn = result.expiresIn
                )
                
                LogManager.d(TAG, "微信用户信息保存到 DataStore 成功")
            } catch (e: Exception) {
                LogManager.e(TAG, "保存微信用户信息到 DataStore 失败", e)
                // 这里不影响登录流程，只记录错误
            }
        }
    }
}

/**
 * 登录结果
 */
sealed class LoginResult {
    /**
     * 微信登录成功
     */
    data class WeChatSuccess(
        val userInfo: WeChatUserInfo,
        val accessToken: String,
        val refreshToken: String?,
        val expiresIn: Int
    ) : LoginResult()
    
    /**
     * 其他登录方式成功（预留）
     */
    data class OtherSuccess(
        val userId: String,
        val username: String,
        val token: String
    ) : LoginResult()
}
