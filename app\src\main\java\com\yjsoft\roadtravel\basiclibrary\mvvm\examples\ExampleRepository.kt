package com.yjsoft.roadtravel.basiclibrary.mvvm.examples

import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseRepository
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.networkBoundResource
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.safeApiCall
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.suspendToResourceFlow
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * MVVM示例Repository
 * 
 * 功能：
 * - 演示BaseRepository的使用
 * - 展示数据访问最佳实践
 * - 演示缓存策略
 * - 展示扩展函数的使用
 * 
 * 使用方式：
 * ```kotlin
 * @Singleton
 * class UserRepository @Inject constructor(
 *     private val apiService: ApiService,
 *     private val userDao: UserDao
 * ) : BaseRepository() {
 *     
 *     suspend fun getUser(id: String): Resource<User> = safeApiCall {
 *         apiService.getUser(id)
 *     }
 * }
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Singleton
class ExampleRepository @Inject constructor(
    // 这里可以注入真实的API服务和数据库DAO
    // private val apiService: ExampleApiService,
    // private val userDao: ExampleUserDao
) : BaseRepository() {
    
    companion object {
        private const val TAG = "ExampleRepository"
    }
    
    // 模拟本地缓存
    private val userCache = mutableListOf<ExampleUser>()
    private var lastCacheTime = 0L
    private val cacheValidDuration = 5 * 60 * 1000L // 5分钟
    
    // ========== 用户相关API ==========
    
    /**
     * 获取用户列表（挂起函数）
     */
    suspend fun getUserList(): List<ExampleUser> = safeApiCall(TAG) {
        logOperation("获取用户列表")
        
        // 模拟网络请求
        delay(1500)
        
        // 模拟数据
        generateMockUsers()
    }.getDataOrNull() ?: emptyList()
    
    /**
     * 获取用户列表（Flow）
     */
    fun getUserListFlow(): Flow<Resource<List<ExampleUser>>> {
        return networkBoundResource(
            tag = TAG,
            query = { getCachedUsers() },
            fetch = { fetchUsersFromNetwork() },
            saveFetchResult = { users -> saveUsersToCache(users) },
            shouldFetch = { cachedUsers -> shouldFetchUsers(cachedUsers) }
        )
    }
    
    /**
     * 获取单个用户
     */
    suspend fun getUser(userId: String): Resource<ExampleUser> = safeApiCall(TAG) {
        logOperation("获取用户详情", "ID: $userId")
        
        // 模拟网络请求
        delay(1000)
        
        // 从缓存或网络获取
        userCache.find { it.id == userId } 
            ?: generateMockUsers().find { it.id == userId }
            ?: throw Exception("用户不存在")
    }
    
    /**
     * 搜索用户
     */
    suspend fun searchUsers(query: String): List<ExampleUser> = safeApiCall(TAG) {
        logOperation("搜索用户", "关键词: $query")
        
        // 模拟网络请求
        delay(800)
        
        // 模拟搜索逻辑
        val allUsers = if (userCache.isNotEmpty()) userCache else generateMockUsers()
        allUsers.filter { 
            it.name.contains(query, ignoreCase = true) || 
            it.email.contains(query, ignoreCase = true) 
        }
    }.getDataOrNull() ?: emptyList()
    
    /**
     * 创建用户
     */
    suspend fun createUser(user: ExampleUser): Resource<ExampleUser> = safeApiCall(TAG) {
        logOperation("创建用户", "姓名: ${user.name}")
        
        // 模拟网络请求
        delay(1200)
        
        // 模拟创建逻辑
        val newUser = user.copy(id = generateUserId())
        userCache.add(newUser)
        
        newUser
    }
    
    /**
     * 更新用户
     */
    suspend fun updateUser(user: ExampleUser): Resource<ExampleUser> = safeApiCall(TAG) {
        logOperation("更新用户", "ID: ${user.id}")
        
        // 模拟网络请求
        delay(1000)
        
        // 模拟更新逻辑
        val index = userCache.indexOfFirst { it.id == user.id }
        if (index != -1) {
            userCache[index] = user
            user
        } else {
            throw Exception("用户不存在")
        }
    }
    
    /**
     * 删除用户
     */
    suspend fun deleteUser(userId: String): Resource<Unit> = safeApiCall(TAG) {
        logOperation("删除用户", "ID: $userId")
        
        // 模拟网络请求
        delay(800)
        
        // 模拟删除逻辑
        val removed = userCache.removeIf { it.id == userId }
        if (!removed) {
            throw Exception("用户不存在")
        }
    }
    
    // ========== 缓存相关方法 ==========
    
    /**
     * 获取缓存的用户列表
     */
    private suspend fun getCachedUsers(): List<ExampleUser>? {
        return if (isCacheValid()) {
            logOperation("使用缓存数据", "用户数量: ${userCache.size}")
            userCache.toList()
        } else {
            null
        }
    }
    
    /**
     * 从网络获取用户列表
     */
    private suspend fun fetchUsersFromNetwork(): List<ExampleUser> {
        logOperation("从网络获取数据")
        
        // 模拟网络请求
        delay(2000)
        
        return generateMockUsers()
    }
    
    /**
     * 保存用户到缓存
     */
    private suspend fun saveUsersToCache(users: List<ExampleUser>) {
        logOperation("保存到缓存", "用户数量: ${users.size}")
        
        userCache.clear()
        userCache.addAll(users)
        lastCacheTime = System.currentTimeMillis()
    }
    
    /**
     * 判断是否需要从网络获取数据
     */
    private fun shouldFetchUsers(cachedUsers: List<ExampleUser>?): Boolean {
        return cachedUsers == null || !isCacheValid()
    }
    
    /**
     * 检查缓存是否有效
     */
    private fun isCacheValid(): Boolean {
        return System.currentTimeMillis() - lastCacheTime < cacheValidDuration
    }
    
    // ========== 其他API示例 ==========
    
    /**
     * 获取用户统计信息
     */
    fun getUserStatsFlow(): Flow<Resource<UserStats>> {
        return suspendToResourceFlow(TAG) {
            delay(1000)
            
            val totalUsers = userCache.size
            val activeUsers = userCache.count { it.email.isNotEmpty() }
            
            UserStats(
                totalUsers = totalUsers,
                activeUsers = activeUsers,
                lastUpdateTime = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 批量操作示例
     */
    suspend fun batchUpdateUsers(users: List<ExampleUser>): Resource<List<ExampleUser>> = safeApiCall(TAG) {
        logOperation("批量更新用户", "数量: ${users.size}")
        
        // 模拟批量网络请求
        delay(2000)
        
        // 模拟批量更新逻辑
        users.forEach { user ->
            val index = userCache.indexOfFirst { it.id == user.id }
            if (index != -1) {
                userCache[index] = user
            }
        }
        
        users
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 生成模拟用户数据
     */
    private fun generateMockUsers(): List<ExampleUser> {
        return listOf(
            ExampleUser("1", "张三", "<EMAIL>"),
            ExampleUser("2", "李四", "<EMAIL>"),
            ExampleUser("3", "王五", "<EMAIL>"),
            ExampleUser("4", "赵六", "<EMAIL>"),
            ExampleUser("5", "钱七", "<EMAIL>")
        )
    }
    
    /**
     * 生成用户ID
     */
    private fun generateUserId(): String {
        return "user_${System.currentTimeMillis()}"
    }
    
    /**
     * 清除缓存
     */
    fun clearCache() {
        logOperation("清除缓存")
        userCache.clear()
        lastCacheTime = 0L
    }
    
    /**
     * 获取缓存信息
     */
    fun getCacheInfo(): CacheInfo {
        return CacheInfo(
            size = userCache.size,
            lastUpdateTime = lastCacheTime,
            isValid = isCacheValid()
        )
    }
}

/**
 * 用户统计信息
 */
data class UserStats(
    val totalUsers: Int,
    val activeUsers: Int,
    val lastUpdateTime: Long
)

/**
 * 缓存信息
 */
data class CacheInfo(
    val size: Int,
    val lastUpdateTime: Long,
    val isValid: Boolean
)
