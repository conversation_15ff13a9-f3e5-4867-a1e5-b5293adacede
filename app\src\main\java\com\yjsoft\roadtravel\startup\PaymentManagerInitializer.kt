package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import com.yjsoft.roadtravel.basiclibrary.payment.config.PaymentConfig

/**
 * 支付框架初始化器
 * 使用androidx.startup进行支付框架的初始化
 * 
 * 优先级：低（可延迟初始化，依赖LogManager和Network）
 * 依赖：LogManager, Network
 */
class PaymentManagerInitializer : Initializer<PaymentManager> {

    companion object {
        private const val TAG = "PaymentManagerInitializer"
    }

    /**
     * 创建并初始化支付框架
     * @param context 应用上下文
     * @return PaymentManager实例
     */
    override fun create(context: Context): PaymentManager {
        try {
            // 根据构建类型选择配置
            val isDebug = isDebugBuild(context)

            // 初始化支付管理器
            PaymentManager.init(context, debugMode = isDebug)

            // 配置支付参数（实际使用时需要替换为真实参数）
            if (isDebug) {
                // 测试环境配置
                configureTestPaymentParams()
                LogManager.d(TAG, "支付框架初始化成功（测试模式）")
            } else {
                // 生产环境配置
                configureProductionPaymentParams()
                LogManager.d(TAG, "支付框架初始化成功（生产模式）")
            }

            // 记录支付框架状态
            val enabledTypes = PaymentConfig.getEnabledPaymentTypes()
            LogManager.d(TAG, "支付框架状态 - 已启用支付方式: ${enabledTypes.map { it.name }}")
            
            LogManager.i(TAG, "支付框架通过androidx.startup初始化成功")
            
            return PaymentManager
            
        } catch (e: Exception) {
            LogManager.e(TAG, "支付框架初始化失败", e)
            throw e
        }
    }

    /**
     * 返回此初始化器的依赖项
     * 支付框架依赖LogManager和Network
     * @return 依赖LogManagerInitializer和NetworkInitializer
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return listOf(
            LogManagerInitializer::class.java,
            NetworkInitializer::class.java
        )
    }

    /**
     * 判断是否为调试构建
     */
    private fun isDebugBuild(context: Context): Boolean {
        return try {
            val applicationInfo = context.applicationInfo
            (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 配置测试环境支付参数
     */
    private fun configureTestPaymentParams() {
        // 配置支付宝测试参数（示例参数，实际使用时需要替换）
        PaymentConfig.configureAlipay(
            appId = "2021000000000000", // 测试AppId
            privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...", // 测试私钥
            publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA..." // 测试公钥
        )

        // 配置微信支付测试参数（示例参数，实际使用时需要替换）
        PaymentConfig.configureWeChatPay(
            appId = "wx1234567890abcdef", // 测试AppId
            merchantId = "1234567890", // 测试商户号
            apiKey = "abcdefghijklmnopqrstuvwxyz123456" // 测试API密钥
        )

        // 设置较短的支付超时时间用于测试
        PaymentConfig.setPaymentTimeout(15_000L)
    }

    /**
     * 配置生产环境支付参数
     */
    private fun configureProductionPaymentParams() {
        // TODO: 配置生产环境的真实支付参数
        // 注意：实际的支付参数应该从安全的配置文件或远程配置中获取
        // 不应该硬编码在代码中

        LogManager.w(TAG, "生产环境支付参数未配置，请在实际部署前配置真实的支付参数")
    }
}
