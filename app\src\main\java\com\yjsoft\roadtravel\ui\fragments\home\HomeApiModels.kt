package com.yjsoft.roadtravel.ui.fragments.home

import com.google.gson.annotations.SerializedName

/**
 * 首页接口响应数据模型
 */
data class HomeApiResponse(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("data")
    val data: HomeData? = null,
    
    @SerializedName("msg")
    val msg: String
) {
    /**
     * 判断请求是否成功
     */
    fun isSuccess(): Boolean = code == 0
    
    /**
     * 获取成功的数据，如果失败则返回null
     */
    fun getDataOrNull(): HomeData? = if (isSuccess()) data else null
    
    /**
     * 获取成功的数据，如果失败则抛出异常
     */
    fun getDataOrThrow(): HomeData {
        return if (isSuccess()) {
            data ?: throw Exception("数据为空")
        } else {
            throw Exception(msg)
        }
    }
}

/**
 * 首页数据
 */
data class HomeData(
    @SerializedName("prompts")
    val prompts: List<String> = emptyList(),
    
    @SerializedName("zones")
    val zones: List<Zone> = emptyList(),
    
    @SerializedName("texts")
    val texts: List<Text> = emptyList(),
    
    @SerializedName("activities")
    val activities: List<Any> = emptyList(), // 当前为空数组，使用Any类型
    
    @SerializedName("hot_tags")
    val hotTags: List<String> = emptyList(),
    
    @SerializedName("title_color")
    val titleColor: String = "#1890FF",
    
    @SerializedName("banners")
    val banners: List<Banner> = emptyList()
)

/**
 * 地区数据
 */
data class Zone(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("lng")
    val lng: Double,
    
    @SerializedName("lat")
    val lat: Double
)

/**
 * 文本数据
 */
data class Text(
    @SerializedName("type")
    val type: String,
    
    @SerializedName("title")
    val title: String,
    
    @SerializedName("hots")
    val hots: List<Any> = emptyList(), // 当前为空数组，使用Any类型
    
    @SerializedName("query")
    val query: Query
)

/**
 * 查询参数
 */
data class Query(
    @SerializedName("days")
    val days: String? = null,
    
    @SerializedName("to")
    val to: String? = null,
    
    @SerializedName("tab")
    val tab: String? = null,
    
    @SerializedName("zone_ids")
    val zoneIds: String? = null,
    
    @SerializedName("location")
    val location: String? = null
)

/**
 * Banner数据
 */
data class Banner(
    @SerializedName("id")
    val id: Int,
    
    @SerializedName("title")
    val title: String,
    
    @SerializedName("link")
    val link: String,
    
    @SerializedName("pic")
    val pic: String
) 

/**
 * Plans接口响应数据模型
 */
data class PlansApiResponse(
    @SerializedName("code")
    val code: Int,
    
    @SerializedName("data")
    val data: PlansData? = null,
    
    @SerializedName("msg")
    val msg: String
) {
    /**
     * 判断请求是否成功
     */
    fun isSuccess(): Boolean = code == 0
    
    /**
     * 获取成功的数据，如果失败则返回null
     */
    fun getDataOrNull(): PlansData? = if (isSuccess()) data else null
    
    /**
     * 获取成功的数据，如果失败则抛出异常
     */
    fun getDataOrThrow(): PlansData {
        return if (isSuccess()) {
            data ?: throw Exception("数据为空")
        } else {
            throw Exception(msg)
        }
    }
}

/**
 * Plans数据
 */
data class PlansData(
    @SerializedName("title")
    val title: String,
    
    @SerializedName("color")
    val color: String,
    
    @SerializedName("list")
    val list: List<PlanItem> = emptyList()
)

/**
 * 计划项目
 */
data class PlanItem(
    @SerializedName("ai_reqid")
    val aiReqid: String,
    
    @SerializedName("subject")
    val subject: String,
    
    @SerializedName("subtitle")
    val subtitle: String,
    
    @SerializedName("tags")
    val tags: List<String> = emptyList(),
    
    @SerializedName("cost_avg")
    val costAvg: Double,
    
    @SerializedName("cost_avg1")
    val costAvg1: Long,
    
    @SerializedName("plan_id")
    val planId: Int,
    
    @SerializedName("is_own")
    val isOwn: Boolean
) 