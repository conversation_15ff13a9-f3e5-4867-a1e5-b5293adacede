package com.yjsoft.roadtravel.basiclibrary.mvvm.utils

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.CreationExtras
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlin.reflect.KClass

/**
 * MVVM工具类
 * 
 * 功能：
 * - ViewModel创建工具
 * - 状态合并工具
 * - 数据转换工具
 * - 调试工具
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
object MVVMUtils {
    
    private const val TAG = "MVVMUtils"
    
    // ========== ViewModel工具 ==========
    
    /**
     * 创建ViewModel工厂
     */
    inline fun <reified VM : ViewModel> createViewModelFactory(
        crossinline create: () -> VM
    ): ViewModelProvider.Factory {
        return object : ViewModelProvider.Factory {
            @Suppress("UNCHECKED_CAST")
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return create() as T
            }
        }
    }
    
    /**
     * 创建带参数的ViewModel工厂
     */
    inline fun <reified VM : ViewModel, P> createViewModelFactoryWithParam(
        param: P,
        crossinline create: (P) -> VM
    ): ViewModelProvider.Factory {
        return object : ViewModelProvider.Factory {
            @Suppress("UNCHECKED_CAST")
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                return create(param) as T
            }
        }
    }
    
    // ========== 状态合并工具 ==========
    
    /**
     * 合并两个UiState
     */
    fun <T1, T2, R> combineUiStates(
        state1: UiState<T1>,
        state2: UiState<T2>,
        transform: (T1, T2) -> R
    ): UiState<R> {
        return when {
            state1 is UiState.Loading || state2 is UiState.Loading -> {
                UiState.Loading("加载中...")
            }
            state1 is UiState.Error -> state1
            state2 is UiState.Error -> state2
            state1 is UiState.Success && state2 is UiState.Success -> {
                try {
                    UiState.Success(transform(state1.data, state2.data))
                } catch (e: Exception) {
                    UiState.Error(e, "数据合并失败")
                }
            }
            state1 is UiState.Empty -> state1
            state2 is UiState.Empty -> state2
            else -> UiState.Idle
        }
    }
    
    /**
     * 合并多个UiState Flow
     */
    fun <T1, T2, R> combineUiStateFlows(
        flow1: Flow<UiState<T1>>,
        flow2: Flow<UiState<T2>>,
        transform: (T1, T2) -> R
    ): Flow<UiState<R>> {
        return combine(flow1, flow2) { state1, state2 ->
            combineUiStates(state1, state2, transform)
        }
    }
    
    /**
     * 合并三个UiState Flow
     */
    fun <T1, T2, T3, R> combineUiStateFlows(
        flow1: Flow<UiState<T1>>,
        flow2: Flow<UiState<T2>>,
        flow3: Flow<UiState<T3>>,
        transform: (T1, T2, T3) -> R
    ): Flow<UiState<R>> {
        return combine(flow1, flow2, flow3) { state1, state2, state3 ->
            when {
                state1 is UiState.Loading || state2 is UiState.Loading || state3 is UiState.Loading -> {
                    UiState.Loading("加载中...")
                }
                state1 is UiState.Error -> state1
                state2 is UiState.Error -> state2
                state3 is UiState.Error -> state3
                state1 is UiState.Success && state2 is UiState.Success && state3 is UiState.Success -> {
                    try {
                        UiState.Success(transform(state1.data, state2.data, state3.data))
                    } catch (e: Exception) {
                        UiState.Error(e, "数据合并失败")
                    }
                }
                else -> UiState.Idle
            }
        }
    }
    
    // ========== 数据转换工具 ==========
    
    /**
     * 将列表转换为分页数据
     */
    fun <T> List<T>.toPaged(pageSize: Int, page: Int = 0): List<T> {
        val startIndex = page * pageSize
        val endIndex = minOf(startIndex + pageSize, size)
        return if (startIndex < size) {
            subList(startIndex, endIndex)
        } else {
            emptyList()
        }
    }
    
    /**
     * 检查列表是否有更多数据
     */
    fun <T> List<T>.hasMore(pageSize: Int, currentPage: Int): Boolean {
        return size > (currentPage + 1) * pageSize
    }
    
    /**
     * 安全的数据转换
     */
    inline fun <T, R> T?.safeTransform(transform: (T) -> R): R? {
        val innerTAG  = "MVVMUtils"
        return try {
            this?.let(transform)
        } catch (e: Exception) {
            LogManager.e(e, "[%s] 数据转换失败", innerTAG)
            null
        }
    }
    
    /**
     * 批量数据转换
     */
    inline fun <T, R> List<T>.safeBatchTransform(transform: (T) -> R): List<R> {
        val innerTAG  = "MVVMUtils"
        return mapNotNull { item ->
            try {
                transform(item)
            } catch (e: Exception) {
                LogManager.e(e, "[%s] 批量转换项失败", innerTAG)
                null
            }
        }
    }
    
    // ========== 缓存工具 ==========
    
    /**
     * 简单的内存缓存
     */
    class SimpleCache<K, V>(private val maxSize: Int = 100) {
        private val cache = LinkedHashMap<K, V>(maxSize, 0.75f, true)
        
        fun get(key: K): V? = cache[key]
        
        fun put(key: K, value: V): V? {
            if (cache.size >= maxSize) {
                val firstKey = cache.keys.first()
                cache.remove(firstKey)
            }
            return cache.put(key, value)
        }
        
        fun remove(key: K): V? = cache.remove(key)
        
        fun clear() = cache.clear()
        
        fun size(): Int = cache.size
        
        fun containsKey(key: K): Boolean = cache.containsKey(key)
    }
    
    // ========== 调试工具 ==========
    
    /**
     * 记录UiState变化
     */
    fun <T> logUiStateChange(tag: String, oldState: UiState<T>, newState: UiState<T>) {
        if (oldState != newState) {
            LogManager.d("[%s] UiState变化: %s -> %s", tag, 
                oldState::class.simpleName, 
                newState::class.simpleName
            )
        }
    }
    
    /**
     * 记录Resource变化
     */
    fun <T> logResourceChange(tag: String, oldResource: Resource<T>, newResource: Resource<T>) {
        if (oldResource != newResource) {
            LogManager.d("[%s] Resource变化: %s -> %s", tag,
                oldResource::class.simpleName,
                newResource::class.simpleName
            )
        }
    }
    
    /**
     * 性能监控
     */
    inline fun <T> measureTime(tag: String, operation: String, block: () -> T): T {
        val startTime = System.currentTimeMillis()
        return try {
            block()
        } finally {
            val endTime = System.currentTimeMillis()
            LogManager.d("[%s] %s 耗时: %dms", tag, operation, endTime - startTime)
        }
    }
    
    // ========== 验证工具 ==========
    
    /**
     * 验证ViewModel状态
     */
    fun validateViewModelState(viewModel: ViewModel, tag: String = "ViewModel") {
        try {
            // 检查ViewModel是否处于有效状态
            val isActive = viewModel.javaClass.getDeclaredMethod("isCleared").invoke(viewModel) as Boolean
            if (isActive) {
                LogManager.w("[%s] ViewModel已被清理", tag)
            } else {
                LogManager.d("[%s] ViewModel状态正常", tag)
            }
        } catch (e: Exception) {
            LogManager.d("[%s] ViewModel状态检查失败: %s", tag, e.message)
        }
    }
    
    /**
     * 检查数据完整性
     */
    fun <T> validateData(data: T?, tag: String = "Data"): Boolean {
        return when {
            data == null -> {
                LogManager.w("[%s] 数据为null", tag)
                false
            }
            data is Collection<*> && data.isEmpty() -> {
                LogManager.w("[%s] 集合数据为空", tag)
                false
            }
            data is String && data.isBlank() -> {
                LogManager.w("[%s] 字符串数据为空", tag)
                false
            }
            else -> {
                LogManager.d("[%s] 数据验证通过", tag)
                true
            }
        }
    }
    
    // ========== 错误处理工具 ==========
    
    /**
     * 创建标准错误UiState
     */
    fun <T> createErrorState(
        exception: Throwable? = null,
        message: String = "操作失败",
        code: Int = -1
    ): UiState<T> {
        return UiState.Error(exception, message, code)
    }
    
    /**
     * 创建标准错误Resource
     */
    fun <T> createErrorResource(
        exception: Throwable? = null,
        message: String = "操作失败",
        code: Int = -1
    ): Resource<T> {
        return Resource.Error(exception, message, code)
    }
    
    /**
     * 统一的异常消息处理
     */
    fun getErrorMessage(exception: Throwable?): String {
        return when (exception) {
            is java.net.UnknownHostException -> "网络连接失败"
            is java.net.SocketTimeoutException -> "网络请求超时"
            is java.net.ConnectException -> "无法连接到服务器"
            is retrofit2.HttpException -> {
                when (exception.code()) {
                    400 -> "请求参数错误"
                    401 -> "未授权访问"
                    403 -> "禁止访问"
                    404 -> "请求的资源不存在"
                    500 -> "服务器内部错误"
                    else -> "网络请求失败"
                }
            }
            else -> exception?.message ?: "未知错误"
        }
    }
}
