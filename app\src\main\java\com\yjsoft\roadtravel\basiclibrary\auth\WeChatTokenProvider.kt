package com.yjsoft.roadtravel.basiclibrary.auth

import android.content.Context

import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenInfo
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenProvider
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

/**
 * 微信Token提供者
 *
 * 实现TokenProvider接口，负责处理认证失败的处理
 *
 * 功能：
 * - 认证失败处理
 * - 错误日志记录
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
class WeChatTokenProvider(
    private val context: Context
) : TokenProvider {
    
    private val TAG = "WeChatTokenProvider"
    

    

    
    /**
     * 认证失败回调
     * 
     * 当token刷新失败或认证失败时调用
     * 负责清除本地认证信息并跳转到登录页
     */
    @OptIn(DelicateCoroutinesApi::class)
    override fun onAuthFailure() {
        try {
            LogManager.w(TAG, "微信认证失败，清除本地认证信息")
            
            // 清除本地token信息
            // 注意：这里不能直接调用suspend函数，需要在协程中执行
            GlobalScope.launch(Dispatchers.IO) {
                try {
                    TokenManager.clearTokens()
                    LogManager.d(TAG, "本地认证信息清除成功")
                } catch (e: Exception) {
                    LogManager.e(TAG, "清除本地认证信息失败", e)
                }
            }
            
            // TODO: 跳转到登录页面
            // 这里应该通过某种方式通知UI层跳转到登录页
            // 可以考虑使用EventBus、LiveData或其他事件机制
            LogManager.w(TAG, "需要跳转到登录页面")
            
        } catch (e: Exception) {
            LogManager.e(TAG, "处理认证失败异常", e)
        }
    }
    
    /**
     * 检查当前token是否有效
     * 
     * @return true表示token有效，false表示需要刷新或重新登录
     */
    fun isTokenValid(): Boolean {
        return try {
            if (!TokenManager.isInitialized()) {
                TokenManager.initialize(context)
            }
            
            val accessToken = TokenManager.getAccessToken()
            val isExpired = TokenManager.isTokenExpired()
            
            accessToken.isNotEmpty() && !isExpired
        } catch (e: Exception) {
            LogManager.e(TAG, "检查token有效性失败", e)
            false
        }
    }
    
    /**
     * 获取当前token摘要信息（用于调试）
     */
    fun getTokenSummary(): String {
        return try {
            if (!TokenManager.isInitialized()) {
                TokenManager.initialize(context)
            }
            
            TokenManager.getTokenSummary()
        } catch (e: Exception) {
            "获取token摘要失败: ${e.message}"
        }
    }
} 