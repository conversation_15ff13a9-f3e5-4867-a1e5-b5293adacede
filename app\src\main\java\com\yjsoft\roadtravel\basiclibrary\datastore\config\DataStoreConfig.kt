package com.yjsoft.roadtravel.basiclibrary.datastore.config

/**
 * DataStore配置类
 * 统一管理DataStore相关的配置参数
 */
object DataStoreConfig {
    
    /**
     * 默认DataStore名称
     */
    const val DEFAULT_DATASTORE_NAME = "road_travel_preferences"
    
    /**
     * 数据迁移配置
     */
    object Migration {
        /**
         * 是否启用数据迁移
         */
        const val ENABLED = true
        
        /**
         * SharedPreferences文件名（用于迁移）
         */
        const val SHARED_PREFERENCES_NAME = "road_travel_prefs"
        
        /**
         * 迁移完成后是否删除SharedPreferences
         */
        const val DELETE_SHARED_PREFS_AFTER_MIGRATION = true
    }
    
    /**
     * 性能配置
     */
    object Performance {
        /**
         * 是否启用内存缓存
         */
        const val ENABLE_MEMORY_CACHE = true
        
        /**
         * 缓存过期时间（毫秒）
         */
        const val CACHE_EXPIRY_MS = 5 * 60 * 1000L // 5分钟
        
        /**
         * 最大缓存大小
         */
        const val MAX_CACHE_SIZE = 100
    }
    
    /**
     * 调试配置
     */
    object Debug {
        /**
         * 是否启用详细日志
         */
        const val ENABLE_VERBOSE_LOGGING = false
        
        /**
         * 是否在调试模式下验证数据完整性
         */
        const val VALIDATE_DATA_INTEGRITY = true
        
        /**
         * 是否记录性能指标
         */
        const val LOG_PERFORMANCE_METRICS = false
    }
    
    /**
     * 错误处理配置
     */
    object ErrorHandling {
        /**
         * 最大重试次数
         */
        const val MAX_RETRY_COUNT = 3
        
        /**
         * 重试延迟（毫秒）
         */
        const val RETRY_DELAY_MS = 1000L
        
        /**
         * 是否在错误时使用默认值
         */
        const val USE_DEFAULT_ON_ERROR = true
        
        /**
         * 是否记录错误到崩溃报告
         */
        const val REPORT_ERRORS_TO_CRASHLYTICS = true
    }
    
    /**
     * 数据验证配置
     */
    object Validation {
        /**
         * 字符串最大长度
         */
        const val MAX_STRING_LENGTH = 10000
        
        /**
         * 集合最大大小
         */
        const val MAX_SET_SIZE = 1000
        
        /**
         * 是否启用数据类型验证
         */
        const val ENABLE_TYPE_VALIDATION = true
        
        /**
         * 是否启用值范围验证
         */
        const val ENABLE_VALUE_RANGE_VALIDATION = true
    }
    
    /**
     * 备份和恢复配置
     */
    object Backup {
        /**
         * 是否启用自动备份
         */
        const val ENABLE_AUTO_BACKUP = false
        
        /**
         * 备份间隔（小时）
         */
        const val BACKUP_INTERVAL_HOURS = 24
        
        /**
         * 最大备份文件数量
         */
        const val MAX_BACKUP_FILES = 7
        
        /**
         * 备份文件前缀
         */
        const val BACKUP_FILE_PREFIX = "datastore_backup_"
        
        /**
         * 备份文件扩展名
         */
        const val BACKUP_FILE_EXTENSION = ".json"
    }
    
    /**
     * 安全配置
     */
    object Security {
        /**
         * 是否启用数据加密
         */
        const val ENABLE_ENCRYPTION = false
        
        /**
         * 加密算法
         */
        const val ENCRYPTION_ALGORITHM = "AES/GCM/NoPadding"
        
        /**
         * 密钥长度
         */
        const val KEY_LENGTH = 256
        
        /**
         * 是否对敏感数据进行混淆
         */
        const val OBFUSCATE_SENSITIVE_DATA = true
    }
    
    /**
     * 监控配置
     */
    object Monitoring {
        /**
         * 是否启用性能监控
         */
        const val ENABLE_PERFORMANCE_MONITORING = false
        
        /**
         * 是否启用使用统计
         */
        const val ENABLE_USAGE_ANALYTICS = false
        
        /**
         * 监控数据上报间隔（分钟）
         */
        const val MONITORING_REPORT_INTERVAL_MINUTES = 60
        
        /**
         * 是否记录数据访问模式
         */
        const val LOG_ACCESS_PATTERNS = false
    }
    
    /**
     * 获取当前环境的配置
     */
    fun getCurrentConfig(): Map<String, Any> {
        return mapOf(
            "datastore_name" to DEFAULT_DATASTORE_NAME,
            "migration_enabled" to Migration.ENABLED,
            "cache_enabled" to Performance.ENABLE_MEMORY_CACHE,
            "cache_expiry_ms" to Performance.CACHE_EXPIRY_MS,
            "max_retry_count" to ErrorHandling.MAX_RETRY_COUNT,
            "encryption_enabled" to Security.ENABLE_ENCRYPTION,
            "backup_enabled" to Backup.ENABLE_AUTO_BACKUP,
            "monitoring_enabled" to Monitoring.ENABLE_PERFORMANCE_MONITORING
        )
    }
    
    /**
     * 验证配置的有效性
     */
    fun validateConfig(): Boolean {
        return try {
            // 验证基本配置
            require(DEFAULT_DATASTORE_NAME.isNotBlank()) { "DataStore名称不能为空" }
            require(Performance.CACHE_EXPIRY_MS > 0) { "缓存过期时间必须大于0" }
            require(Performance.MAX_CACHE_SIZE > 0) { "最大缓存大小必须大于0" }
            require(ErrorHandling.MAX_RETRY_COUNT >= 0) { "最大重试次数不能为负数" }
            require(ErrorHandling.RETRY_DELAY_MS > 0) { "重试延迟必须大于0" }
            require(Validation.MAX_STRING_LENGTH > 0) { "字符串最大长度必须大于0" }
            require(Validation.MAX_SET_SIZE > 0) { "集合最大大小必须大于0" }
            require(Backup.BACKUP_INTERVAL_HOURS > 0) { "备份间隔必须大于0" }
            require(Backup.MAX_BACKUP_FILES > 0) { "最大备份文件数量必须大于0" }
            require(Security.KEY_LENGTH > 0) { "密钥长度必须大于0" }
            require(Monitoring.MONITORING_REPORT_INTERVAL_MINUTES > 0) { "监控上报间隔必须大于0" }
            
            true
        } catch (e: IllegalArgumentException) {
            false
        }
    }
}
