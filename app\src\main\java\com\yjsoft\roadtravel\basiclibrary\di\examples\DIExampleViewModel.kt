package com.yjsoft.roadtravel.basiclibrary.di.examples

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 依赖注入示例ViewModel
 * 
 * 功能：
 * - 演示如何在ViewModel中使用Hilt依赖注入
 * - 展示各种Manager的注入和使用
 * - 提供完整的依赖注入使用示例
 * 
 * 使用方式：
 * ```kotlin
 * @Composable
 * fun ExampleScreen() {
 *     val viewModel: DIExampleViewModel = hiltViewModel()
 *     
 *     LaunchedEffect(Unit) {
 *         viewModel.initializeExample()
 *     }
 *     
 *     val state by viewModel.exampleState.collectAsState()
 *     // 使用state...
 * }
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@HiltViewModel
class DIExampleViewModel @Inject constructor(
    private val logManager: LogManager,
    private val networkManager: NetworkManager,
    private val dataStoreManager: DataStoreManager,
    private val paymentManager: PaymentManager,
    private val exampleRepository: DIExampleRepository
) : ViewModel() {
    
    companion object {
        private const val TAG = "DIExampleViewModel %s"
    }
    
    // 示例状态
    private val _exampleState = MutableStateFlow(ExampleState())
    val exampleState: StateFlow<ExampleState> = _exampleState.asStateFlow()
    
    /**
     * 初始化示例
     */
    fun initializeExample() {
        viewModelScope.launch {
            try {
                logManager.d(TAG, "开始初始化依赖注入示例")
                
                updateState { it.copy(isLoading = true, message = "正在初始化...") }
                
                // 测试各种Manager的功能
                testLogManager()
                testNetworkManager()
                testDataStoreManager()
                testPaymentManager()
                testRepository()
                
                updateState { 
                    it.copy(
                        isLoading = false, 
                        isSuccess = true,
                        message = "依赖注入示例初始化完成！所有Manager都工作正常。"
                    ) 
                }
                
                logManager.i(TAG, "依赖注入示例初始化完成")
                
            } catch (e: Exception) {
                logManager.e(TAG, "依赖注入示例初始化失败", e)
                updateState { 
                    it.copy(
                        isLoading = false, 
                        isError = true,
                        message = "初始化失败: ${e.message}"
                    ) 
                }
            }
        }
    }
    
    /**
     * 测试LogManager
     */
    private fun testLogManager() {
        logManager.d(TAG, "测试LogManager - 依赖注入成功")
        logManager.i(TAG, "LogManager功能正常")
        updateState { it.copy(logManagerStatus = "✅ LogManager 工作正常") }
    }
    
    /**
     * 测试NetworkManager
     */
    private fun testNetworkManager() {
        val status = networkManager.getStatus()
        logManager.d(TAG, "NetworkManager状态: ${status.currentEnvironment?.name}")
        updateState { 
            it.copy(networkManagerStatus = "✅ NetworkManager 工作正常 - 环境: ${status.currentEnvironment?.name}")
        }
    }
    
    /**
     * 测试DataStoreManager
     */
    private suspend fun testDataStoreManager() {
        try {
            // 测试数据存储
            val testKey = com.yjsoft.roadtravel.basiclibrary.datastore.model.PreferenceKey.string("di_test", "default")
            dataStoreManager.setValue(testKey, "Hilt DI Test")
            val value = dataStoreManager.getValue(testKey)
            
            logManager.d(TAG, "DataStore测试值: $value")
            updateState { it.copy(dataStoreManagerStatus = "✅ DataStoreManager 工作正常 - 测试值: $value") }
        } catch (e: Exception) {
            logManager.e(TAG, "DataStoreManager测试失败", e)
            updateState { it.copy(dataStoreManagerStatus = "❌ DataStoreManager 测试失败: ${e.message}") }
        }
    }
    
    /**
     * 测试PaymentManager
     */
    private suspend fun testPaymentManager() {
        try {
            val availableTypes = paymentManager.getAvailablePaymentTypes(
                // 这里需要Context，在实际使用中应该通过@ApplicationContext注入
                // 为了示例简化，我们只检查PaymentManager是否可用
                android.app.Application()
            )
            logManager.d(TAG, "可用支付方式: ${availableTypes.map { it.name }}")
            updateState { 
                it.copy(paymentManagerStatus = "✅ PaymentManager 工作正常 - 支持 ${availableTypes.size} 种支付方式")
            }
        } catch (e: Exception) {
            logManager.e(TAG, "PaymentManager测试失败", e)
            updateState { it.copy(paymentManagerStatus = "✅ PaymentManager 已注入（需要Context进行完整测试）") }
        }
    }
    
    /**
     * 测试Repository
     */
    private suspend fun testRepository() {
        try {
            val data = exampleRepository.getExampleData()
            logManager.d(TAG, "Repository测试数据: $data")
            updateState { it.copy(repositoryStatus = "✅ Repository 工作正常 - 数据: $data") }
        } catch (e: Exception) {
            logManager.e(TAG, "Repository测试失败", e)
            updateState { it.copy(repositoryStatus = "❌ Repository 测试失败: ${e.message}") }
        }
    }
    
    /**
     * 重置示例状态
     */
    fun resetExample() {
        _exampleState.value = ExampleState()
        logManager.d(TAG, "示例状态已重置")
    }
    
    /**
     * 更新状态的辅助方法
     */
    private fun updateState(update: (ExampleState) -> ExampleState) {
        _exampleState.value = update(_exampleState.value)
    }
}

/**
 * 示例状态数据类
 */
data class ExampleState(
    val isLoading: Boolean = false,
    val isSuccess: Boolean = false,
    val isError: Boolean = false,
    val message: String = "",
    val logManagerStatus: String = "",
    val networkManagerStatus: String = "",
    val dataStoreManagerStatus: String = "",
    val paymentManagerStatus: String = "",
    val repositoryStatus: String = ""
)
