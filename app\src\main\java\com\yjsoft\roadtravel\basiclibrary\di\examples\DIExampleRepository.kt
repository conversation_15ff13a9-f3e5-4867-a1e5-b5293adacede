package com.yjsoft.roadtravel.basiclibrary.di.examples

import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.ApiService
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 依赖注入示例Repository
 * 
 * 功能：
 * - 演示如何在Repository中使用Hilt依赖注入
 * - 展示Repository层的依赖管理
 * - 提供数据访问层的依赖注入示例
 * 
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 依赖注入管理外部依赖
 * - 清晰的数据访问接口
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Singleton
class DIExampleRepository @Inject constructor(
    private val logManager: LogManager,
    private val dataStoreRepository: DataStoreRepository,
    private val apiService: ApiService
) {
    
    companion object {
        private const val TAG = "DIExampleRepository %s"
    }
    
    /**
     * 获取示例数据
     * 演示Repository如何使用注入的依赖
     */
    suspend fun getExampleData(): String {
        logManager.d(TAG, "开始获取示例数据")
        
        return try {
            // 模拟数据获取过程
            delay(1000) // 模拟网络延迟
            
            // 使用DataStoreRepository获取本地数据
            val localData = getLocalExampleData()
            
            // 使用ApiService获取远程数据（模拟）
            val remoteData = getRemoteExampleData()
            
            val result = "本地数据: $localData, 远程数据: $remoteData"
            logManager.d(TAG, "示例数据获取成功: $result")
            
            result
        } catch (e: Exception) {
            logManager.e(TAG, "获取示例数据失败", e)
            throw e
        }
    }
    
    /**
     * 获取本地示例数据
     */
    private suspend fun getLocalExampleData(): String {
        return try {
            // 使用DataStoreRepository获取数据
            val userId = dataStoreRepository.getUserId()
            val userName = dataStoreRepository.getUserName()
            
            if (userId.isNotEmpty() && userName.isNotEmpty()) {
                "用户ID: $userId, 用户名: $userName"
            } else {
                "无本地用户数据"
            }
        } catch (e: Exception) {
            logManager.w(TAG, "获取本地数据失败，使用默认值", e)
            "默认本地数据"
        }
    }
    
    /**
     * 获取远程示例数据
     */
    private suspend fun getRemoteExampleData(): String {
        return try {
            // 这里可以使用ApiService进行网络请求
            // 为了示例简化，我们只是模拟一个响应
            logManager.d(TAG, "模拟远程数据请求")
            
            // 模拟网络请求
            delay(500)
            
            "远程服务器时间: ${System.currentTimeMillis()}"
        } catch (e: Exception) {
            logManager.w(TAG, "获取远程数据失败，使用默认值", e)
            "默认远程数据"
        }
    }
    
    /**
     * 保存示例数据
     */
    suspend fun saveExampleData(data: String): Boolean {
        return try {
            logManager.d(TAG, "保存示例数据: $data")
            
            // 使用DataStoreRepository保存数据
            dataStoreRepository.setLastKnownCity(data)
            
            logManager.d(TAG, "示例数据保存成功")
            true
        } catch (e: Exception) {
            logManager.e(TAG, "保存示例数据失败", e)
            false
        }
    }
    
    /**
     * 清除示例数据
     */
    suspend fun clearExampleData(): Boolean {
        return try {
            logManager.d(TAG, "清除示例数据")
            
            // 使用DataStoreRepository清除数据
            dataStoreRepository.logout()
            
            logManager.d(TAG, "示例数据清除成功")
            true
        } catch (e: Exception) {
            logManager.e(TAG, "清除示例数据失败", e)
            false
        }
    }
    
    /**
     * 检查Repository状态
     */
    fun getRepositoryStatus(): Map<String, Any> {
        return mapOf(
            "logManagerAvailable" to (logManager != null),
            "dataStoreRepositoryAvailable" to (dataStoreRepository != null),
            "apiServiceAvailable" to (apiService != null),
            "repositoryInitialized" to true
        )
    }
    
    /**
     * 获取依赖信息
     */
    fun getDependencyInfo(): String {
        val status = getRepositoryStatus()
        return buildString {
            append("Repository依赖状态:\n")
            status.forEach { (key, value) ->
                append("- $key: $value\n")
            }
        }
    }
}
