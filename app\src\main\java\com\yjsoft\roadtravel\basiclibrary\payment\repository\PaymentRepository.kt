package com.yjsoft.roadtravel.basiclibrary.payment.repository

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.RetrofitInstance
import com.yjsoft.roadtravel.basiclibrary.network.models.ApiResponse
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentRequest
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.utils.PaymentUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

/**
 * 支付数据仓库
 * 负责处理支付相关的数据操作和网络请求
 */
class PaymentRepository private constructor() {
    
    companion object {
        private const val TAG = "PaymentRepository %s"
        
        @Volatile
        private var INSTANCE: PaymentRepository? = null
        
        fun getInstance(): PaymentRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PaymentRepository().also { INSTANCE = it }
            }
        }
    }
    
    private var paymentApiService: PaymentApiService? = null
    
    /**
     * 初始化仓库
     */
    fun init(context: Context) {
        try {
            // 创建专用的支付API服务实例
            val retrofitInstance = RetrofitInstance.getInstance(context)
            paymentApiService = retrofitInstance.createService(PaymentApiService::class.java)

            LogManager.d(TAG, "支付仓库初始化完成")
        } catch (e: Exception) {
            LogManager.e(TAG, "支付仓库初始化失败", e)
            // 在演示模式下，我们可以使用模拟实现
            paymentApiService = createMockPaymentApiService()
            LogManager.w(TAG, "使用模拟支付API服务")
        }
    }
    
    /**
     * 创建支付订单
     */
    suspend fun createPaymentOrder(
        paymentType: PaymentType,
        request: PaymentRequest
    ): Result<PaymentOrderResponse> = withContext(Dispatchers.IO) {
        return@withContext try {
            val apiService = getApiService()
            
            val createRequest = CreatePaymentOrderRequest(
                orderId = request.orderId,
                amount = request.getAmountString(),
                title = request.title,
                description = request.description,
                paymentType = paymentType.name,
                notifyUrl = request.notifyUrl,
                returnUrl = request.returnUrl,
                extraParams = request.extraParams
            )
            
            val response = apiService.createPaymentOrder(createRequest)
            
            if (response.isSuccess() && response.data != null) {
                LogManager.d(TAG, "创建支付订单成功: ${request.orderId}")
                Result.success(response.data)
            } else {
                val errorMsg = response.message
                LogManager.e(TAG, "创建支付订单失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
            
        } catch (e: Exception) {
            LogManager.e(TAG, "创建支付订单异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 查询支付订单状态
     */
    suspend fun getPaymentOrderStatus(orderId: String): Result<PaymentOrderStatusResponse> = withContext(Dispatchers.IO) {
        return@withContext try {
            val apiService = getApiService()
            val response = apiService.getPaymentOrderStatus(orderId)
            
            if (response.isSuccess() && response.data != null) {
                LogManager.d(TAG, "查询订单状态成功: $orderId")
                Result.success(response.data)
            } else {
                val errorMsg = response.message
                LogManager.e(TAG, "查询订单状态失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
            
        } catch (e: Exception) {
            LogManager.e(TAG, "查询订单状态异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 通知支付结果
     */
    suspend fun notifyPaymentResult(
        orderId: String,
        result: PaymentResult
    ): Result<PaymentNotifyResponse> = withContext(Dispatchers.IO) {
        return@withContext try {
            val apiService = getApiService()
            
            val notifyRequest = when (result) {
                is PaymentResult.Success -> PaymentNotifyRequest(
                    orderId = orderId,
                    transactionId = result.transactionId,
                    amount = result.amount,
                    paymentType = result.paymentType.name,
                    status = "SUCCESS",
                    payTime = result.timestamp,
                    extraData = result.extraData
                )
                is PaymentResult.Cancel -> PaymentNotifyRequest(
                    orderId = orderId,
                    transactionId = "",
                    amount = "",
                    paymentType = result.paymentType.name,
                    status = "CANCELLED",
                    payTime = System.currentTimeMillis()
                )
                is PaymentResult.Error -> PaymentNotifyRequest(
                    orderId = orderId,
                    transactionId = "",
                    amount = "",
                    paymentType = result.paymentType.name,
                    status = "FAILED",
                    payTime = System.currentTimeMillis(),
                    extraData = mapOf("errorCode" to result.errorCode, "errorMessage" to result.errorMessage)
                )
                else -> throw IllegalArgumentException("不支持的支付结果类型")
            }
            
            val response = apiService.notifyPaymentResult(notifyRequest)
            
            if (response.isSuccess() && response.data != null) {
                LogManager.d(TAG, "支付结果通知成功: $orderId")
                Result.success(response.data)
            } else {
                val errorMsg = response.message
                LogManager.e(TAG, "支付结果通知失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
            
        } catch (e: Exception) {
            LogManager.e(TAG, "支付结果通知异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 申请退款
     */
    suspend fun requestRefund(
        orderId: String,
        refundId: String,
        refundAmount: String,
        refundReason: String,
        notifyUrl: String? = null
    ): Result<RefundResponse> = withContext(Dispatchers.IO) {
        return@withContext try {
            val apiService = getApiService()
            
            val refundRequest = RefundRequest(
                orderId = orderId,
                refundId = refundId,
                refundAmount = refundAmount,
                refundReason = refundReason,
                notifyUrl = notifyUrl
            )
            
            val response = apiService.requestRefund(refundRequest)
            
            if (response.isSuccess() && response.data != null) {
                LogManager.d(TAG, "申请退款成功: $refundId")
                Result.success(response.data)
            } else {
                val errorMsg = response.message
                LogManager.e(TAG, "申请退款失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
            
        } catch (e: Exception) {
            LogManager.e(TAG, "申请退款异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 查询退款状态
     */
    suspend fun getRefundStatus(refundId: String): Result<RefundStatusResponse> = withContext(Dispatchers.IO) {
        return@withContext try {
            val apiService = getApiService()
            val response = apiService.getRefundStatus(refundId)
            
            if (response.isSuccess() && response.data != null) {
                LogManager.d(TAG, "查询退款状态成功: $refundId")
                Result.success(response.data)
            } else {
                val errorMsg = response.message
                LogManager.e(TAG, "查询退款状态失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
            
        } catch (e: Exception) {
            LogManager.e(TAG, "查询退款状态异常", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取API服务实例
     */
    private fun getApiService(): PaymentApiService {
        return paymentApiService ?: throw IllegalStateException("PaymentRepository未初始化")
    }
    
    /**
     * 创建模拟的支付API服务
     */
    private fun createMockPaymentApiService(): PaymentApiService {
        return object : PaymentApiService {
            override suspend fun createPaymentOrder(request: CreatePaymentOrderRequest): ApiResponse<PaymentOrderResponse> {
                // 模拟网络延迟
                delay(1000)

                // 生成模拟的支付参数
                val mockPaymentParams = when (request.paymentType) {
                    "ALIPAY" -> generateMockAlipayParams(request)
                    "WECHAT_PAY" -> generateMockWeChatParams(request)
                    else -> "{\"mock\": true}"
                }

                return ApiResponse.success(
                    PaymentOrderResponse(
                        orderId = request.orderId,
                        paymentParams = mockPaymentParams,
                        expireTime = System.currentTimeMillis() + 30 * 60 * 1000, // 30分钟后过期
                        paymentType = request.paymentType
                    )
                )
            }

            override suspend fun getPaymentOrderStatus(orderId: String): ApiResponse<PaymentOrderStatusResponse> {
                delay(500)
                return ApiResponse.success(
                    PaymentOrderStatusResponse(
                        orderId = orderId,
                        status = "PENDING",
                        amount = "0.01",
                        paymentType = "MOCK"
                    )
                )
            }

            override suspend fun notifyPaymentResult(request: PaymentNotifyRequest): ApiResponse<PaymentNotifyResponse> {
                delay(300)
                return ApiResponse.success(
                    PaymentNotifyResponse(
                        success = true,
                        message = "通知成功"
                    )
                )
            }

            override suspend fun requestRefund(request: RefundRequest): ApiResponse<RefundResponse> {
                delay(1000)
                return ApiResponse.success(
                    RefundResponse(
                        refundId = request.refundId,
                        status = "SUCCESS",
                        refundAmount = request.refundAmount,
                        refundTime = System.currentTimeMillis()
                    )
                )
            }

            override suspend fun getRefundStatus(refundId: String): ApiResponse<RefundStatusResponse> {
                delay(500)
                return ApiResponse.success(
                    RefundStatusResponse(
                        refundId = refundId,
                        orderId = "MOCK_ORDER",
                        status = "SUCCESS",
                        refundAmount = "0.01"
                    )
                )
            }
        }
    }

    /**
     * 生成模拟的支付宝支付参数
     */
    private fun generateMockAlipayParams(request: CreatePaymentOrderRequest): String {
        val params = mapOf(
            "app_id" to "2021000000000000",
            "method" to "alipay.trade.app.pay",
            "charset" to "UTF-8",
            "sign_type" to "RSA2",
            "timestamp" to PaymentUtils.getCurrentTimestamp(),
            "version" to "1.0",
            "biz_content" to PaymentUtils.toJsonString(mapOf(
                "out_trade_no" to request.orderId,
                "total_amount" to request.amount,
                "subject" to request.title,
                "product_code" to "QUICK_MSECURITY_PAY"
            )),
            "sign" to "mock_signature_${PaymentUtils.generateNonceStr(16)}"
        )

        return params.map { "${it.key}=${it.value}" }.joinToString("&")
    }

    /**
     * 生成模拟的微信支付参数
     */
    private fun generateMockWeChatParams(request: CreatePaymentOrderRequest): String {
        val params = mapOf(
            "appId" to "wx1234567890abcdef",
            "partnerId" to "1234567890",
            "prepayId" to "wx_prepay_${PaymentUtils.generateNonceStr(16)}",
            "packageValue" to "Sign=WXPay",
            "nonceStr" to PaymentUtils.generateNonceStr(),
            "timeStamp" to (System.currentTimeMillis() / 1000).toString(),
            "sign" to "mock_signature_${PaymentUtils.generateNonceStr(16)}"
        )

        return PaymentUtils.toJsonString(params)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        paymentApiService = null
        INSTANCE = null
        LogManager.d(TAG, "支付仓库资源已清理")
    }
}
