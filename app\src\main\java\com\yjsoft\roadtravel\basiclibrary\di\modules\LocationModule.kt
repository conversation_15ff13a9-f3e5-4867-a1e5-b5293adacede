package com.yjsoft.roadtravel.basiclibrary.di.modules

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.location.service.LocationManager as LocationService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 定位框架依赖注入模块
 *
 * 功能：
 * - 提供LocationManager的依赖注入
 * - 管理定位框架的生命周期
 *
 * 设计原则：
 * - 单例模式确保全局唯一
 * - 延迟初始化避免循环依赖
 * - 兼容现有代码逻辑
 *
 * 注意：
 * - 定位服务需要权限支持，使用时需要确保权限已获取
 * - 定位服务的生命周期与应用生命周期绑定
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Module
@InstallIn(SingletonComponent::class)
object LocationModule {

    /**
     * 提供LocationManager实例
     */
    @Provides
    @Singleton
    fun provideLocationManager(
        @ApplicationContext context: Context
    ): LocationService {
        return LocationManagerProvider.getInstance(context)
    }

    /**
     * LocationManager提供器
     */
    private object LocationManagerProvider {
        @Volatile
        private var instance: LocationService? = null

        fun getInstance(context: Context): LocationService {
            return instance ?: synchronized(this) {
                instance ?: createLocationManager(context).also { instance = it }
            }
        }

        private fun createLocationManager(context: Context): LocationService {
            // 检查LocationManager是否已经存在实例
            return LocationService.getInstance(context)
        }
    }
}
