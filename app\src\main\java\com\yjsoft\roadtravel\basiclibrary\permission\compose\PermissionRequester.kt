package com.yjsoft.roadtravel.basiclibrary.permission.compose

import android.os.Build
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionConfig
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionGroups
import com.yjsoft.roadtravel.basiclibrary.permission.core.MultiplePermissionState
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionManager
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionRequest
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionRequestConfig
import com.yjsoft.roadtravel.basiclibrary.permission.core.PermissionResult
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionChecker
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionLogger

import kotlinx.coroutines.launch

/**
 * 权限请求状态
 */
@Stable
class PermissionRequesterState(
    private val permissions: List<String>,
    private val config: PermissionRequestConfig,
    private val permissionManager: PermissionManager
) {
    private var _isRequesting by mutableStateOf(false)
    private var _lastResult by mutableStateOf<PermissionResult?>(null)
    private var _permissionStates by mutableStateOf<MultiplePermissionState?>(null)
    
    val isRequesting: Boolean get() = _isRequesting
    val lastResult: PermissionResult? get() = _lastResult
    val permissionStates: MultiplePermissionState? get() = _permissionStates
    
    /**
     * 所有权限是否已授予
     */
    val allPermissionsGranted: Boolean
        get() = _permissionStates?.allGranted ?: false
    
    /**
     * 是否有权限被拒绝
     */
    val hasPermissionsDenied: Boolean
        get() = _permissionStates?.anyDenied ?: false
    
    /**
     * 是否有权限被永久拒绝
     */
    val hasPermissionsPermanentlyDenied: Boolean
        get() = _permissionStates?.anyPermanentlyDenied ?: false
    
    /**
     * 请求权限
     */
    suspend fun requestPermissions(): PermissionResult {
        if (_isRequesting) {
            return PermissionResult.Error(
                permissions = permissions,
                requestId = "duplicate",
                error = IllegalStateException("权限请求正在进行中")
            )
        }
        
        _isRequesting = true
        
        return try {
            val request = PermissionRequest(
                permissions = permissions,
                config = config
            )
            
            val result = permissionManager.requestPermissions(request)
            _lastResult = result
            
            // 更新权限状态
            refreshPermissionStates()
            
            result
        } finally {
            _isRequesting = false
        }
    }
    
    /**
     * 刷新权限状态
     */
    fun refreshPermissionStates() {
        // 这里需要从PermissionManager获取最新状态
        // 暂时使用PermissionChecker作为备选方案
    }
    
    /**
     * 更新权限状态
     */
    internal fun updatePermissionStates(states: MultiplePermissionState) {
        _permissionStates = states
    }
}

/**
 * 创建权限请求状态
 */
@Composable
fun rememberPermissionRequesterState(
    permissions: List<String>,
    config: PermissionRequestConfig = PermissionConfig.defaultRequestConfig
): PermissionRequesterState {
    val context = LocalContext.current
    val permissionManager = remember { PermissionManager.getInstance(context) }
    
    return remember(permissions, config) {
        PermissionRequesterState(permissions, config, permissionManager)
    }
}

/**
 * 权限请求器Composable
 * 主要的权限申请组件
 */
@Composable
fun PermissionRequester(
    permissions: List<String>,
    config: PermissionRequestConfig = PermissionConfig.defaultRequestConfig,
    onPermissionResult: (PermissionResult) -> Unit = {},
    content: @Composable (PermissionRequesterState) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val activity = context as? ComponentActivity
    
    val state = rememberPermissionRequesterState(permissions, config)
    val scope = rememberCoroutineScope()
    
    // 初始化权限管理器
    LaunchedEffect(activity) {
        activity?.let { act ->
            PermissionManager.getInstance(context).initialize(act)
            PermissionLogger.logFrameworkInitialized()
        }
    }
    
    // 监听权限状态变化
    LaunchedEffect(permissions) {
        if (activity != null) {
            val currentStates = PermissionChecker.getPermissionsState(context, permissions, activity)
            state.updatePermissionStates(currentStates)
        }
    }
    
    // 监听权限结果
    LaunchedEffect(state.lastResult) {
        state.lastResult?.let { result ->
            onPermissionResult(result)
        }
    }
    
    content(state)
}

/**
 * 单个权限请求器
 */
@Composable
fun SinglePermissionRequester(
    permission: String,
    config: PermissionRequestConfig = PermissionConfig.defaultRequestConfig,
    onPermissionResult: (Boolean) -> Unit = {},
    content: @Composable (PermissionRequesterState) -> Unit
) {
    PermissionRequester(
        permissions = listOf(permission),
        config = config,
        onPermissionResult = { result ->
            onPermissionResult(result.isSuccess)
        },
        content = content
    )
}

/**
 * 权限组请求器
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun PermissionGroupRequester(
    groupName: String,
    config: PermissionRequestConfig? = null,
    onPermissionResult: (PermissionResult) -> Unit = {},
    content: @Composable (PermissionRequesterState) -> Unit
) {
    val permissions = remember(groupName) {
        PermissionGroups.getPermissions(groupName)
    }
    
    val groupConfig = remember(groupName, config) {
        config ?: PermissionConfig.getPermissionGroupConfig(groupName) ?: PermissionConfig.defaultRequestConfig
    }
    
    PermissionRequester(
        permissions = permissions,
        config = groupConfig,
        onPermissionResult = onPermissionResult,
        content = content
    )
}

/**
 * 位置权限请求器（便捷组件）
 */
@RequiresApi(Build.VERSION_CODES.Q)
@Composable
fun LocationPermissionRequester(
    includeBackgroundLocation: Boolean = false,
    onPermissionResult: (PermissionResult) -> Unit = {},
    content: @Composable (PermissionRequesterState) -> Unit
) {
    val permissions = remember(includeBackgroundLocation) {
        if (includeBackgroundLocation) {
            PermissionGroups.LOCATION_PERMISSIONS
        } else {
            PermissionGroups.LOCATION_PERMISSIONS.filter {
                it != android.Manifest.permission.ACCESS_BACKGROUND_LOCATION 
            }
        }
    }
    
    val config = remember {
        PermissionRequestConfig(
            showRationaleDialog = true,
            rationaleTitle = "位置权限申请",
            rationaleMessage = "应用需要获取您的位置信息来提供导航和定位服务",
            showSettingsDialog = true,
            settingsTitle = "开启位置权限",
            settingsMessage = "请在设置中开启位置权限，以便应用为您提供更好的服务"
        )
    }
    
    PermissionRequester(
        permissions = permissions,
        config = config,
        onPermissionResult = onPermissionResult,
        content = content
    )
}

/**
 * 相机权限请求器（便捷组件）
 */
@Composable
fun CameraPermissionRequester(
    onPermissionResult: (Boolean) -> Unit = {},
    content: @Composable (PermissionRequesterState) -> Unit
) {
    val config = remember {
        PermissionRequestConfig(
            showRationaleDialog = true,
            rationaleTitle = "相机权限申请",
            rationaleMessage = "应用需要使用相机来拍摄照片和录制视频",
            showSettingsDialog = true,
            settingsTitle = "开启相机权限",
            settingsMessage = "请在设置中开启相机权限"
        )
    }
    
    SinglePermissionRequester(
        permission = android.Manifest.permission.CAMERA,
        config = config,
        onPermissionResult = onPermissionResult,
        content = content
    )
}

/**
 * 存储权限请求器（便捷组件）
 */
@Composable
fun StoragePermissionRequester(
    onPermissionResult: (PermissionResult) -> Unit = {},
    content: @Composable (PermissionRequesterState) -> Unit
) {
    val config = remember {
        PermissionRequestConfig(
            showRationaleDialog = true,
            rationaleTitle = "存储权限申请",
            rationaleMessage = "应用需要访问存储空间来保存和读取文件",
            showSettingsDialog = true,
            settingsTitle = "开启存储权限",
            settingsMessage = "请在设置中开启存储权限"
        )
    }
    
    PermissionRequester(
        permissions = PermissionGroups.STORAGE_PERMISSIONS,
        config = config,
        onPermissionResult = onPermissionResult,
        content = content
    )
}
