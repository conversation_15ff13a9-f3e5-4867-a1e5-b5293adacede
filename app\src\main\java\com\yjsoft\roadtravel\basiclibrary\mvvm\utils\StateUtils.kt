package com.yjsoft.roadtravel.basiclibrary.mvvm.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Resource
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 * 状态管理工具类
 * 
 * 功能：
 * - 状态流管理工具
 * - Compose状态工具
 * - 状态缓存和恢复
 * - 状态验证工具
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
object StateUtils {
    
    private const val TAG = "StateUtils"
    
    // ========== StateFlow工具 ==========
    
    /**
     * 创建带初始值的StateFlow
     */
    fun <T> createStateFlow(initialValue: T): MutableStateFlow<T> {
        return MutableStateFlow(initialValue)
    }
    
    /**
     * 创建UiState StateFlow
     */
    fun <T> createUiStateFlow(initialState: UiState<T> = UiState.Idle): MutableStateFlow<UiState<T>> {
        return MutableStateFlow(initialState)
    }
    
    /**
     * 创建Resource StateFlow
     */
    fun <T> createResourceFlow(): MutableStateFlow<Resource<T>> {
        return MutableStateFlow(Resource.Loading())
    }
    
    /**
     * 安全更新StateFlow
     */
    fun <T> MutableStateFlow<T>.safeUpdate(update: (T) -> T) {
        try {
            value = update(value)
        } catch (e: Exception) {
            LogManager.e(e, "[%s] StateFlow更新失败", TAG)
        }
    }
    
    /**
     * 条件更新StateFlow
     */
    fun <T> MutableStateFlow<T>.updateIf(condition: (T) -> Boolean, update: (T) -> T) {
        if (condition(value)) {
            safeUpdate(update)
        }
    }
    
    // ========== 状态组合工具 ==========
    
    /**
     * 组合两个StateFlow
     */
    fun <T1, T2, R> combineStates(
        flow1: StateFlow<T1>,
        flow2: StateFlow<T2>,
        transform: (T1, T2) -> R
    ): Flow<R> {
        return combine(flow1, flow2) { value1, value2 ->
            transform(value1, value2)
        }
    }
    
    /**
     * 组合三个StateFlow
     */
    fun <T1, T2, T3, R> combineStates(
        flow1: StateFlow<T1>,
        flow2: StateFlow<T2>,
        flow3: StateFlow<T3>,
        transform: (T1, T2, T3) -> R
    ): Flow<R> {
        return combine(flow1, flow2, flow3) { value1, value2, value3 ->
            transform(value1, value2, value3)
        }
    }
    
    /**
     * 组合多个UiState
     */
    fun <T1, T2, R> combineUiStates(
        flow1: StateFlow<UiState<T1>>,
        flow2: StateFlow<UiState<T2>>,
        transform: (T1, T2) -> R
    ): Flow<UiState<R>> {
        return combine(flow1, flow2) { state1, state2 ->
            MVVMUtils.combineUiStates(state1, state2, transform)
        }
    }
    
    // ========== Compose状态工具 ==========
    
    /**
     * 记忆化状态转换
     */
    @Composable
    fun <T, R> rememberDerivedState(
        state: State<T>,
        transform: (T) -> R
    ): State<R> {
        return remember(state) {
            derivedStateOf { transform(state.value) }
        }
    }
    
    /**
     * 记忆化UiState处理
     */
    @Composable
    fun <T> rememberUiStateProcessor(
        uiState: UiState<T>
    ): UiStateProcessor<T> {
        return remember(uiState) {
            UiStateProcessor(uiState)
        }
    }
    
    /**
     * UiState处理器
     */
    class UiStateProcessor<T>(private val uiState: UiState<T>) {
        val isLoading: Boolean get() = uiState is UiState.Loading
        val isSuccess: Boolean get() = uiState is UiState.Success
        val isError: Boolean get() = uiState is UiState.Error
        val isEmpty: Boolean get() = uiState is UiState.Empty
        val isIdle: Boolean get() = uiState is UiState.Idle
        
        val data: T? get() = (uiState as? UiState.Success)?.data
        val errorMessage: String get() = (uiState as? UiState.Error)?.message ?: ""
        val exception: Throwable? get() = (uiState as? UiState.Error)?.exception
    }
    
    // ========== 状态缓存工具 ==========
    
    /**
     * 状态缓存管理器
     */
    class StateCache<K, V> {
        private val cache = mutableMapOf<K, CacheEntry<V>>()
        private val defaultTtl = 5 * 60 * 1000L // 5分钟
        
        data class CacheEntry<V>(
            val value: V,
            val timestamp: Long,
            val ttl: Long
        ) {
            fun isExpired(): Boolean = System.currentTimeMillis() - timestamp > ttl
        }
        
        fun put(key: K, value: V, ttl: Long = defaultTtl) {
            cache[key] = CacheEntry(value, System.currentTimeMillis(), ttl)
        }
        
        fun get(key: K): V? {
            val entry = cache[key] ?: return null
            return if (entry.isExpired()) {
                cache.remove(key)
                null
            } else {
                entry.value
            }
        }
        
        fun remove(key: K): V? {
            return cache.remove(key)?.value
        }
        
        fun clear() {
            cache.clear()
        }
        
        fun cleanExpired() {
            val expiredKeys = cache.filter { it.value.isExpired() }.keys
            expiredKeys.forEach { cache.remove(it) }
        }
        
        fun size(): Int = cache.size
    }
    
    // ========== 状态持久化工具 ==========
    
    /**
     * 状态快照
     */
    data class StateSnapshot<T>(
        val value: T,
        val timestamp: Long = System.currentTimeMillis(),
        val version: Int = 1
    )
    
    /**
     * 状态历史管理器
     */
    class StateHistory<T>(private val maxSize: Int = 10) {
        private val history = mutableListOf<StateSnapshot<T>>()
        
        fun addSnapshot(value: T) {
            if (history.size >= maxSize) {
                history.removeAt(0)
            }
            history.add(StateSnapshot(value))
        }
        
        fun getLatest(): StateSnapshot<T>? = history.lastOrNull()
        
        fun getPrevious(): StateSnapshot<T>? {
            return if (history.size >= 2) history[history.size - 2] else null
        }
        
        fun getHistory(): List<StateSnapshot<T>> = history.toList()
        
        fun canUndo(): Boolean = history.size > 1
        
        fun undo(): StateSnapshot<T>? {
            return if (canUndo()) {
                history.removeAt(history.size - 1)
                history.lastOrNull()
            } else null
        }
        
        fun clear() = history.clear()
        
        fun size(): Int = history.size
    }
    
    // ========== 状态验证工具 ==========
    
    /**
     * 验证UiState
     */
    fun <T> validateUiState(state: UiState<T>, tag: String = "UiState"): Boolean {
        return when (state) {
            is UiState.Success -> {
                val isValid = state.data != null
                if (!isValid) {
                    LogManager.w("[%s] Success状态但数据为null", tag)
                }
                isValid
            }
            is UiState.Error -> {
                val hasMessage = state.message.isNotEmpty()
                if (!hasMessage) {
                    LogManager.w("[%s] Error状态但消息为空", tag)
                }
                hasMessage
            }
            else -> true
        }
    }
    
    /**
     * 验证Resource
     */
    fun <T> validateResource(resource: Resource<T>, tag: String = "Resource"): Boolean {
        return when (resource) {
            is Resource.Success -> {
                val isValid = resource.data != null
                if (!isValid) {
                    LogManager.w("[%s] Success状态但数据为null", tag)
                }
                isValid
            }
            is Resource.Error -> {
                val hasMessage = resource.message.isNotEmpty()
                if (!hasMessage) {
                    LogManager.w("[%s] Error状态但消息为空", tag)
                }
                hasMessage
            }
            else -> true
        }
    }
    
    // ========== 状态转换工具 ==========

    /**
     * 创建带日志的去重Flow
     */
    fun <T> createDistinctFlowWithLogging(flow: Flow<T>, tag: String): Flow<T> {
        return flow.distinctUntilChanged { old, new ->
            val isSame = old == new
            if (!isSame) {
                LogManager.d("[%s] 状态变化: %s -> %s", tag, old, new)
            }
            isSame
        }
    }
    
    // ========== 调试工具 ==========
    
    /**
     * 状态变化监听器
     */
    @Composable
    fun <T> StateChangeListener(
        state: State<T>,
        tag: String = "State",
        onStateChange: ((T) -> Unit)? = null
    ) {
        val currentValue = state.value
        
        LaunchedEffect(currentValue) {
            LogManager.d("[%s] 状态变化: %s", tag, currentValue)
            onStateChange?.invoke(currentValue)
        }
    }
    
    /**
     * UiState变化监听器
     */
    @Composable
    fun <T> UiStateChangeListener(
        uiState: UiState<T>,
        tag: String = "UiState"
    ) {
        LaunchedEffect(uiState) {
            when (uiState) {
                is UiState.Idle -> LogManager.d("[%s] 进入空闲状态", tag)
                is UiState.Loading -> LogManager.d("[%s] 进入加载状态: %s", tag, uiState.message)
                is UiState.Success -> LogManager.d("[%s] 进入成功状态", tag)
                is UiState.Error -> LogManager.d("[%s] 进入错误状态: %s", tag, uiState.message)
                is UiState.Empty -> LogManager.d("[%s] 进入空数据状态: %s", tag, uiState.message)
            }
        }
    }
    
    /**
     * 性能监控
     */
    @Composable
    fun <T> PerformanceMonitor(
        state: State<T>,
        tag: String = "Performance"
    ) {
        var lastUpdateTime by remember { mutableStateOf(System.currentTimeMillis()) }
        
        LaunchedEffect(state.value) {
            val currentTime = System.currentTimeMillis()
            val timeDiff = currentTime - lastUpdateTime
            LogManager.d("[%s] 状态更新间隔: %dms", tag, timeDiff)
            lastUpdateTime = currentTime
        }
    }
}

// ========== 扩展函数（必须在object外部） ==========

/**
 * 将Flow转换为去重的Flow
 */
fun <T> Flow<T>.distinctUntilChangedWithLogging(tag: String): Flow<T> {
    return this.distinctUntilChanged { old, new ->
        val isSame = old == new
        if (!isSame) {
            LogManager.d("[%s] 状态变化: %s -> %s", tag, old, new)
        }
        isSame
    }
}

/**
 * 映射UiState数据
 */
fun <T, R> Flow<UiState<T>>.mapUiStateData(transform: (T) -> R): Flow<UiState<R>> {
    return this.map { state ->
        when (state) {
            is UiState.Success -> UiState.Success(transform(state.data), state.message)
            is UiState.Loading -> UiState.Loading(state.message)
            is UiState.Error -> UiState.Error(state.exception, state.message, state.code)
            is UiState.Empty -> UiState.Empty(state.message)
            is UiState.Idle -> UiState.Idle
        }
    }
}

/**
 * 过滤UiState
 */
fun <T> Flow<UiState<T>>.filterUiState(predicate: (UiState<T>) -> Boolean): Flow<UiState<T>> {
    return this.map { state ->
        if (predicate(state)) state else UiState.Idle
    }
}
