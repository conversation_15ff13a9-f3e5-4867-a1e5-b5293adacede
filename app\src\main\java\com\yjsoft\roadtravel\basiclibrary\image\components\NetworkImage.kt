package com.yjsoft.roadtravel.basiclibrary.image.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.compose.AsyncImagePainter
import coil3.compose.rememberAsyncImagePainter
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager

/**
 * 网络图片组件
 * 基于Coil3的AsyncImage封装，提供统一的网络图片加载体验
 */
@Composable
fun NetworkImage(
    url: String,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    placeholder: Painter? = null,
    error: Painter? = null,
    fallback: Painter? = null,
    contentScale: ContentScale = ContentScale.Crop,
    colorFilter: ColorFilter? = null,
    alpha: Float = 1.0f,
    crossfadeEnabled: Boolean = true,
    crossfadeDuration: Int = 300,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    val context = LocalContext.current
    val imageLoader = ImageManager.getImageLoader(context)
    
    AsyncImage(
        model = ImageRequest.Builder(context)
            .data(url)
            .apply {
                if (crossfadeEnabled) {
                    crossfade(crossfadeDuration)
                }
            }
            .build(),
        contentDescription = contentDescription,
        imageLoader = imageLoader,
        modifier = modifier,
        placeholder = placeholder,
        error = error,
        fallback = fallback,
        contentScale = contentScale,
        colorFilter = colorFilter,
        alpha = alpha,
        onLoading = onLoading,
        onSuccess = onSuccess,
        onError = onError
    )
}

/**
 * 带默认占位符的网络图片组件
 */
@Composable
fun NetworkImageWithPlaceholder(
    url: String,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    placeholderRes: Int? = null,
    errorRes: Int? = null,
    contentScale: ContentScale = ContentScale.Crop,
    colorFilter: ColorFilter? = null,
    alpha: Float = 1.0f,
    crossfadeEnabled: Boolean = true,
    showLoadingIndicator: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    val placeholder = placeholderRes?.let { painterResource(it) }
    val error = errorRes?.let { painterResource(it) }
    
    Box(modifier = modifier) {
        NetworkImage(
            url = url,
            contentDescription = contentDescription,
            modifier = Modifier.fillMaxSize(),
            placeholder = placeholder,
            error = error,
            contentScale = contentScale,
            colorFilter = colorFilter,
            alpha = alpha,
            crossfadeEnabled = crossfadeEnabled,
            onLoading = { state ->
                onLoading?.invoke(state)
            },
            onSuccess = onSuccess,
            onError = onError
        )
        
        // 显示加载指示器
        if (showLoadingIndicator) {
            val painter = rememberAsyncImagePainter(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(url)
                    .build(),
                imageLoader = ImageManager.getImageLoader(LocalContext.current)
            )

            // 使用collectAsState来观察状态
            val state by painter.state.collectAsState()
            if (state is AsyncImagePainter.State.Loading) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .size(24.dp)
                        .align(Alignment.Center),
                    color = MaterialTheme.colorScheme.primary,
                    strokeWidth = 2.dp
                )
            }
        }
    }
}

/**
 * 固定尺寸的网络图片组件
 */
@Composable
fun NetworkImageFixedSize(
    url: String,
    contentDescription: String?,
    size: Dp,
    modifier: Modifier = Modifier,
    placeholder: Painter? = null,
    error: Painter? = null,
    contentScale: ContentScale = ContentScale.Crop,
    colorFilter: ColorFilter? = null,
    alpha: Float = 1.0f,
    crossfadeEnabled: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    NetworkImage(
        url = url,
        contentDescription = contentDescription,
        modifier = modifier.size(size),
        placeholder = placeholder,
        error = error,
        contentScale = contentScale,
        colorFilter = colorFilter,
        alpha = alpha,
        crossfadeEnabled = crossfadeEnabled,
        onLoading = onLoading,
        onSuccess = onSuccess,
        onError = onError
    )
}

/**
 * 带背景色的网络图片组件
 */
@Composable
fun NetworkImageWithBackground(
    url: String,
    contentDescription: String?,
    backgroundColor: Color,
    modifier: Modifier = Modifier,
    placeholder: Painter? = null,
    error: Painter? = null,
    contentScale: ContentScale = ContentScale.Crop,
    colorFilter: ColorFilter? = null,
    alpha: Float = 1.0f,
    crossfadeEnabled: Boolean = true,
    onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null,
    onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
    onError: ((AsyncImagePainter.State.Error) -> Unit)? = null
) {
    Box(
        modifier = modifier
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        
        NetworkImage(
            url = url,
            contentDescription = contentDescription,
            modifier = Modifier.fillMaxSize(),
            placeholder = placeholder,
            error = error,
            contentScale = contentScale,
            colorFilter = colorFilter,
            alpha = alpha,
            crossfadeEnabled = crossfadeEnabled,
            onLoading = onLoading,
            onSuccess = onSuccess,
            onError = onError
        )
    }
}

/**
 * 网络图片加载状态枚举
 */
enum class NetworkImageState {
    LOADING,    // 加载中
    SUCCESS,    // 加载成功
    ERROR,      // 加载失败
    EMPTY       // 初始状态
}

/**
 * 网络图片加载状态回调
 */
data class NetworkImageCallback(
    val onStateChange: ((NetworkImageState) -> Unit)? = null,
    val onProgress: ((Float) -> Unit)? = null,
    val onError: ((Throwable) -> Unit)? = null
)

/**
 * 带状态监听的网络图片组件
 */
@Composable
fun NetworkImageWithState(
    url: String,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    placeholder: Painter? = null,
    error: Painter? = null,
    contentScale: ContentScale = ContentScale.Crop,
    colorFilter: ColorFilter? = null,
    alpha: Float = 1.0f,
    crossfadeEnabled: Boolean = true,
    callback: NetworkImageCallback? = null
) {
    NetworkImage(
        url = url,
        contentDescription = contentDescription,
        modifier = modifier,
        placeholder = placeholder,
        error = error,
        contentScale = contentScale,
        colorFilter = colorFilter,
        alpha = alpha,
        crossfadeEnabled = crossfadeEnabled,
        onLoading = { state ->
            callback?.onStateChange?.invoke(NetworkImageState.LOADING)
        },
        onSuccess = { state ->
            callback?.onStateChange?.invoke(NetworkImageState.SUCCESS)
        },
        onError = { state ->
            callback?.onStateChange?.invoke(NetworkImageState.ERROR)
            callback?.onError?.invoke(state.result.throwable)
        }
    )
}
