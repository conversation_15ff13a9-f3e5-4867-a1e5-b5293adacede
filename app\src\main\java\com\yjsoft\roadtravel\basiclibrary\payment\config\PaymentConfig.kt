package com.yjsoft.roadtravel.basiclibrary.payment.config

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType

/**
 * 支付框架配置管理器
 * 负责管理支付相关的配置信息和环境设置
 */
object PaymentConfig {

    private var isInitialized = false
    private var applicationContext: Context? = null
    
    // 支付环境配置
    var isDebugMode: Boolean = false
        private set
    
    // 支付超时配置（毫秒）
    var paymentTimeoutMs: Long = 30_000L
        private set
    
    // 支持的支付方式
    private val enabledPaymentTypes = mutableSetOf<PaymentType>()
    
    // 支付宝配置
    var alipayAppId: String? = null
        private set
    var alipayPrivateKey: String? = null
        private set
    var alipayPublicKey: String? = null
        private set
    
    // 微信支付配置
    var wechatAppId: String? = null
        private set
    var wechatMerchantId: String? = null
        private set
    var wechatApiKey: String? = null
        private set
    
    // 云闪付配置
    var unionPayMerchantId: String? = null
        private set
    var unionPayAppId: String? = null
        private set
    
    /**
     * 初始化支付配置
     * @param context 应用上下文
     * @param debugMode 是否为调试模式
     */
    fun init(context: Context, debugMode: Boolean = false) {
        if (isInitialized) {
            LogManager.w("PaymentConfig已经初始化，跳过重复初始化")
            return
        }

        // 只保存ApplicationContext，避免内存泄漏
        this.applicationContext = context.applicationContext
        this.isDebugMode = debugMode
        
        // 默认启用所有支付方式
        enabledPaymentTypes.addAll(PaymentType.entries.toTypedArray())
        
        isInitialized = true
        LogManager.i("支付配置初始化完成 - 调试模式: $debugMode")
    }
    
    /**
     * 配置支付宝参数
     */
    fun configureAlipay(
        appId: String,
        privateKey: String,
        publicKey: String
    ) {
        this.alipayAppId = appId
        this.alipayPrivateKey = privateKey
        this.alipayPublicKey = publicKey
        LogManager.d("支付宝配置已更新")
    }
    
    /**
     * 配置微信支付参数
     */
    fun configureWeChatPay(
        appId: String,
        merchantId: String,
        apiKey: String
    ) {
        this.wechatAppId = appId
        this.wechatMerchantId = merchantId
        this.wechatApiKey = apiKey
        LogManager.d("微信支付配置已更新")
    }
    
    /**
     * 配置云闪付参数
     */
    fun configureUnionPay(
        merchantId: String,
        appId: String
    ) {
        this.unionPayMerchantId = merchantId
        this.unionPayAppId = appId
        LogManager.d("云闪付配置已更新")
    }
    
    /**
     * 设置支付超时时间
     */
    fun setPaymentTimeout(timeoutMs: Long) {
        this.paymentTimeoutMs = timeoutMs
        LogManager.d("支付超时时间设置为: ${timeoutMs}ms")
    }
    
    /**
     * 启用指定的支付方式
     */
    fun enablePaymentType(type: PaymentType) {
        enabledPaymentTypes.add(type)
        LogManager.d("启用支付方式: ${type.name}")
    }
    
    /**
     * 禁用指定的支付方式
     */
    fun disablePaymentType(type: PaymentType) {
        enabledPaymentTypes.remove(type)
        LogManager.d("禁用支付方式: ${type.name}")
    }
    
    /**
     * 检查支付方式是否启用
     */
    fun isPaymentTypeEnabled(type: PaymentType): Boolean {
        return enabledPaymentTypes.contains(type)
    }
    
    /**
     * 获取所有启用的支付方式
     */
    fun getEnabledPaymentTypes(): List<PaymentType> {
        return enabledPaymentTypes.toList()
    }
    
    /**
     * 获取应用上下文
     * 注意：返回的是ApplicationContext，避免内存泄漏
     */
    fun getContext(): Context {
        return applicationContext ?: throw IllegalStateException("PaymentConfig未初始化，请先调用init()方法")
    }
    
    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * 验证支付宝配置是否完整
     */
    fun isAlipayConfigured(): Boolean {
        return !alipayAppId.isNullOrBlank() && 
               !alipayPrivateKey.isNullOrBlank() && 
               !alipayPublicKey.isNullOrBlank()
    }
    
    /**
     * 验证微信支付配置是否完整
     */
    fun isWeChatPayConfigured(): Boolean {
        return !wechatAppId.isNullOrBlank() && 
               !wechatMerchantId.isNullOrBlank() && 
               !wechatApiKey.isNullOrBlank()
    }
    
    /**
     * 验证云闪付配置是否完整
     */
    fun isUnionPayConfigured(): Boolean {
        return !unionPayMerchantId.isNullOrBlank() &&
               !unionPayAppId.isNullOrBlank()
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        applicationContext = null
        isInitialized = false
        LogManager.d("PaymentConfig资源已清理")
    }
}
