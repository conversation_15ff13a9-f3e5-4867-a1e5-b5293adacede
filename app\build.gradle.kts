// 💡 新 DSL — 统一 Kotlin 编译器参数
import org.jetbrains.kotlin.gradle.dsl.JvmTarget



plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.kotlinx.serialization)
    alias(libs.plugins.hilt)
    kotlin("kapt")
}

android {
    namespace = "com.yjsoft.roadtravel"
    compileSdk = 36

    defaultConfig {
        applicationId = "com.yjsoft.roadtravel"
        minSdk = 24
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
       ndk {
           //设置支持的SO库架构
           abiFilters.addAll(listOf("armeabi-v7a", "arm64-v8a"))
       }
    }

    // 签名配置 - 从gradle.properties读取
    signingConfigs {
        create("release") {
            storeFile = file("../${project.property("KEYSTORE_FILE")}")
            storePassword = project.property("KEYSTORE_PASSWORD") as String
            keyAlias = project.property("KEY_ALIAS") as String
            keyPassword = project.property("KEY_PASSWORD") as String
        }
    }

    buildTypes {
        debug {
            // Debug模式使用Release签名
            signingConfig = signingConfigs.getByName("release")
            isDebuggable = true
            // applicationIdSuffix = ".debug"
        }
        release {
            isMinifyEnabled = false
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro", "retrofit2.pro"
            )
        }
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
}

// Kapt配置
kapt {
    correctErrorTypes = true
}
kotlin {
    jvmToolchain(17)
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
    }
}

dependencies {
    // 引入本地的jar包
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.compose.foundation)
    implementation(libs.androidx.material.icons.extended)

    // Navigation Compose 导航框架
    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.navigation.runtime.ktx)
    
    // 序列化支持
    implementation(libs.kotlinx.serialization.json)

    // 网络请求相关依赖
    implementation(libs.retrofit.core)
    implementation(libs.retrofit.converter.gson)
    implementation(platform(libs.okhttp.bom)) // OkHttp 的 BOM
    implementation(libs.okhttp.logging.interceptor)
    implementation(libs.gson)


    // 协程相关依赖
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // ViewModel相关依赖
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.compose)

    // 日志框架依赖
    implementation(libs.timber)
    debugImplementation(libs.leakcanary.android)

    // DataStore依赖
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.datastore.core)

    // 图片加载框架依赖
    implementation(libs.coil.compose)
    implementation(libs.coil.network.okhttp)

    // 支付相关依赖
    // 支付宝SDK
    implementation(libs.alipay.sdk)
    // 微信支付SDK
    implementation(libs.wechat.sdk)

    //高德地图
    implementation(libs.location)
    implementation(libs.map2d)

    // Hilt 依赖注入
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)
    implementation(libs.hilt.navigation.compose)

    // SplashScreen API
    implementation(libs.androidx.core.splashscreen)

    // androidx.startup
    implementation(libs.androidx.startup.runtime)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}