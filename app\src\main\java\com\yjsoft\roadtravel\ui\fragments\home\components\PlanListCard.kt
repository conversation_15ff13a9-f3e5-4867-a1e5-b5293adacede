package com.yjsoft.roadtravel.ui.fragments.home.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.toColorInt
import com.yjsoft.roadtravel.basiclibrary.image.components.NetworkImage
import com.yjsoft.roadtravel.ui.fragments.home.PlanItem
import java.util.Locale

/**
 * 渐变背景色定义
 */
data class GradientColor(
    val from: Color,
    val to: Color
)

/**
 * 预定义的渐变颜色
 */
val gradientColors = listOf(
    GradientColor(Color(0x80_99C7FF), Color(0x80_F3F7FF)), // 蓝色系
    GradientColor(Color(0x80_D2BEFB), Color(0x80_FBF1FF)), // 紫色系
    GradientColor(Color(0x80_99FFE8), Color(0x80_E5FFF5)), // 绿色系
    GradientColor(Color(0x80_FBD5BE), Color(0x80_FFF8F1)), // 橙色系
    GradientColor(Color(0x80_FFBCE6), Color(0x80_FFF3F4))  // 粉色系
)

/**
 * 创建线性渐变背景
 */
fun createLinearGradient(index: Int): Brush {
    val color = gradientColors[index % gradientColors.size]
    return Brush.verticalGradient(
        colors = listOf(color.from, color.to)
    )
}

/**
 * 计划列表卡片组件
 */
@Composable
fun PlanListCard(
    cityName: String,
    selectedTag: String,
    tagColor: String,
    planList: List<PlanItem>,
    onSeeMoreClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        // 主卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 17.dp) // 为图片留出突出空间
                .shadow(
                    elevation = 4.dp,
                    shape = RoundedCornerShape(10.dp),
                    ambientColor = Color.Black.copy(alpha = 0.08f), // 8%的黑色阴影
                    spotColor = Color.Black.copy(alpha = 0.08f)
                ),
            shape = RoundedCornerShape(10.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(horizontal = 10.dp, vertical = 16.dp)
            ) {
                // 顶部标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左侧：城市名称 + 圆点 + 标签
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            modifier = Modifier.padding(start = 60.dp),
                            text = cityName,
                            fontSize = 16.sp,
                            color = Color.Black,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = " • ",
                            fontSize = 16.sp,
                            color = Color.Black,
                            modifier = Modifier.padding(horizontal = 2.dp)
                        )
                        Text(
                            text = selectedTag,
                            fontSize = 16.sp,
                            color = Color(tagColor.toColorInt()),
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    // 右侧：查看更多按钮
                    Text(
                        text = "查看更多",
                        fontSize = 14.sp,
                        color = Color.Black,
                        modifier = Modifier.clickable { onSeeMoreClick() }
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 计划列表
                val displayPlans = planList.take(3)
                displayPlans.forEachIndexed { index, plan ->
                    PlanItemCard(
                        plan = plan,
                        backgroundIndex = index,
                        modifier = Modifier.padding(bottom = if (index < displayPlans.size - 1) 8.dp else 0.dp)
                    )
                }
            }
        }
        
        // 左上角图片
        NetworkImage(
            url = "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/index/list-item-ren.png",
            contentDescription = "计划图标",
            modifier = Modifier
                .size(width = 76.dp, height = 80.dp)
                .align(Alignment.TopStart),
            contentScale = ContentScale.Fit
        )
    }
}

/**
 * 计划项卡片组件
 */
@Composable
fun PlanItemCard(
    plan: PlanItem,
    backgroundIndex: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = Color.Transparent)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(createLinearGradient(backgroundIndex))
                .padding(horizontal = 10.dp, vertical = 16.dp)
        ) {
            Column {
                // 顶部标题行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    Text(
                        text = plan.subject,
                        fontSize = 14.sp,
                        color = Color.Black,
                        fontWeight = FontWeight.Medium,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    
                    // 喜欢图标
                    Icon(
                        imageVector = if (plan.isOwn) Icons.Filled.Favorite else Icons.Filled.FavoriteBorder,
                        contentDescription = if (plan.isOwn) "已喜欢" else "未喜欢",
                        modifier = Modifier.size(20.dp),
                        tint = if (plan.isOwn) Color.Red else Color.Gray
                    )
                }
                
                Spacer(modifier = Modifier.height(6.dp))
                
                // 标签和费用行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 标签
                    Row {
                        plan.tags.take(2).forEachIndexed { index, tag ->
                            val (backgroundColor, textColor) = when (index) {
                                0 -> Color(0xFF_C0FAFF) to Color(0xFF_1890FF)
                                else -> Color(0xFF_FFF2C1) to Color.Black
                            }
                            
                            Text(
                                text = tag,
                                fontSize = 10.sp,
                                color = textColor,
                                modifier = Modifier
                                    .background(
                                        backgroundColor,
                                        RoundedCornerShape(4.dp)
                                    )
                                    .padding(horizontal = 5.dp)
                            )
                            
                            if (index < 1 && plan.tags.size > 1) {
                                Spacer(modifier = Modifier.width(4.dp))
                            }
                        }
                    }
                    
                    // 预计费用
                    Text(
                        text = "预计费用: ￥${
                            String.format(
                                Locale.getDefault(),
                                "%.1f",
                                plan.costAvg1 / 100.0
                            )
                        }",
                        fontSize = 10.sp,
                        color = Color(0xFF_1890FF),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 副标题
                Text(
                    text = plan.subtitle,
                    fontSize = 14.sp,
                    color = Color.Black,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
} 