# 图片加载框架

基于 Coil3 封装的图片加载框架，提供统一的图片加载解决方案。

## 特性

- 🚀 基于 Coil3，性能优异
- 🎨 丰富的 UI 组件（网络图片、头像、占位符等）
- 🔧 灵活的配置选项
- 📱 完美适配 Jetpack Compose
- 🗂️ 智能缓存管理
- 🎯 多种图片变换效果
- 📊 详细的使用统计

## 快速开始

### 1. 初始化

在 `Application` 中初始化图片加载框架：

```kotlin
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 基础初始化
        ImageManager.init(this)
        
        // 或者使用自定义配置
        val config = ImageConfig.createDebugConfig()
        ImageManager.init(this, config)
    }
}
```

### 2. 基础使用

```kotlin
@Composable
fun MyScreen() {
    // 基础网络图片
    NetworkImage(
        url = "https://example.com/image.jpg",
        contentDescription = "示例图片",
        modifier = Modifier.size(200.dp)
    )
    
    // 带占位符的网络图片
    NetworkImageWithPlaceholder(
        url = imageUrl,
        contentDescription = "图片",
        modifier = Modifier.fillMaxWidth(),
        showLoadingIndicator = true
    )
    
    // 智能头像
    SmartAvatarImage(
        url = user.avatarUrl,
        name = user.name,
        contentDescription = "用户头像",
        size = AvatarSize.Large
    )
}
```

## 核心组件

### 网络图片组件

#### NetworkImage
基础的网络图片组件，支持各种自定义选项。

```kotlin
NetworkImage(
    url = "https://example.com/image.jpg",
    contentDescription = "图片描述",
    modifier = Modifier.size(200.dp),
    contentScale = ContentScale.Crop,
    crossfadeEnabled = true,
    onSuccess = { /* 加载成功回调 */ },
    onError = { /* 加载失败回调 */ }
)
```

#### NetworkImageWithPlaceholder
带占位符和加载指示器的网络图片组件。

```kotlin
NetworkImageWithPlaceholder(
    url = imageUrl,
    contentDescription = "图片",
    modifier = Modifier.fillMaxWidth(),
    showLoadingIndicator = true,
    placeholderRes = R.drawable.placeholder,
    errorRes = R.drawable.error
)
```

### 头像组件

#### AvatarImage
基础头像组件，支持圆形和方形样式。

```kotlin
AvatarImage(
    url = user.avatarUrl,
    contentDescription = "用户头像",
    size = AvatarSize.Medium,
    shape = CircleShape,
    borderWidth = 2.dp,
    borderColor = Color.White
)
```

#### SmartAvatarImage
智能头像组件，自动根据用户名生成背景色和文字占位符。

```kotlin
SmartAvatarImage(
    url = user.avatarUrl,
    name = user.name,
    contentDescription = "用户头像",
    size = AvatarSize.Large
)
```

### 占位符组件

#### PlaceholderImage
通用占位符组件。

```kotlin
PlaceholderImage(
    modifier = Modifier.size(200.dp),
    icon = Icons.Default.Image,
    text = "暂无图片",
    backgroundColor = Color.Gray
)
```

#### 预设占位符
- `LoadingPlaceholder` - 加载中占位符
- `ErrorPlaceholder` - 错误占位符
- `EmptyPlaceholder` - 空状态占位符

## 配置选项

### 环境配置

```kotlin
// 调试环境配置
val debugConfig = ImageConfig.createDebugConfig(okHttpClient)

// 发布环境配置
val releaseConfig = ImageConfig.createReleaseConfig(okHttpClient)

// 性能优先配置
val performanceConfig = ImageConfig.createPerformanceConfig(okHttpClient)

// 质量优先配置
val qualityConfig = ImageConfig.createQualityConfig(okHttpClient)
```

### 缓存配置

```kotlin
// 清理缓存
ImageManager.clearAllCache()
ImageManager.clearMemoryCache()
ImageManager.clearDiskCache()

// 获取缓存统计
val stats = ImageManager.getCacheStats()
val report = ImageManager.getCacheUsageReport()

// 优化缓存配置
ImageManager.optimizeCacheConfig(context)
```

## 图片变换

### 基础变换

```kotlin
// 圆形变换
val circleTransform = ImageTransformations.createCircleTransformation()

// 圆角变换
val roundedTransform = ImageTransformations.createRoundedCornersTransformation(12.dp)

// 自定义圆角
val customRoundedTransform = ImageTransformations.createRoundedCornersTransformation(
    topLeft = 12.dp,
    topRight = 12.dp,
    bottomLeft = 0.dp,
    bottomRight = 0.dp
)
```

### 预设配置

```kotlin
// 使用预设变换配置
val avatarConfig = ImageTransformations.Presets.AVATAR
val cardConfig = ImageTransformations.Presets.CARD
val thumbnailConfig = ImageTransformations.Presets.THUMBNAIL
```

## 工具类

### ImageUtils
提供图片处理相关的工具方法。

```kotlin
// 检查URL有效性
val isValid = ImageUtils.isValidImageUrl(url)

// 创建标准ImageRequest
val request = ImageUtils.createImageRequest(context, url)

// 压缩图片
ImageUtils.compressImage(context, sourceUri, targetFile, quality = ImageQuality.MEDIUM)

// 获取图片主要颜色
val dominantColor = ImageUtils.getDominantColor(bitmap)
```

## 最佳实践

### 1. 性能优化

- 根据使用场景选择合适的 ImageLoader 类型
- 合理设置图片尺寸，避免加载过大的图片
- 使用适当的图片格式和质量设置
- 定期清理缓存，避免占用过多存储空间

### 2. 用户体验

- 始终提供占位符和错误处理
- 使用淡入淡出动画提升视觉体验
- 为头像等小图片使用快速加载配置
- 在网络较差时降低图片质量

### 3. 内存管理

- 在列表中使用缩略图配置
- 避免同时加载大量高分辨率图片
- 及时释放不需要的图片资源

## 故障排除

### 常见问题

1. **图片加载失败**
   - 检查网络连接
   - 验证图片URL的有效性
   - 查看错误日志

2. **内存不足**
   - 减少同时加载的图片数量
   - 使用较低的图片质量设置
   - 清理图片缓存

3. **加载速度慢**
   - 检查网络状况
   - 优化图片尺寸
   - 使用CDN加速

### 调试技巧

```kotlin
// 启用调试日志
val debugConfig = ImageConfig.createDebugConfig()
ImageManager.init(context, debugConfig)

// 检查缓存健康状态
val isHealthy = ImageManager.checkCacheHealth(context)

// 获取详细的缓存报告
val report = ImageManager.getCacheUsageReport()
```

## 版本历史

- **v1.0.0** - 初始版本，基于 Coil3 3.2.0
  - 基础图片加载功能
  - 头像和占位符组件
  - 缓存管理
  - 图片变换效果

## 依赖

- Coil3 3.2.0
- Jetpack Compose
- OkHttp (可选，用于网络请求)

## 许可证

本项目采用 MIT 许可证。
