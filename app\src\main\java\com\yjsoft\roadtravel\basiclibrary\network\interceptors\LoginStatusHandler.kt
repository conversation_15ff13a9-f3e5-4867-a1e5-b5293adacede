package com.yjsoft.roadtravel.basiclibrary.network.interceptors

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import com.yjsoft.roadtravel.basiclibrary.auth.TokenManager
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreRepository
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.navigation.core.NavigationConstants
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 登录状态处理器
 * 负责处理业务 code 900000 的登录跳转逻辑
 */
class LoginStatusHandler(
    private val context: Context
) {
    
    companion object {
        private const val TAG = "LoginStatusHandler"
        private const val LOGIN_REQUIRED_CODE = 900000
        
        // 防重复跳转标志
        private val isNavigatingToLogin = AtomicBoolean(false)
    }
    
    // DataStore仓库，用于清除用户数据
    private val dataStoreRepository: DataStoreRepository by lazy {
        DataStoreRepository.getInstance()
    }
    
    // 主线程Handler，用于UI操作
    private val mainHandler = Handler(Looper.getMainLooper())
    
    /**
     * 处理登录状态检查
     * @param code 业务返回码
     * @param message 错误消息
     * @return true 如果处理了登录跳转，false 如果不需要处理
     */
    fun handleLoginStatus(code: Int, message: String): Boolean {
        if (code == LOGIN_REQUIRED_CODE) {
            LogManager.w(TAG, "检测到需要登录的业务码: $code, 消息: $message")
            handleLoginRequired()
            return true
        }
        return false
    }
    
    /**
     * 处理需要登录的情况
     */
    @OptIn(DelicateCoroutinesApi::class)
    private fun handleLoginRequired() {
        // 防止重复跳转
        if (!isNavigatingToLogin.compareAndSet(false, true)) {
            LogManager.d(TAG, "正在跳转到登录页面，跳过重复操作")
            return
        }
        
        try {
            LogManager.d(TAG, "开始处理登录跳转逻辑")
            
            // 在后台线程中清除认证信息
            GlobalScope.launch(Dispatchers.IO) {
                try {
                    clearAuthenticationData()
                    
                    // 在主线程中执行页面跳转
                    mainHandler.post {
                        navigateToLoginPage()
                    }
                } catch (e: Exception) {
                    LogManager.e(TAG, "清除认证数据失败", e)
                    // 即使清除失败，也要尝试跳转到登录页
                    mainHandler.post {
                        navigateToLoginPage()
                    }
                } finally {
                    // 重置跳转标志，允许下次跳转
                    mainHandler.postDelayed({
                        isNavigatingToLogin.set(false)
                    }, 2000) // 2秒后重置标志
                }
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "处理登录跳转异常", e)
            isNavigatingToLogin.set(false)
        }
    }
    
    /**
     * 清除认证数据
     */
    private suspend fun clearAuthenticationData() {
        try {
            LogManager.d(TAG, "开始清除认证数据")
            
            // 清除Token信息
            if (TokenManager.isInitialized()) {
                TokenManager.clearTokens()
                LogManager.d(TAG, "Token信息已清除")
            }
            
            // 清除用户登录状态
            dataStoreRepository.logout()
            LogManager.d(TAG, "用户登录状态已清除")
            
        } catch (e: Exception) {
            LogManager.e(TAG, "清除认证数据异常", e)
            throw e
        }
    }
    
    /**
     * 跳转到登录页面
     */
    private fun navigateToLoginPage() {
        try {
            LogManager.d(TAG, "开始跳转到登录页面")
            
            // 创建登录页面Intent
            val intent = Intent(context, NavigationConstants.ActivityClasses.LOGIN).apply {
                // 添加标志，确保登录页面在新任务中启动
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                
                // 添加来源信息
                putExtra("from", "network_interceptor")
                putExtra("reason", "login_required")
                putExtra("code", LOGIN_REQUIRED_CODE)
            }
            
            // 启动登录Activity
            context.startActivity(intent)
            
            LogManager.d(TAG, "登录页面跳转成功")
            
        } catch (e: Exception) {
            LogManager.e(TAG, "跳转到登录页面失败", e)
            
            // 如果直接跳转失败，尝试通过当前Activity跳转
            tryNavigateFromCurrentActivity()
        }
    }
    
    /**
     * 尝试从当前Activity跳转
     */
    private fun tryNavigateFromCurrentActivity() {
        try {
            // 获取当前Activity
            val currentActivity = getCurrentActivity()
            if (currentActivity != null) {
                LogManager.d(TAG, "通过当前Activity跳转到登录页面")
                
                val intent = Intent(currentActivity, NavigationConstants.ActivityClasses.LOGIN).apply {
                    putExtra("from", "network_interceptor")
                    putExtra("reason", "login_required")
                    putExtra("code", LOGIN_REQUIRED_CODE)
                }
                
                currentActivity.startActivity(intent)
                LogManager.d(TAG, "通过当前Activity跳转成功")
            } else {
                LogManager.w(TAG, "无法获取当前Activity，跳转失败")
            }
        } catch (e: Exception) {
            LogManager.e(TAG, "通过当前Activity跳转失败", e)
        }
    }
    
    /**
     * 获取当前Activity
     * 注意：这是一个简化实现，实际项目中可能需要更复杂的Activity管理
     */
    private fun getCurrentActivity(): Activity? {
        return try {
            // 这里可以通过Application的ActivityLifecycleCallbacks获取当前Activity
            // 或者使用其他方式获取当前Activity引用
            // 简化实现：返回null，依赖Application Context跳转
            null
        } catch (e: Exception) {
            LogManager.e(TAG, "获取当前Activity失败", e)
            null
        }
    }
    
    /**
     * 检查是否正在跳转到登录页面
     */
    fun isNavigatingToLogin(): Boolean {
        return isNavigatingToLogin.get()
    }
    
    /**
     * 重置跳转状态（用于测试或特殊情况）
     */
    fun resetNavigationState() {
        isNavigatingToLogin.set(false)
        LogManager.d(TAG, "登录跳转状态已重置")
    }
}
