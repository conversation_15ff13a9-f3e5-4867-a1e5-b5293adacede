package com.yjsoft.roadtravel.basiclibrary.payment.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import java.util.Locale
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType

/**
 * 支付方式选择对话框
 */
@Composable
fun PaymentDialog(
    amount: Double,
    paymentTypes: List<PaymentType>,
    onPaymentSelected: (PaymentType) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "选择支付方式",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 金额显示
                Text(
                    text = "支付金额：¥${String.format(Locale.getDefault(), "%.2f", amount)}",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // 支付方式列表
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(paymentTypes) { paymentType ->
                        PaymentMethodItem(
                            paymentType = paymentType,
                            onClick = { onPaymentSelected(paymentType) }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 取消按钮
                OutlinedButton(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = "取消",
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

/**
 * 支付方式选项组件
 */
@Composable
fun PaymentMethodItem(
    paymentType: PaymentType,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (enabled) {
                MaterialTheme.colorScheme.surfaceVariant
            } else {
                MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 支付方式图标
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(getPaymentTypeColor(paymentType).copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = paymentType.iconRes),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = getPaymentTypeColor(paymentType)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 支付方式名称
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = stringResource(id = paymentType.displayNameRes),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (enabled) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    }
                )
                
                Text(
                    text = getPaymentTypeDescription(paymentType),
                    fontSize = 12.sp,
                    color = if (enabled) {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
                    }
                )
            }
            
            // 状态指示器
            if (!enabled) {
                Text(
                    text = "不可用",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

/**
 * 获取支付方式对应的颜色
 */
private fun getPaymentTypeColor(paymentType: PaymentType): Color {
    return when (paymentType) {
        PaymentType.ALIPAY -> Color(0xFF1677FF)
        PaymentType.WECHAT_PAY -> Color(0xFF07C160)
        PaymentType.UNION_PAY -> Color(0xFFE21836)
    }
}

/**
 * 获取支付方式描述
 */
private fun getPaymentTypeDescription(paymentType: PaymentType): String {
    return when (paymentType) {
        PaymentType.ALIPAY -> "安全快捷的移动支付"
        PaymentType.WECHAT_PAY -> "微信安全支付"
        PaymentType.UNION_PAY -> "银联云闪付"
    }
}

@Preview(showBackground = true)
@Composable
private fun PaymentDialogPreview() {
    MaterialTheme {
        PaymentDialog(
            amount = 99.99,
            paymentTypes = PaymentType.values().toList(),
            onPaymentSelected = {},
            onDismiss = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PaymentMethodItemPreview() {
    MaterialTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            PaymentMethodItem(
                paymentType = PaymentType.ALIPAY,
                onClick = {}
            )
            
            PaymentMethodItem(
                paymentType = PaymentType.WECHAT_PAY,
                onClick = {}
            )
            
            PaymentMethodItem(
                paymentType = PaymentType.UNION_PAY,
                onClick = {},
                enabled = false
            )
        }
    }
}
