package com.yjsoft.roadtravel.basiclibrary.mvvm.examples

import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseActivity
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.UiStateHandler
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.collectAsUiState
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import dagger.hilt.android.AndroidEntryPoint

/**
 * MVVM示例Activity
 * 
 * 功能：
 * - 演示BaseActivity的使用
 * - 展示MVVM架构的完整实现
 * - 演示Compose与MVVM的集成
 * - 展示事件处理和状态管理
 * 
 * 特性：
 * - 用户列表展示
 * - 搜索功能
 * - 计数器演示
 * - 加载状态处理
 * - 错误处理
 * - 事件响应
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@AndroidEntryPoint
class ExampleActivity : BaseActivity() {

    private val viewModel: ExampleViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置Compose内容
        setupContent {
            ExampleScreen(viewModel = viewModel)
        }
        
        // 观察ViewModel事件
        observeEvents(
            uiEvents = viewModel.uiEvents,
            navigationEvents = viewModel.navigationEvents
        )
        
        // 初始化数据
        viewModel.initialize()
    }
}

/**
 * 示例主屏幕
 */
@Composable
fun ExampleScreen(viewModel: ExampleViewModel) {
    // 收集状态
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val userListState by viewModel.userListState.collectAsUiState()
    val counter by viewModel.counter.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    
    // 搜索状态
    var searchQuery by remember { mutableStateOf("") }
    
    // 初始化时加载数据
    LaunchedEffect(Unit) {
        viewModel.loadUserProfile()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "MVVM架构示例",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        // 状态信息卡片
        StatusCard(
            uiState = uiState,
            counter = counter,
            isLoading = isLoading
        )
        
        // 计数器控制
        CounterControls(
            counter = counter,
            onIncrement = { viewModel.incrementCounter() },
            onReset = { viewModel.resetCounter() },
            onLongOperation = { viewModel.simulateLongOperation() }
        )
        
        // 搜索框
        SearchBox(
            query = searchQuery,
            onQueryChange = { 
                searchQuery = it
                viewModel.searchUsers(it)
            },
            onRefresh = { viewModel.refresh() }
        )
        
        // 用户列表
        UserListSection(
            userListState = userListState,
            onUserClick = { user -> viewModel.showUserDetail(user) },
            onUserDelete = { userId -> viewModel.deleteUser(userId) },
            onRetry = { viewModel.loadUserProfile() }
        )
    }
}

/**
 * 状态信息卡片
 */
@Composable
fun StatusCard(
    uiState: ExampleUiState,
    counter: Int,
    isLoading: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "当前状态",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text("初始化状态: ${if (uiState.isInitialized) "已完成" else "未完成"}")
            Text("计数器值: $counter")
            Text("加载状态: ${if (isLoading) "加载中" else "空闲"}")
            
            if (uiState.lastAction.isNotEmpty()) {
                Text("最后操作: ${uiState.lastAction}")
            }
            
            if (uiState.lastRefreshTime > 0) {
                Text("最后刷新: ${java.text.SimpleDateFormat("HH:mm:ss").format(uiState.lastRefreshTime)}")
            }
        }
    }
}

/**
 * 计数器控制组件
 */
@Composable
fun CounterControls(
    counter: Int,
    onIncrement: () -> Unit,
    onReset: () -> Unit,
    onLongOperation: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "计数器控制",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onIncrement,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("增加 ($counter)")
                }
                
                OutlinedButton(
                    onClick = onReset,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("重置")
                }
            }
            
            Button(
                onClick = onLongOperation,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("模拟长时间操作")
            }
        }
    }
}

/**
 * 搜索框组件
 */
@Composable
fun SearchBox(
    query: String,
    onQueryChange: (String) -> Unit,
    onRefresh: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "用户搜索",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = query,
                    onValueChange = onQueryChange,
                    label = { Text("搜索用户") },
                    placeholder = { Text("输入姓名或邮箱") },
                    modifier = Modifier.weight(1f)
                )
                
                Button(onClick = onRefresh) {
                    Text("刷新")
                }
            }
        }
    }
}

/**
 * 用户列表部分
 */
@Composable
fun UserListSection(
    userListState: UiState<List<ExampleUser>>,
    onUserClick: (ExampleUser) -> Unit,
    onUserDelete: (String) -> Unit,
    onRetry: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "用户列表",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            UiStateHandler(
                uiState = userListState,
                onRetry = onRetry,
                loadingContent = {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            CircularProgressIndicator()
                            Text("加载用户列表中...")
                        }
                    }
                },
                emptyContent = { message ->
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(message)
                    }
                }
            ) { users ->
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(users) { user ->
                        UserItem(
                            user = user,
                            onClick = { onUserClick(user) },
                            onDelete = { onUserDelete(user.id) }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 用户项组件
 */
@Composable
fun UserItem(
    user: ExampleUser,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = user.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = user.email,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            OutlinedButton(
                onClick = onDelete
            ) {
                Text("删除")
            }
        }
    }
}
