package com.yjsoft.roadtravel.basiclibrary.payment.demo

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Payment
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentResult
import com.yjsoft.roadtravel.basiclibrary.payment.models.PaymentType
import com.yjsoft.roadtravel.basiclibrary.payment.state.PaymentViewModel
import com.yjsoft.roadtravel.basiclibrary.payment.test.PaymentStrategyTestResult
import com.yjsoft.roadtravel.basiclibrary.payment.test.PaymentTestCallback
import com.yjsoft.roadtravel.basiclibrary.payment.test.PaymentTestHelper
import com.yjsoft.roadtravel.basiclibrary.payment.ui.components.PaymentButton
import com.yjsoft.roadtravel.basiclibrary.payment.ui.components.SinglePaymentButton
import com.yjsoft.roadtravel.basiclibrary.payment.ui.screens.OrderInfo
import com.yjsoft.roadtravel.basiclibrary.payment.ui.screens.PaymentScreen

/** 支付框架演示页面 展示支付框架的各种使用方式和功能 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PaymentDemoScreen(
    modifier: Modifier = Modifier,
    onNavigateBack: () -> Unit = {},
    viewModel: PaymentViewModel = viewModel()
) {
    val context = LocalContext.current
    var showPaymentScreen by remember { mutableStateOf(false) }
    var testResults by remember { mutableStateOf<List<String>>(emptyList()) }
    var isTestRunning by remember { mutableStateOf(false) }

    // 初始化支付测试环境
    LaunchedEffect(Unit) {
        try {
            PaymentTestHelper.initTestEnvironment(context)
            viewModel.init(context)
        } catch (e: Exception) {
            LogManager.e("支付演示页面初始化失败", e)
        }
    }

    if (showPaymentScreen) {
        PaymentScreen(
            orderInfo =
                OrderInfo(
                    orderId = "DEMO_ORDER_${System.currentTimeMillis()}",
                    title = "演示商品",
                    description = "这是一个支付框架演示商品",
                    amount = 0.01 // 演示金额1分钱
                ),
            onNavigateBack = { showPaymentScreen = false },
            onPaymentComplete = { result ->
                showPaymentScreen = false
                val message =
                    when (result) {
                        is PaymentResult.Success -> "支付成功: ${result.transactionId}"
                        is PaymentResult.Cancel -> "支付取消: ${result.reason ?: "用户取消"}"
                        is PaymentResult.Error -> "支付失败: ${result.errorMessage}"
                        is PaymentResult.Processing -> "支付处理中: ${result.message}"
                    }
                testResults = testResults + message
            }
        )
    } else {
        Scaffold() { paddingValues ->
            Column(
                modifier =
                    modifier
                        .fillMaxSize()
                        .padding(
                            horizontal = paddingValues.calculateStartPadding(
                                LayoutDirection.Ltr
                            )
                        )
                        .verticalScroll(rememberScrollState())
                        .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 演示说明
                DemoIntroCard()

                // UI组件演示
                UIComponentsDemo(onShowPaymentScreen = { showPaymentScreen = true })

                // 测试功能演示
                TestFunctionsDemo(
                    isTestRunning = isTestRunning,
                    onRunTest = { testType ->
                        isTestRunning = true
                        runPaymentTest(context, testType) { result ->
                            testResults = testResults + result
                            isTestRunning = false
                        }
                    }
                )

                // 测试结果显示
                TestResultsCard(testResults = testResults) { testResults = emptyList() }
            }
        }
    }
}

/** 演示说明卡片 */
@Composable
private fun DemoIntroCard() {
    Card(modifier = Modifier.fillMaxWidth(), shape = RoundedCornerShape(12.dp)) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    imageVector = Icons.Default.Payment,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(text = "支付框架演示", fontSize = 18.sp, fontWeight = FontWeight.Bold)
            }

            Text(
                text = "本页面演示了支付框架的各种功能和使用方式，包括UI组件、测试功能等。",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = "注意：演示使用测试金额（0.01元），请勿用于实际交易。",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

/** UI组件演示 */
@Composable
private fun UIComponentsDemo(onShowPaymentScreen: () -> Unit) {
    Card(modifier = Modifier.fillMaxWidth(), shape = RoundedCornerShape(12.dp)) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(text = "UI组件演示", fontSize = 16.sp, fontWeight = FontWeight.Bold)

            // 统一支付按钮
            PaymentButton(
                amount = 0.01,
                paymentTypes = listOf(PaymentType.ALIPAY, PaymentType.WECHAT_PAY),
                orderId = "DEMO_${System.currentTimeMillis()}",
                title = "演示商品",
                onPaymentResult = { /* 由ViewModel处理 */ },
                buttonText = "统一支付按钮演示"
            )

            // 单一支付方式按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                SinglePaymentButton(
                    paymentType = PaymentType.ALIPAY,
                    amount = 0.01,
                    title = "支付宝演示",
                    onPaymentResult = { /* 由ViewModel处理 */ },
                    modifier = Modifier.weight(1f)
                )

                SinglePaymentButton(
                    paymentType = PaymentType.WECHAT_PAY,
                    amount = 0.01,
                    title = "微信支付演示",
                    onPaymentResult = { /* 由ViewModel处理 */ },
                    modifier = Modifier.weight(1f)
                )
            }

            // 完整支付页面
            Button(onClick = onShowPaymentScreen, modifier = Modifier.fillMaxWidth()) {
                Text("完整支付页面演示")
            }
        }
    }
}

/** 测试功能演示 */
@Composable
private fun TestFunctionsDemo(isTestRunning: Boolean, onRunTest: (String) -> Unit) {
    Card(modifier = Modifier.fillMaxWidth(), shape = RoundedCornerShape(12.dp)) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(text = "测试功能演示", fontSize = 16.sp, fontWeight = FontWeight.Bold)

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = { onRunTest("availability") },
                    enabled = !isTestRunning,
                    modifier = Modifier.weight(1f)
                ) { Text("可用性测试") }

                Button(
                    onClick = { onRunTest("strategy") },
                    enabled = !isTestRunning,
                    modifier = Modifier.weight(1f)
                ) { Text("策略测试") }
            }

            Button(
                onClick = { onRunTest("alipay") },
                enabled = !isTestRunning,
                modifier = Modifier.fillMaxWidth()
            ) {
                if (isTestRunning) {
                    CircularProgressIndicator(modifier = Modifier.size(16.dp), strokeWidth = 2.dp)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("测试中...")
                } else {
                    Text("支付宝支付测试")
                }
            }
        }
    }
}

/** 测试结果卡片 */
@Composable
private fun TestResultsCard(testResults: List<String>, onClear: () -> Unit) {
    if (testResults.isNotEmpty()) {
        Card(modifier = Modifier.fillMaxWidth(), shape = RoundedCornerShape(12.dp)) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(text = "测试结果", fontSize = 16.sp, fontWeight = FontWeight.Bold)

                    TextButton(onClick = onClear) { Text("清除") }
                }

                testResults.forEach { result ->
                    Text(
                        text = "• $result",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/** 运行支付测试 */
private fun runPaymentTest(
    context: android.content.Context,
    testType: String,
    onResult: (String) -> Unit
) {
    val callback =
        object : PaymentTestCallback {
            override fun onTestStart(message: String) {
                onResult("开始: $message")
            }

            override fun onTestSuccess(message: String, result: PaymentResult?) {
                onResult("成功: $message")
            }

            override fun onTestError(message: String, result: PaymentResult?) {
                onResult("失败: $message")
            }

            override fun onTestException(message: String, exception: Exception?) {
                onResult("异常: $message")
            }

            override fun onAvailabilityTestComplete(results: Map<PaymentType, Boolean>) {
                results.forEach { (type, available) ->
                    onResult("${type.name}: ${if (available) "可用" else "不可用"}")
                }
            }

            override fun onStrategyTestComplete(result: PaymentStrategyTestResult) {
                onResult(
                    "${result.paymentType.name}: 配置=${result.isConfigured}, 可用=${result.isAvailable}, 版本=${result.sdkVersion}"
                )
            }
        }

    when (testType) {
        "availability" -> PaymentTestHelper.testPaymentAvailability(context, callback)
        "strategy" -> {
            PaymentType.values().forEach { type ->
                PaymentTestHelper.testPaymentStrategy(context, type, callback)
            }
        }

        "alipay" -> PaymentTestHelper.testAlipayPayment(context, callback)
    }
}

@Preview(showBackground = true)
@Composable
private fun PaymentDemoScreenPreview() {
    MaterialTheme { PaymentDemoScreen() }
}
