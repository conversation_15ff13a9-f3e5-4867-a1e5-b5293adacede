package com.yjsoft.roadtravel.basiclibrary.permission.ui

import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.yjsoft.roadtravel.basiclibrary.permission.config.PermissionMessages
import com.yjsoft.roadtravel.basiclibrary.permission.utils.PermissionLogger

/**
 * 权限设置引导对话框
 * 当权限被永久拒绝时，引导用户到设置页面手动开启权限
 */
@Composable
fun PermissionSettingsDialog(
    permissions: List<String>,
    title: String = "权限设置",
    message: String? = null,
    icon: ImageVector = Icons.Default.Settings,
    onNavigateToSettings: () -> Unit,
    onDismiss: () -> Unit,
    onDismissRequest: () -> Unit = onDismiss
) {
    val context = LocalContext.current
    
    // 记录对话框显示
    LaunchedEffect(permissions) {
        PermissionLogger.logSettingsDialogShown(permissions)
    }
    
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 图标
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 标题
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 消息内容
                val displayMessage = message ?: generateSettingsMessage(permissions)
                Text(
                    text = displayMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    lineHeight = MaterialTheme.typography.bodyMedium.lineHeight
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // 按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = {
                            PermissionLogger.logSettingsDialogResult(permissions, false)
                            onDismiss()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("取消")
                    }
                    
                    // 前往设置按钮
                    Button(
                        onClick = {
                            PermissionLogger.logSettingsDialogResult(permissions, true)
                            
                            // 跳转到应用设置页面
                            try {
                                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                                    data = Uri.fromParts("package", context.packageName, null)
                                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                }
                                context.startActivity(intent)
                            } catch (e: Exception) {
                                PermissionLogger.e("SettingsDialog", "无法打开应用设置页面", e)
                                // 备选方案：打开系统设置
                                try {
                                    val intent = Intent(Settings.ACTION_SETTINGS).apply {
                                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                    }
                                    context.startActivity(intent)
                                } catch (e2: Exception) {
                                    PermissionLogger.e("SettingsDialog", "无法打开系统设置", e2)
                                }
                            }
                            
                            onNavigateToSettings()
                        },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("前往设置")
                    }
                }
            }
        }
    }
}

/**
 * 单个权限设置对话框
 */
@Composable
fun SinglePermissionSettingsDialog(
    permission: String,
    title: String? = null,
    message: String? = null,
    icon: ImageVector = Icons.Default.Settings,
    onNavigateToSettings: () -> Unit,
    onDismiss: () -> Unit,
    onDismissRequest: () -> Unit = onDismiss
) {
    val permissionMessage = PermissionMessages.getMessage(permission)
    
    PermissionSettingsDialog(
        permissions = listOf(permission),
        title = title ?: permissionMessage.settingsMessage,
        message = message ?: permissionMessage.settingsMessage,
        icon = icon,
        onNavigateToSettings = onNavigateToSettings,
        onDismiss = onDismiss,
        onDismissRequest = onDismissRequest
    )
}

/**
 * 权限组设置对话框
 */
@Composable
fun PermissionGroupSettingsDialog(
    groupName: String,
    permissions: List<String>,
    title: String? = null,
    message: String? = null,
    icon: ImageVector = Icons.Default.Settings,
    onNavigateToSettings: () -> Unit,
    onDismiss: () -> Unit,
    onDismissRequest: () -> Unit = onDismiss
) {
    val groupMessage = PermissionMessages.getGroupMessage(groupName)
    
    PermissionSettingsDialog(
        permissions = permissions,
        title = title ?: "开启${groupMessage.title}",
        message = message ?: groupMessage.settingsMessage,
        icon = icon,
        onNavigateToSettings = onNavigateToSettings,
        onDismiss = onDismiss,
        onDismissRequest = onDismissRequest
    )
}

/**
 * 生成设置引导消息
 */
private fun generateSettingsMessage(permissions: List<String>): String {
    return when {
        permissions.size == 1 -> {
            val permission = permissions.first()
            PermissionMessages.getMessage(permission).settingsMessage
        }
        permissions.size <= 3 -> {
            val permissionNames = permissions.map { permission ->
                PermissionMessages.getMessage(permission).title
            }
            "请在设置中手动开启以下权限：\n\n${permissionNames.joinToString("、")}\n\n这些权限对应用的正常运行非常重要。"
        }
        else -> {
            "应用需要您在设置中手动开启 ${permissions.size} 个权限。请在应用权限设置页面中开启所需的权限。"
        }
    }
}

/**
 * 权限设置对话框状态
 */
@Stable
class PermissionSettingsDialogState {
    private var _isVisible by mutableStateOf(false)
    private var _permissions by mutableStateOf<List<String>>(emptyList())
    private var _title by mutableStateOf("")
    private var _message by mutableStateOf("")
    private var _onNavigateToSettings by mutableStateOf<(() -> Unit)?>(null)
    private var _onDismiss by mutableStateOf<(() -> Unit)?>(null)
    
    val isVisible: Boolean get() = _isVisible
    val permissions: List<String> get() = _permissions
    val title: String get() = _title
    val message: String get() = _message
    
    /**
     * 显示对话框
     */
    fun show(
        permissions: List<String>,
        title: String = "权限设置",
        message: String? = null,
        onNavigateToSettings: () -> Unit,
        onDismiss: () -> Unit
    ) {
        _permissions = permissions
        _title = title
        _message = message ?: generateSettingsMessage(permissions)
        _onNavigateToSettings = onNavigateToSettings
        _onDismiss = onDismiss
        _isVisible = true
    }
    
    /**
     * 隐藏对话框
     */
    fun hide() {
        _isVisible = false
    }
    
    /**
     * 前往设置操作
     */
    fun navigateToSettings() {
        _onNavigateToSettings?.invoke()
        hide()
    }
    
    /**
     * 取消操作
     */
    fun dismiss() {
        _onDismiss?.invoke()
        hide()
    }
}

/**
 * 记住权限设置对话框状态
 */
@Composable
fun rememberPermissionSettingsDialogState(): PermissionSettingsDialogState {
    return remember { PermissionSettingsDialogState() }
}

/**
 * 权限设置对话框组件（使用状态管理）
 */
@Composable
fun PermissionSettingsDialogWithState(
    state: PermissionSettingsDialogState,
    icon: ImageVector = Icons.Default.Settings
) {
    if (state.isVisible) {
        PermissionSettingsDialog(
            permissions = state.permissions,
            title = state.title,
            message = state.message,
            icon = icon,
            onNavigateToSettings = { state.navigateToSettings() },
            onDismiss = { state.dismiss() }
        )
    }
}
