package com.yjsoft.roadtravel.basiclibrary.di

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * Hilt诊断工具
 * 
 * 功能：
 * - 检查Hilt框架是否正确配置
 * - 诊断常见的Hilt问题
 * - 提供调试信息
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
object HiltDiagnostics {
    
    private const val TAG = "HiltDiagnostics"
    
    /**
     * 运行完整的Hilt诊断
     */
    fun runDiagnostics(context: Context) {
        LogManager.w("🔍 [%s] ========== Hilt 诊断开始 ==========", TAG)

        checkHiltAnnotations()
        checkHiltDependencies()
        checkKaptConfiguration()
        checkGeneratedClasses(context)
        checkModules()

        LogManager.w("🔍 [%s] ========== Hilt 诊断结束 ==========", TAG)
    }
    
    /**
     * 检查Hilt注解
     */
    private fun checkHiltAnnotations() {
        LogManager.w("🔍 [%s] 检查Hilt注解...", TAG)

        try {
            // 检查@HiltAndroidApp注解是否存在
            val appClass = Class.forName("com.yjsoft.roadtravel.application.RoadTravelApplication")
            val hiltAppAnnotation = appClass.getAnnotation(dagger.hilt.android.HiltAndroidApp::class.java)

            if (hiltAppAnnotation != null) {
                LogManager.w("✅ [%s] @HiltAndroidApp 注解已正确添加", TAG)
            } else {
                LogManager.e("❌ [%s] @HiltAndroidApp 注解未找到", TAG)
            }

        } catch (e: ClassNotFoundException) {
            LogManager.e(e, "❌ [%s] Application类未找到", TAG)
        } catch (e: Exception) {
            LogManager.e(e, "❌ [%s] 检查@HiltAndroidApp注解时出错", TAG)
        }

        try {
            // 检查@AndroidEntryPoint注解是否存在
            val activityClass = Class.forName("com.yjsoft.roadtravel.ui.activities.main.MainActivity")
            val entryPointAnnotation = activityClass.getAnnotation(dagger.hilt.android.AndroidEntryPoint::class.java)

            if (entryPointAnnotation != null) {
                LogManager.w("✅ [%s] @AndroidEntryPoint 注解已正确添加", TAG)
            } else {
                LogManager.w("⚠️ [%s] @AndroidEntryPoint 注解未找到（如果不使用依赖注入可忽略）", TAG)
            }

        } catch (e: ClassNotFoundException) {
            LogManager.w("⚠️ [%s] MainActivity类未找到", TAG)
        } catch (e: Exception) {
            LogManager.w(e, "⚠️ [%s] 检查@AndroidEntryPoint注解时出错", TAG)
        }
    }
    
    /**
     * 检查Hilt依赖
     */
    private fun checkHiltDependencies() {
        LogManager.w("🔍 [%s] 检查Hilt依赖...", TAG)

        val requiredClasses = listOf(
            "dagger.hilt.android.HiltAndroidApp",
            "dagger.hilt.android.AndroidEntryPoint",
            "dagger.Module",
            "dagger.Provides",
            "javax.inject.Inject",
            "javax.inject.Singleton"
        )

        requiredClasses.forEach { className ->
            try {
                Class.forName(className)
                LogManager.w("✅ [%s] %s 可用", TAG, className)
            } catch (e: ClassNotFoundException) {
                LogManager.e("❌ [%s] %s 不可用 - 检查依赖配置", TAG, className)
            }
        }
    }
    
    /**
     * 检查KAPT配置
     */
    private fun checkKaptConfiguration() {
        LogManager.d("$TAG 检查KAPT配置...")
        
        // 这里主要是提醒检查项
        LogManager.d("$TAG 请确认以下KAPT配置:")
        LogManager.d("$TAG 1. build.gradle.kts中是否添加了 kotlin(\"kapt\")")
        LogManager.d("$TAG 2. 是否添加了 kapt(libs.hilt.compiler)")
        LogManager.d("$TAG 3. 是否配置了 kapt { correctErrorTypes = true }")
    }
    
    /**
     * 检查生成的类
     */
    private fun checkGeneratedClasses(context: Context) {
        LogManager.d("$TAG 检查Hilt生成的类...")
        
        val generatedClasses = listOf(
            "com.yjsoft.roadtravel.application.RoadTravelApplication_HiltComponents",
            "dagger.hilt.internal.aggregatedroot.codegen._com_yjsoft_roadtravel_application_RoadTravelApplication",
            "com.yjsoft.roadtravel.MainActivity_GeneratedInjector"
        )
        
        generatedClasses.forEach { className ->
            try {
                Class.forName(className)
                LogManager.i("$TAG ✅ 生成类 $className 存在")
            } catch (e: ClassNotFoundException) {
                LogManager.w("$TAG ⚠️ 生成类 $className 不存在（可能需要重新编译）")
            }
        }
    }
    
    /**
     * 检查Hilt模块
     */
    private fun checkModules() {
        LogManager.d("$TAG 检查Hilt模块...")
        
        val moduleClasses = listOf(
            "com.yjsoft.roadtravel.basiclibrary.di.modules.LoggerModule",
            "com.yjsoft.roadtravel.basiclibrary.di.modules.NetworkModule",
            "com.yjsoft.roadtravel.basiclibrary.di.modules.DataStoreModule"
        )
        
        moduleClasses.forEach { className ->
            try {
                val moduleClass = Class.forName(className)
                val moduleAnnotation = moduleClass.getAnnotation(dagger.Module::class.java)
                val installInAnnotation = moduleClass.getAnnotation(dagger.hilt.InstallIn::class.java)
                
                if (moduleAnnotation != null && installInAnnotation != null) {
                    LogManager.i("$TAG ✅ 模块 $className 配置正确")
                } else {
                    LogManager.w("$TAG ⚠️ 模块 $className 注解不完整")
                }
                
            } catch (e: ClassNotFoundException) {
                LogManager.e("$TAG ❌ 模块 $className 未找到")
            } catch (e: Exception) {
                LogManager.e("$TAG ❌ 检查模块 $className 时出错", e)
            }
        }
    }
    
    /**
     * 获取诊断摘要
     */
    fun getDiagnosticSummary(): String {
        return buildString {
            append("Hilt诊断摘要:\n")
            append("1. 检查@HiltAndroidApp注解是否添加到Application类\n")
            append("2. 检查@AndroidEntryPoint注解是否添加到Activity类\n")
            append("3. 检查build.gradle.kts中的Hilt依赖配置\n")
            append("4. 检查kapt配置是否正确\n")
            append("5. 检查Hilt模块是否正确配置\n")
            append("6. 尝试Clean Project并重新编译\n")
            append("7. 检查proguard规则是否正确\n")
        }
    }
    
    /**
     * 快速检查Hilt是否可用
     */
    fun isHiltAvailable(): Boolean {
        return try {
            Class.forName("dagger.hilt.android.HiltAndroidApp")
            Class.forName("dagger.hilt.android.AndroidEntryPoint")
            true
        } catch (e: ClassNotFoundException) {
            false
        }
    }
}
