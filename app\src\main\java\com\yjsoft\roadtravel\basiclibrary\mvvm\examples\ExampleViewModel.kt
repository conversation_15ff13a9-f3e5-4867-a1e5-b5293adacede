package com.yjsoft.roadtravel.basiclibrary.mvvm.examples

import androidx.lifecycle.viewModelScope
import com.yjsoft.roadtravel.basiclibrary.mvvm.base.BaseViewModel
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.handleResourceFlow
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.launchWithLoading
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.launchWithUiState
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.logOperation
import com.yjsoft.roadtravel.basiclibrary.mvvm.extensions.safeLaunch
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * MVVM示例ViewModel
 * 
 * 功能：
 * - 演示BaseViewModel的使用
 * - 展示状态管理最佳实践
 * - 演示事件处理
 * - 展示扩展函数的使用
 * 
 * 使用方式：
 * ```kotlin
 * @AndroidEntryPoint
 * class ExampleActivity : BaseActivity() {
 *     @Inject
 *     lateinit var viewModel: ExampleViewModel
 *     
 *     override fun onCreate(savedInstanceState: Bundle?) {
 *         super.onCreate(savedInstanceState)
 *         setupContent {
 *             ExampleScreen(viewModel = viewModel)
 *         }
 *         observeEvents(viewModel.uiEvents, viewModel.navigationEvents)
 *     }
 * }
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@HiltViewModel
class ExampleViewModel @Inject constructor(
    private val exampleRepository: ExampleRepository
) : BaseViewModel<ExampleUiState>() {
    
    companion object {
        private const val TAG = "ExampleViewModel"
    }
    
    // ========== 额外状态 ==========
    
    /**
     * 计数器状态
     */
    private val _counter = MutableStateFlow(0)
    val counter: StateFlow<Int> = _counter.asStateFlow()
    
    /**
     * 用户列表状态
     */
    private val _userListState = MutableStateFlow<UiState<List<ExampleUser>>>(UiState.Idle)
    val userListState: StateFlow<UiState<List<ExampleUser>>> = _userListState.asStateFlow()
    
    // ========== 基类实现 ==========
    
    override fun createInitialState(): ExampleUiState {
        return ExampleUiState()
    }
    
    // ========== 业务方法 ==========
    
    /**
     * 初始化数据
     */
    fun initialize() {
        logOperation("初始化", "开始加载初始数据")
        
        // 使用扩展函数启动协程
        safeLaunch(
            onError = { exception ->
                showError("初始化失败: ${exception.message}")
            }
        ) {
            updateState { it.copy(isInitialized = true) }
            loadUserProfile()
        }
    }
    
    /**
     * 加载用户资料
     */
    fun loadUserProfile() {
        // 使用扩展函数处理UiState
        launchWithUiState(_userListState, "加载用户资料中...") {
            val users = exampleRepository.getUserList()
            users
        }
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        logOperation("刷新", "开始刷新数据")
        
        // 使用扩展函数处理Resource Flow
        handleResourceFlow(
            resourceFlow = exampleRepository.getUserListFlow(),
            uiState = _userListState,
            onSuccess = { users ->
                showToast("刷新成功，获取到${users.size}个用户")
                updateState { it.copy(lastRefreshTime = System.currentTimeMillis()) }
            },
            onError = { _, message, _ ->
                showError("刷新失败: $message")
            }
        )
    }
    
    /**
     * 增加计数器
     */
    fun incrementCounter() {
        _counter.value += 1
        
        // 演示状态更新
        updateState { 
            it.copy(
                counterValue = _counter.value,
                lastAction = "增加计数器到${_counter.value}"
            )
        }
        
        // 演示事件发送
        if (_counter.value % 5 == 0) {
            showSnackbar("计数器达到${_counter.value}！", "重置") 
        }
    }
    
    /**
     * 重置计数器
     */
    fun resetCounter() {
        _counter.value = 0
        updateState { 
            it.copy(
                counterValue = 0,
                lastAction = "重置计数器"
            )
        }
        showToast("计数器已重置")
    }
    
    /**
     * 模拟长时间操作
     */
    fun simulateLongOperation() {
        launchWithLoading(
            loadingState = _isLoading,
            onError = { exception ->
                showError("操作失败: ${exception.message}")
            }
        ) {
            updateState { it.copy(lastAction = "开始长时间操作") }
            
            // 模拟耗时操作
            repeat(5) { index ->
                delay(1000)
                updateState { 
                    it.copy(lastAction = "长时间操作进度: ${index + 1}/5")
                }
            }
            
            updateState { it.copy(lastAction = "长时间操作完成") }
            showToast("长时间操作完成！")
        }
    }
    
    /**
     * 导航到详情页
     */
    fun navigateToDetail(userId: String) {
        logOperation("导航", "跳转到用户详情: $userId")
        navigateTo("user_detail/$userId")
    }
    
    /**
     * 显示用户详情对话框
     */
    fun showUserDetail(user: ExampleUser) {
        showDialog(
            title = "用户详情",
            message = "姓名: ${user.name}\n邮箱: ${user.email}\nID: ${user.id}"
        )
    }
    
    /**
     * 删除用户
     */
    fun deleteUser(userId: String) {
        safeLaunch {
            try {
                setLoading(true)
                exampleRepository.deleteUser(userId)
                
                // 更新本地状态
                val currentUsers = (_userListState.value as? UiState.Success)?.data ?: return@safeLaunch
                val updatedUsers = currentUsers.filter { it.id != userId }
                _userListState.value = UiState.Success(updatedUsers)
                
                showToast("用户删除成功")
                updateState { it.copy(lastAction = "删除用户: $userId") }
                
            } catch (e: Exception) {
                showError("删除用户失败: ${e.message}")
            } finally {
                setLoading(false)
            }
        }
    }
    
    /**
     * 搜索用户
     */
    fun searchUsers(query: String) {
        if (query.isBlank()) {
            loadUserProfile()
            return
        }
        
        launchWithUiState(_userListState, "搜索中...") {
            exampleRepository.searchUsers(query)
        }
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 获取当前用户数量
     */
    fun getCurrentUserCount(): Int {
        return (_userListState.value as? UiState.Success)?.data?.size ?: 0
    }
    
    /**
     * 检查是否有用户数据
     */
    fun hasUsers(): Boolean {
        return getCurrentUserCount() > 0
    }
}

/**
 * 示例UI状态
 */
data class ExampleUiState(
    val isInitialized: Boolean = false,
    val counterValue: Int = 0,
    val lastAction: String = "",
    val lastRefreshTime: Long = 0L
)

/**
 * 示例用户数据类
 */
data class ExampleUser(
    val id: String,
    val name: String,
    val email: String,
    val avatar: String = ""
)
