package com.yjsoft.roadtravel.basiclibrary.mvvm.base

import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.Event
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.NavigationEvent
import com.yjsoft.roadtravel.basiclibrary.mvvm.state.UiEvent
import com.yjsoft.roadtravel.ui.theme.RoadTravelTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch

/**
 * Activity基类
 * 
 * 功能：
 * - 集成Hilt依赖注入
 * - 统一的UI事件处理
 * - 统一的导航事件处理
 * - 生命周期感知
 * - Compose集成
 * 
 * 设计原则：
 * - 最小化样板代码
 * - 统一的事件处理机制
 * - 生命周期安全
 * - 主题统一
 * 
 * 使用方式：
 * ```kotlin
 * @AndroidEntryPoint
 * class MainActivity : BaseActivity() {
 * 
 *     @Inject
 *     lateinit var viewModel: MainViewModel
 * 
 *     override fun onCreate(savedInstanceState: Bundle?) {
 *         super.onCreate(savedInstanceState)
 *         
 *         setupContent {
 *             MainScreen(viewModel = viewModel)
 *         }
 *         
 *         observeEvents(
 *             uiEvents = viewModel.uiEvents,
 *             navigationEvents = viewModel.navigationEvents
 *         )
 *     }
 * }
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@AndroidEntryPoint
abstract class BaseActivity : ComponentActivity() {
    
    companion object {
        private const val TAG = "BaseActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogManager.d("[%s] Activity创建: %s", TAG, this::class.simpleName)
        
        // 初始化Activity
        initializeActivity()
    }
    
    override fun onStart() {
        super.onStart()
        LogManager.d("[%s] Activity启动: %s", TAG, this::class.simpleName)
    }
    
    override fun onResume() {
        super.onResume()
        LogManager.d("[%s] Activity恢复: %s", TAG, this::class.simpleName)
    }
    
    override fun onPause() {
        super.onPause()
        LogManager.d("[%s] Activity暂停: %s", TAG, this::class.simpleName)
    }
    
    override fun onStop() {
        super.onStop()
        LogManager.d("[%s] Activity停止: %s", TAG, this::class.simpleName)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        LogManager.d("[%s] Activity销毁: %s", TAG, this::class.simpleName)
    }
    
    // ========== 初始化方法 ==========
    
    /**
     * 初始化Activity
     * 子类可以重写此方法进行自定义初始化
     */
    protected open fun initializeActivity() {
        // 默认实现为空，子类可以重写
    }
    
    // ========== Compose集成 ==========
    
    /**
     * 设置Compose内容
     */
    protected fun setupContent(content: @Composable () -> Unit) {
        setContent {
            RoadTravelTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = Color.Transparent // 让Surface背景透明，显示GradientBackground
                ) {
                    content()
                }
            }
        }
    }
    
    // ========== 事件处理 ==========
    
    /**
     * 观察ViewModel事件
     */
    protected fun observeEvents(
        uiEvents: SharedFlow<Event<UiEvent>>? = null,
        navigationEvents: SharedFlow<Event<NavigationEvent>>? = null,
        customEvents: SharedFlow<Event<Any>>? = null
    ) {
        // 观察UI事件
        uiEvents?.let { events ->
            lifecycleScope.launch {
                repeatOnLifecycle(Lifecycle.State.STARTED) {
                    events.collect { event ->
                        event.getContentIfNotHandled()?.let { uiEvent ->
                            handleUiEvent(uiEvent)
                        }
                    }
                }
            }
        }
        
        // 观察导航事件
        navigationEvents?.let { events ->
            lifecycleScope.launch {
                repeatOnLifecycle(Lifecycle.State.STARTED) {
                    events.collect { event ->
                        event.getContentIfNotHandled()?.let { navigationEvent ->
                            handleNavigationEvent(navigationEvent)
                        }
                    }
                }
            }
        }
        
        // 观察自定义事件
        customEvents?.let { events ->
            lifecycleScope.launch {
                repeatOnLifecycle(Lifecycle.State.STARTED) {
                    events.collect { event ->
                        event.getContentIfNotHandled()?.let { customEvent ->
                            handleCustomEvent(customEvent)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 处理UI事件
     */
    open fun handleUiEvent(event: UiEvent) {
        when (event) {
            is UiEvent.ShowToast -> {
                showToast(event.message)
            }
            is UiEvent.ShowSnackbar -> {
                showSnackbar(event.message, event.actionLabel)
            }
            is UiEvent.ShowDialog -> {
                showDialog(event.title, event.message)
            }
            is UiEvent.HideKeyboard -> {
                hideKeyboard()
            }
            is UiEvent.ShowKeyboard -> {
                showKeyboard()
            }
        }
    }
    
    /**
     * 处理导航事件
     */
    open fun handleNavigationEvent(event: NavigationEvent) {
        when (event) {
            is NavigationEvent.Back -> {
                onBackPressedDispatcher.onBackPressed()
            }
            is NavigationEvent.ToRoute -> {
                navigateToRoute(event.route)
            }
            is NavigationEvent.ToActivity -> {
                navigateToActivity(event.className, event.params)
            }
        }
    }
    
    /**
     * 处理自定义事件
     * 子类可以重写此方法处理特定的自定义事件
     */
    open fun handleCustomEvent(event: Any) {
        LogManager.d("[%s] 收到自定义事件: %s", TAG, event::class.simpleName)
    }
    
    // ========== UI操作方法 ==========
    
    /**
     * 显示Toast
     */
    protected open fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示Snackbar
     */
    protected open fun showSnackbar(message: String, actionLabel: String? = null) {
        // 默认实现使用Toast，子类可以重写使用真正的Snackbar
        showToast(message)
    }
    
    /**
     * 显示对话框
     */
    protected open fun showDialog(title: String, message: String) {
        // 默认实现使用Toast，子类可以重写使用真正的Dialog
        showToast("$title: $message")
    }
    
    /**
     * 隐藏键盘
     */
    protected open fun hideKeyboard() {
        // 子类可以实现具体的隐藏键盘逻辑
    }
    
    /**
     * 显示键盘
     */
    protected open fun showKeyboard() {
        // 子类可以实现具体的显示键盘逻辑
    }
    
    // ========== 导航方法 ==========
    
    /**
     * 导航到指定路由
     * 子类需要实现具体的导航逻辑
     */
    protected open fun navigateToRoute(route: String) {
        LogManager.d("[%s] 导航到路由: %s", TAG, route)
        // 子类实现具体的导航逻辑
    }
    
    /**
     * 导航到指定Activity
     */
    protected open fun navigateToActivity(className: String, params: Map<String, Any> = emptyMap()) {
        LogManager.d("[%s] 导航到Activity: %s, 参数: %s", TAG, className, params)
        // 子类实现具体的Activity跳转逻辑
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 获取Activity名称
     */
    protected fun getActivityName(): String {
        return this::class.simpleName ?: "UnknownActivity"
    }
    
    /**
     * 检查Activity是否处于活跃状态
     */
    protected fun isActivityActive(): Boolean {
        return !isFinishing && !isDestroyed
    }
}

/**
 * Compose中使用的事件处理扩展
 */
@Composable
fun BaseActivity.HandleUiEvents(
    uiEvents: SharedFlow<Event<UiEvent>>
) {
    LaunchedEffect(uiEvents) {
        uiEvents.collect { event ->
            event.getContentIfNotHandled()?.let { uiEvent ->
                handleUiEvent(uiEvent)
            }
        }
    }
}

@Composable
fun BaseActivity.HandleNavigationEvents(
    navigationEvents: SharedFlow<Event<NavigationEvent>>
) {
    LaunchedEffect(navigationEvents) {
        navigationEvents.collect { event ->
            event.getContentIfNotHandled()?.let { navigationEvent ->
                handleNavigationEvent(navigationEvent)
            }
        }
    }
}
